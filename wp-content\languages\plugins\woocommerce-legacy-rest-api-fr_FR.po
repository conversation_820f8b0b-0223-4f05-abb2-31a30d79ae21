# Translation of Plugins - WooCommerce Legacy REST API - Stable (latest release) in French (France)
# This file is distributed under the same license as the Plugins - WooCommerce Legacy REST API - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-05-15 08:36:23+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: fr\n"
"Project-Id-Version: Plugins - WooCommerce Legacy REST API - Stable (latest release)\n"

#: includes/legacy/api/v3/class-wc-api-taxes.php:639
msgid "You do not have permission to read the tax classes count"
msgstr "Vous n’avez pas le droit de lire le nombre de classes de taxe"

#: includes/legacy/api/v3/class-wc-api-taxes.php:620
msgid "Could not delete the tax class"
msgstr "Impossible de supprimer la classe de taxe"

#: includes/legacy/api/v3/class-wc-api-taxes.php:612
msgid "You do not have permission to delete tax classes"
msgstr "Vous n’avez pas le droit de supprimer les classes de taxe"

#: includes/legacy/api/v3/class-wc-api-taxes.php:569
msgid "You do not have permission to create tax classes"
msgstr "Vous n’avez pas le droit de créer les classes de taxe"

#: includes/legacy/api/v3/class-wc-api-taxes.php:534
msgid "Standard rate"
msgstr "Taux standard"

#: includes/legacy/api/v3/class-wc-api-taxes.php:526
msgid "You do not have permission to read tax classes"
msgstr "Vous n’avez pas le droit de lire les classes de taxe"

#: includes/legacy/api/v3/class-wc-api-taxes.php:373
msgid "You do not have permission to read the taxes count"
msgstr "Vous n’avez pas le droit de lire le comptage des taxe"

#: includes/legacy/api/v3/class-wc-api-taxes.php:351
msgid "Could not delete the tax rate"
msgstr "Impossible de supprimer le taux de taxe"

#: includes/legacy/api/v3/class-wc-api-taxes.php:343
msgid "You do not have permission to delete tax rates"
msgstr "Vous n’avez pas le droit de supprimer les taux de taxe"

#: includes/legacy/api/v3/class-wc-api-taxes.php:259
msgid "You do not have permission to edit tax rates"
msgstr "Vous n’avez pas le droit de modifier les taux de taxe"

#: includes/legacy/api/v3/class-wc-api-taxes.php:189
msgid "You do not have permission to create tax rates"
msgstr "Vous n’avez pas le droit de créer des taux de taxe"

#: includes/legacy/api/v3/class-wc-api-taxes.php:135
msgid "A tax rate with the provided ID could not be found"
msgstr "Un taux de taxe avec l’ID fourni ne peut pas être trouvé"

#: includes/legacy/api/v3/class-wc-api-taxes.php:128
msgid "You do not have permission to read tax rate"
msgstr "Vous n’avez pas le droit de voir le taux de taxe"

#: includes/legacy/api/v3/class-wc-api-products.php:3294
msgid "You do not have permission to delete product shipping classes"
msgstr "Vous n’avez pas le droit de supprimer les classes de livraison produit"

#: includes/legacy/api/v3/class-wc-api-products.php:3269
msgid "Could not edit the shipping class"
msgstr "Impossible de modifier la classe de livraison"

#: includes/legacy/api/v3/class-wc-api-products.php:3257
msgid "You do not have permission to edit product shipping classes"
msgstr "Vous n’avez pas le droit de modifier les classes de livraison produit"

#: includes/legacy/api/v3/class-wc-api-products.php:3196
msgid "You do not have permission to create product shipping classes"
msgstr "Vous n’avez pas le droit de créer les classes de livraison produit"

#: includes/legacy/api/v3/class-wc-api-products.php:3158
msgid "A product shipping class with the provided ID could not be found"
msgstr "Une classe de livraison produit avec l’D fourni ne peut être trouvée"

#: includes/legacy/api/v3/class-wc-api-products.php:3147
msgid "Invalid product shipping class ID"
msgstr "ID de classe de livraison produit non valide"

#: includes/legacy/api/v3/class-wc-api-products.php:3117
#: includes/legacy/api/v3/class-wc-api-products.php:3152
msgid "You do not have permission to read product shipping classes"
msgstr "Vous n’avez pas le droit de lire les classes de livraison produit"

#: includes/legacy/api/v3/class-wc-api-products.php:2974
msgid "You do not have permission to delete product attribute terms"
msgstr "Vous n’avez pas le droit de supprimer les termes d’attribut de produit"

#: includes/legacy/api/v3/class-wc-api-products.php:2817
msgid "A product attribute term with the provided ID could not be found"
msgstr "Un terme d’attribut de produit avec l’ID fourni ne peut pas être trouvé"

#: includes/legacy/api/v3/class-wc-api-products.php:2752
#: includes/legacy/api/v3/class-wc-api-products.php:2805
msgid "You do not have permission to read product attribute terms"
msgstr "Vous n’avez pas le droit de voir les termes d’attribut de produit"

#: includes/legacy/api/v3/class-wc-api-products.php:1053
msgid "Could not delete the tag"
msgstr "Impossible de supprimer la marque"

#: includes/legacy/api/v3/class-wc-api-products.php:861
msgid "Could not delete the category"
msgstr "Impossible de supprimer la catégorie"

#: includes/legacy/api/v3/class-wc-api-products.php:855
msgid "You do not have permission to delete product category"
msgstr "Vous n’avez pas les droits nécessaires pour supprimer une catégorie de produit"

#: includes/legacy/api/v3/class-wc-api-products.php:822
msgid "Could not edit the category"
msgstr "Impossible de modifier la catégorie"

#: includes/legacy/api/v3/class-wc-api-products.php:792
msgid "You do not have permission to edit product categories"
msgstr "Vous n’avez pas le droit de modifier des catégories de produit"

#: includes/legacy/api/v3/class-wc-api-products.php:712
msgid "You do not have permission to create product categories"
msgstr "Vous n’avez pas le droit de créer des catégories de produit"

#: includes/legacy/api/v3/class-wc-api-authentication.php:114
msgid "Consumer Secret is invalid."
msgstr "La clé secrète client/cliente est non valide."

#: includes/legacy/api/v3/class-wc-api-authentication.php:111
msgid "WooCommerce API. Use a consumer key in the username field and a consumer secret in the password field."
msgstr "API WooCommerce. Utilisez une clé client/cliente dans le champ identifiant et un secret client dans le champ mot de passe."

#: includes/legacy/api/v2/class-wc-api-webhooks.php:449
#: includes/legacy/api/v3/class-wc-api-webhooks.php:449
msgid "Invalid webhook delivery."
msgstr "Crochet Web délivré non valide."

#: includes/legacy/api/v2/class-wc-api-webhooks.php:441
#: includes/legacy/api/v3/class-wc-api-webhooks.php:441
msgid "Invalid webhook delivery ID."
msgstr "ID crochet Web délivré non valide."

#: includes/legacy/api/v2/class-wc-api-webhooks.php:199
#: includes/legacy/api/v3/class-wc-api-webhooks.php:199
msgid "Webhook created on %s"
msgstr "Crochet Web créé le %s"

#: includes/legacy/api/v2/class-wc-api-webhooks.php:190
#: includes/legacy/api/v2/class-wc-api-webhooks.php:276
#: includes/legacy/api/v3/class-wc-api-webhooks.php:190
#: includes/legacy/api/v3/class-wc-api-webhooks.php:276
msgid "Webhook delivery URL must be a valid URL starting with http:// or https://"
msgstr "L’URL du crochet web délivrée doit être une URL valide commençant par http:// ou https://"

#: includes/legacy/api/v2/class-wc-api-webhooks.php:185
#: includes/legacy/api/v3/class-wc-api-webhooks.php:185
msgid "Webhook topic is required and must be valid."
msgstr "Le sujet du crochet Web est nécessaire et doit être valide."

#: includes/legacy/api/v2/class-wc-api-webhooks.php:178
#: includes/legacy/api/v3/class-wc-api-webhooks.php:178
msgid "You do not have permission to create webhooks."
msgstr "Vous n’avez pas le droit de créer des crochets Web."

#: includes/legacy/api/v2/class-wc-api-webhooks.php:143
#: includes/legacy/api/v3/class-wc-api-webhooks.php:143
msgid "You do not have permission to read the webhooks count"
msgstr "Vous n’avez pas le droit de voir le nombre de crochets Web"

#: includes/legacy/api/v2/class-wc-api-resource.php:103
#: includes/legacy/api/v2/class-wc-api-webhooks.php:482
#: includes/legacy/api/v3/class-wc-api-resource.php:111
#: includes/legacy/api/v3/class-wc-api-webhooks.php:482
msgid "No %1$s found with the ID equal to %2$s"
msgstr "Pas de %1$s trouvé avec l’ID %2$s"

#: includes/legacy/api/v2/class-wc-api-products.php:2135
#: includes/legacy/api/v3/class-wc-api-products.php:2690
msgid "You do not have permission to delete product attributes"
msgstr "Vous n’avez pas le droit de supprimer des attributs de produit"

#: includes/legacy/api/v2/class-wc-api-products.php:2059
#: includes/legacy/api/v3/class-wc-api-products.php:2613
#: includes/legacy/api/v3/class-wc-api-products.php:2921
msgid "You do not have permission to edit product attributes"
msgstr "Vous n’avez pas le droit de modifier des attributs de produit"

#: includes/legacy/api/v2/class-wc-api-products.php:1974
#: includes/legacy/api/v3/class-wc-api-products.php:2527
#: includes/legacy/api/v3/class-wc-api-products.php:2855
msgid "You do not have permission to create product attributes"
msgstr "Vous n’avez pas le droit de créer des attributs de produit"

#: includes/legacy/api/v2/class-wc-api-products.php:1947
#: includes/legacy/api/v3/class-wc-api-products.php:2500
msgid "Invalid product attribute order_by type - the product attribute order_by type must be any of these: %s"
msgstr "Type d’attribut de produit order_by non valide - Le type d’attribut de produit order_by doit être un de ceux-ci : %s"

#: includes/legacy/api/v2/class-wc-api-products.php:1942
#: includes/legacy/api/v3/class-wc-api-products.php:2495
msgid "Invalid product attribute type - the product attribute type must be any of these: %s"
msgstr "Type d’attribut de produit non valide - le type d’attribut de produit doit être un de ceux-ci : %s"

#: includes/legacy/api/v2/class-wc-api-products.php:1937
#: includes/legacy/api/v3/class-wc-api-products.php:2490
msgid "Slug \"%s\" is already in use. Change it, please."
msgstr "L’identifiant d’URL « %s » est déjà utilisé. Veuillez le changer."

#: includes/legacy/api/v2/class-wc-api-products.php:1935
#: includes/legacy/api/v3/class-wc-api-products.php:2488
msgid "Slug \"%s\" is not allowed because it is a reserved term. Change it, please."
msgstr "L’identifiant d’URL « %s » n’est pas autorisé car c’est un terme réservé. Veuillez le changer."

#: includes/legacy/api/v2/class-wc-api-products.php:1933
#: includes/legacy/api/v3/class-wc-api-products.php:2486
msgid "Slug \"%s\" is too long (28 characters max). Shorten it, please."
msgstr "L’identifiant d’URL « %s » est trop long (28 caractères max.). Veuillez le raccourcir."

#: includes/legacy/api/v2/class-wc-api-products.php:1897
#: includes/legacy/api/v2/class-wc-api-products.php:2147
#: includes/legacy/api/v3/class-wc-api-products.php:2450
#: includes/legacy/api/v3/class-wc-api-products.php:2702
#: includes/legacy/api/v3/class-wc-api-products.php:2759
#: includes/legacy/api/v3/class-wc-api-products.php:2811
#: includes/legacy/api/v3/class-wc-api-products.php:2861
#: includes/legacy/api/v3/class-wc-api-products.php:2927
#: includes/legacy/api/v3/class-wc-api-products.php:2980
msgid "A product attribute with the provided ID could not be found"
msgstr "Un attribut de produit avec l’ID fourni ne peut pas être trouvé"

#: includes/legacy/api/v2/class-wc-api-products.php:1841
#: includes/legacy/api/v2/class-wc-api-products.php:1887
#: includes/legacy/api/v3/class-wc-api-products.php:2394
#: includes/legacy/api/v3/class-wc-api-products.php:2440
msgid "You do not have permission to read product attributes"
msgstr "Vous n’avez pas le droit de lire les attributs de produit"

#: includes/legacy/api/v2/class-wc-api-products.php:604
#: includes/legacy/api/v3/class-wc-api-products.php:663
msgid "A product category with the provided ID could not be found"
msgstr "Une catégorie de produit avec l’ID fourni ne peut pas être trouvé"

#: includes/legacy/api/v2/class-wc-api-products.php:560
#: includes/legacy/api/v2/class-wc-api-products.php:598
#: includes/legacy/api/v3/class-wc-api-products.php:619
#: includes/legacy/api/v3/class-wc-api-products.php:657
msgid "You do not have permission to read product categories"
msgstr "Vous n’avez pas le droit de voir les catégories de produit"

#: includes/legacy/api/v2/class-wc-api-products.php:243
#: includes/legacy/api/v2/class-wc-api-products.php:369
#: includes/legacy/api/v3/class-wc-api-products.php:292
#: includes/legacy/api/v3/class-wc-api-products.php:423
msgid "Invalid product type - the product type must be any of these: %s"
msgstr "Type de produit non valide - le type de produit doit être un de ceux-ci : %s"

#: includes/legacy/api/v2/class-wc-api-products.php:221
#: includes/legacy/api/v3/class-wc-api-products.php:270
msgid "You do not have permission to create products"
msgstr "Vous n’avez pas le droit de créer des produits"

#: includes/legacy/api/v2/class-wc-api-orders.php:1686
#: includes/legacy/api/v2/class-wc-api-orders.php:1747
#: includes/legacy/api/v3/class-wc-api-orders.php:1731
#: includes/legacy/api/v3/class-wc-api-orders.php:1792
msgid "The order refund ID provided is not associated with the order."
msgstr "L’ID de remboursement n’est pas associé à la commande."

#: includes/legacy/api/v2/class-wc-api-orders.php:1628
#: includes/legacy/api/v3/class-wc-api-orders.php:1673
msgid "An error occurred while attempting to create the refund using the payment gateway API."
msgstr "Une erreur s’est produite en tentant de créer le remboursement en utilisant l’API de la passerelle de paiement."

#: includes/legacy/api/v2/class-wc-api-orders.php:1611
#: includes/legacy/api/v3/class-wc-api-orders.php:1656
msgid "Cannot create order refund, please try again."
msgstr "Impossible de créer un remboursement de commande, réessayer."

#: includes/legacy/api/v2/class-wc-api-orders.php:1601
#: includes/legacy/api/v3/class-wc-api-orders.php:1646
msgid "Refund amount must be positive."
msgstr "Le montant du remboursement doit être positif."

#: includes/legacy/api/v2/class-wc-api-orders.php:1599
#: includes/legacy/api/v3/class-wc-api-orders.php:1644
msgid "Refund amount is required."
msgstr "Le montant de remboursement est nécessaire."

#: includes/legacy/api/v2/class-wc-api-orders.php:1586
#: includes/legacy/api/v3/class-wc-api-orders.php:1631
msgid "You do not have permission to create order refunds"
msgstr "Vous n’avez pas le droit de créer des remboursements de commande"

#: includes/legacy/api/v2/class-wc-api-orders.php:1519
#: includes/legacy/api/v2/class-wc-api-orders.php:1681
#: includes/legacy/api/v2/class-wc-api-orders.php:1742
#: includes/legacy/api/v3/class-wc-api-orders.php:1564
#: includes/legacy/api/v3/class-wc-api-orders.php:1726
#: includes/legacy/api/v3/class-wc-api-orders.php:1787
msgid "An order refund with the provided ID could not be found."
msgstr "Aucune commande de remboursement avec l’ID fourni ne peut être trouvée."

#: includes/legacy/api/v2/class-wc-api-orders.php:1512
#: includes/legacy/api/v2/class-wc-api-orders.php:1674
#: includes/legacy/api/v2/class-wc-api-orders.php:1735
#: includes/legacy/api/v3/class-wc-api-orders.php:1557
#: includes/legacy/api/v3/class-wc-api-orders.php:1719
#: includes/legacy/api/v3/class-wc-api-orders.php:1780
msgid "Invalid order refund ID."
msgstr "ID de commande de remboursement non valide."

#: includes/legacy/api/v2/class-wc-api-orders.php:1450
#: includes/legacy/api/v3/class-wc-api-orders.php:1495
msgid "Permanently deleted order note"
msgstr "Note de commande définitivement supprimée"

#: includes/legacy/api/v2/class-wc-api-orders.php:1374
#: includes/legacy/api/v2/class-wc-api-orders.php:1438
#: includes/legacy/api/v3/class-wc-api-orders.php:1419
#: includes/legacy/api/v3/class-wc-api-orders.php:1483
msgid "The order note ID provided is not associated with the order"
msgstr "L’ID de note de commande fourni n’est pas associé à la commande"

#: includes/legacy/api/v2/class-wc-api-orders.php:1316
#: includes/legacy/api/v3/class-wc-api-orders.php:1361
msgid "Cannot create order note, please try again."
msgstr "Impossible de créer une note de commande, réessayer."

#: includes/legacy/api/v2/class-wc-api-orders.php:1307
#: includes/legacy/api/v3/class-wc-api-orders.php:1352
msgid "Order note is required"
msgstr "La note de commande est nécessairee"

#: includes/legacy/api/v2/class-wc-api-orders.php:1292
#: includes/legacy/api/v3/class-wc-api-orders.php:1337
msgid "You do not have permission to create order notes"
msgstr "Vous n’avez pas le droit de créer des notes de commande"

#: includes/legacy/api/v2/class-wc-api-orders.php:1258
#: includes/legacy/api/v2/class-wc-api-orders.php:1369
#: includes/legacy/api/v2/class-wc-api-orders.php:1433
#: includes/legacy/api/v3/class-wc-api-orders.php:1303
#: includes/legacy/api/v3/class-wc-api-orders.php:1414
#: includes/legacy/api/v3/class-wc-api-orders.php:1478
msgid "An order note with the provided ID could not be found"
msgstr "Une note de commande avec l’ID fourni ne peut être trouvée"

#: includes/legacy/api/v2/class-wc-api-orders.php:1252
#: includes/legacy/api/v2/class-wc-api-orders.php:1362
#: includes/legacy/api/v2/class-wc-api-orders.php:1426
#: includes/legacy/api/v3/class-wc-api-orders.php:1297
#: includes/legacy/api/v3/class-wc-api-orders.php:1407
#: includes/legacy/api/v3/class-wc-api-orders.php:1471
msgid "Invalid order note ID"
msgstr "L’ID de la note de commande est non valide"

#: includes/legacy/api/v2/class-wc-api-orders.php:1185
#: includes/legacy/api/v3/class-wc-api-orders.php:1230
msgid "Cannot update coupon, try again."
msgstr "Impossible de modifier le code promo, réessayer."

#: includes/legacy/api/v2/class-wc-api-orders.php:1152
#: includes/legacy/api/v3/class-wc-api-orders.php:1197
msgid "Coupon discount total must be a positive amount."
msgstr "Le montant total de remise du code promo doit être positif."

#: includes/legacy/api/v2/class-wc-api-orders.php:1134
#: includes/legacy/api/v3/class-wc-api-orders.php:1179
msgid "Cannot update fee, try again."
msgstr "Impossible de modifier les frais, réessayer."

#: includes/legacy/api/v2/class-wc-api-orders.php:1094
#: includes/legacy/api/v3/class-wc-api-orders.php:1139
msgid "Fee tax class is required when fee is taxable."
msgstr "Une classe de taxe de frais est nécessaire lorsque les frais sont taxables."

#: includes/legacy/api/v2/class-wc-api-orders.php:1083
#: includes/legacy/api/v3/class-wc-api-orders.php:1128
msgid "Fee title is required"
msgstr "Le titre des frais est nécessaire"

#: includes/legacy/api/v2/class-wc-api-orders.php:1063
#: includes/legacy/api/v3/class-wc-api-orders.php:1108
msgid "Cannot update shipping method, try again."
msgstr "Impossible de modifier le mode de livraison, réessayer."

#: includes/legacy/api/v2/class-wc-api-orders.php:1036
#: includes/legacy/api/v3/class-wc-api-orders.php:1081
msgid "Shipping method ID is required."
msgstr "L’ID du mode de livraison est nécessaire."

#: includes/legacy/api/v2/class-wc-api-orders.php:1029
#: includes/legacy/api/v3/class-wc-api-orders.php:1074
msgid "Shipping total must be a positive amount."
msgstr "Le total de livraison doit être un montant positif."

#: includes/legacy/api/v2/class-wc-api-orders.php:953
#: includes/legacy/api/v3/class-wc-api-orders.php:998
msgid "Cannot create line item, try again."
msgstr "Impossible de créer la ligne d’élément, réessayer."

#: includes/legacy/api/v2/class-wc-api-orders.php:910
#: includes/legacy/api/v3/class-wc-api-orders.php:954
msgid "Product quantity is required."
msgstr "La quantité de produit est nécessaire."

#: includes/legacy/api/v2/class-wc-api-orders.php:905
#: includes/legacy/api/v3/class-wc-api-orders.php:949
msgid "Product quantity must be a positive float."
msgstr "La quantité de produit doit être un entier positif."

#: includes/legacy/api/v2/class-wc-api-orders.php:890
#: includes/legacy/api/v3/class-wc-api-orders.php:934
msgid "The product variation is invalid"
msgstr "La variation de produit est non valide"

#: includes/legacy/api/v2/class-wc-api-orders.php:875
#: includes/legacy/api/v3/class-wc-api-orders.php:919
msgid "Product ID provided does not match this line item"
msgstr "L’ID produit fourni ne correspond pas à cette ligne d’élément"

#: includes/legacy/api/v2/class-wc-api-orders.php:865
#: includes/legacy/api/v3/class-wc-api-orders.php:909
msgid "Product ID or SKU is required"
msgstr "L’ID ou l’UGS produit est nécessaire"

#: includes/legacy/api/v2/class-wc-api-orders.php:844
#: includes/legacy/api/v3/class-wc-api-orders.php:888
msgid "Order item ID provided is not associated with order."
msgstr "L’ID de l’article de commande fourni n’est pas associé à la commande."

#: includes/legacy/api/v2/class-wc-api-orders.php:448
#: includes/legacy/api/v2/class-wc-api-orders.php:600
#: includes/legacy/api/v3/class-wc-api-orders.php:488
#: includes/legacy/api/v3/class-wc-api-orders.php:637
msgid "Provided order currency is invalid."
msgstr "La devise de commande fournie est non valide."

#: includes/legacy/api/v2/class-wc-api-orders.php:432
#: includes/legacy/api/v3/class-wc-api-orders.php:472
msgid "Payment method ID and title are required"
msgstr "L’ID et le titre du moyen de paiement sont nécessaires"

#: includes/legacy/api/v2/class-wc-api-orders.php:398
#: includes/legacy/api/v3/class-wc-api-orders.php:433
msgid "Cannot create order: %s"
msgstr "Impossible de créer la commande : %s"

#: includes/legacy/api/v2/class-wc-api-orders.php:372
#: includes/legacy/api/v3/class-wc-api-orders.php:407
msgid "You do not have permission to create orders"
msgstr "Vous n’avez pas le droit de créer des commandes"

#: includes/legacy/api/v2/class-wc-api-customers.php:374
#: includes/legacy/api/v3/class-wc-api-customers.php:375
msgid "This resource cannot be created."
msgstr "Cette ressource ne peut pas être créée."

#: includes/legacy/api/v2/class-wc-api-coupons.php:521
#: includes/legacy/api/v2/class-wc-api-customers.php:784
#: includes/legacy/api/v2/class-wc-api-orders.php:1775
#: includes/legacy/api/v2/class-wc-api-products.php:2249
#: includes/legacy/api/v3/class-wc-api-coupons.php:521
#: includes/legacy/api/v3/class-wc-api-customers.php:774
#: includes/legacy/api/v3/class-wc-api-orders.php:1820
#: includes/legacy/api/v3/class-wc-api-products.php:3041
#: includes/legacy/api/v3/class-wc-api-taxes.php:457
msgid "No %1$s data specified to create/edit %1$s"
msgstr "Aucune donnée %1$s spécifiée pour créer/modifier %1$s"

#: includes/legacy/api/v2/class-wc-api-coupons.php:350
#: includes/legacy/api/v2/class-wc-api-coupons.php:358
#: includes/legacy/api/v3/class-wc-api-coupons.php:348
#: includes/legacy/api/v3/class-wc-api-coupons.php:356
msgid "Failed to update coupon"
msgstr "Échec de la mise à jour du code promo"

#: includes/legacy/api/v2/class-wc-api-coupons.php:324
#: includes/legacy/api/v2/class-wc-api-customers.php:404
#: includes/legacy/api/v2/class-wc-api-orders.php:504
#: includes/legacy/api/v2/class-wc-api-orders.php:1344
#: includes/legacy/api/v2/class-wc-api-orders.php:1658
#: includes/legacy/api/v2/class-wc-api-products.php:323
#: includes/legacy/api/v2/class-wc-api-products.php:2051
#: includes/legacy/api/v2/class-wc-api-webhooks.php:242
#: includes/legacy/api/v3/class-wc-api-coupons.php:322
#: includes/legacy/api/v3/class-wc-api-customers.php:405
#: includes/legacy/api/v3/class-wc-api-orders.php:542
#: includes/legacy/api/v3/class-wc-api-orders.php:1389
#: includes/legacy/api/v3/class-wc-api-orders.php:1703
#: includes/legacy/api/v3/class-wc-api-products.php:377
#: includes/legacy/api/v3/class-wc-api-products.php:784
#: includes/legacy/api/v3/class-wc-api-products.php:1004
#: includes/legacy/api/v3/class-wc-api-products.php:2605
#: includes/legacy/api/v3/class-wc-api-products.php:2913
#: includes/legacy/api/v3/class-wc-api-products.php:3249
#: includes/legacy/api/v3/class-wc-api-taxes.php:254
#: includes/legacy/api/v3/class-wc-api-webhooks.php:242
msgid "No %1$s data specified to edit %1$s"
msgstr "Aucune donnée %1$s spécifiée pour modifier %1$s"

#: includes/legacy/api/v2/class-wc-api-coupons.php:261
#: includes/legacy/api/v2/class-wc-api-coupons.php:365
#: includes/legacy/api/v3/class-wc-api-coupons.php:259
#: includes/legacy/api/v3/class-wc-api-coupons.php:363
msgid "Invalid coupon type - the coupon type must be any of these: %s"
msgstr "Type de code promo non valide - le type doit être un ce ceux-ci : %s"

#: includes/legacy/api/v2/class-wc-api-coupons.php:219
#: includes/legacy/api/v3/class-wc-api-coupons.php:217
msgid "You do not have permission to create coupons"
msgstr "Vous n’avez pas le droit de créer des codes promo"

#: includes/legacy/api/v2/class-wc-api-coupons.php:212
#: includes/legacy/api/v2/class-wc-api-customers.php:349
#: includes/legacy/api/v2/class-wc-api-orders.php:365
#: includes/legacy/api/v2/class-wc-api-orders.php:1285
#: includes/legacy/api/v2/class-wc-api-orders.php:1579
#: includes/legacy/api/v2/class-wc-api-products.php:214
#: includes/legacy/api/v2/class-wc-api-products.php:1967
#: includes/legacy/api/v2/class-wc-api-webhooks.php:171
#: includes/legacy/api/v3/class-wc-api-coupons.php:210
#: includes/legacy/api/v3/class-wc-api-customers.php:350
#: includes/legacy/api/v3/class-wc-api-orders.php:400
#: includes/legacy/api/v3/class-wc-api-orders.php:1330
#: includes/legacy/api/v3/class-wc-api-orders.php:1624
#: includes/legacy/api/v3/class-wc-api-products.php:263
#: includes/legacy/api/v3/class-wc-api-products.php:707
#: includes/legacy/api/v3/class-wc-api-products.php:959
#: includes/legacy/api/v3/class-wc-api-products.php:2520
#: includes/legacy/api/v3/class-wc-api-products.php:2848
#: includes/legacy/api/v3/class-wc-api-products.php:3191
#: includes/legacy/api/v3/class-wc-api-taxes.php:184
#: includes/legacy/api/v3/class-wc-api-taxes.php:564
#: includes/legacy/api/v3/class-wc-api-webhooks.php:171
msgid "No %1$s data specified to create %1$s"
msgstr "Aucune donnée %1$s spécifiée pour créer %1$s"

#: includes/legacy/api/v1/class-wc-api-server.php:412
#: includes/legacy/api/v2/class-wc-api-coupons.php:226
#: includes/legacy/api/v2/class-wc-api-customers.php:363
#: includes/legacy/api/v2/class-wc-api-products.php:228
#: includes/legacy/api/v2/class-wc-api-products.php:1929
#: includes/legacy/api/v2/class-wc-api-server.php:430
#: includes/legacy/api/v3/class-wc-api-coupons.php:224
#: includes/legacy/api/v3/class-wc-api-customers.php:364
#: includes/legacy/api/v3/class-wc-api-products.php:277
#: includes/legacy/api/v3/class-wc-api-products.php:2482
#: includes/legacy/api/v3/class-wc-api-products.php:2868
#: includes/legacy/api/v3/class-wc-api-server.php:431
#: includes/legacy/api/v3/class-wc-api-taxes.php:575
msgid "Missing parameter %s"
msgstr "Paramètre %s manquant"

#: includes/legacy/api/v1/class-wc-api-server.php:377
#: includes/legacy/api/v2/class-wc-api-server.php:375
#: includes/legacy/api/v3/class-wc-api-server.php:375
msgid "No route was found matching the URL and request method"
msgstr "Aucun itinéraire n’a été trouvé correspondant à l’URL et à la méthode de requête"

#: includes/legacy/api/v1/class-wc-api-server.php:340
#: includes/legacy/api/v2/class-wc-api-server.php:338
#: includes/legacy/api/v3/class-wc-api-server.php:338
msgid "The handler for the route is invalid"
msgstr "Le gestionnaire de route est non valide"

#: includes/legacy/api/v1/class-wc-api-server.php:321
#: includes/legacy/api/v2/class-wc-api-server.php:319
#: includes/legacy/api/v3/class-wc-api-server.php:319
msgid "Unsupported request method"
msgstr "Méthode de requête non supportée"

#: includes/legacy/api/v1/class-wc-api-server.php:168
#: includes/legacy/api/v2/class-wc-api-server.php:164
#: includes/legacy/api/v3/class-wc-api-server.php:164
msgid "Invalid authentication method"
msgstr "Méthode d’authentification non valide"

#: includes/legacy/api/v1/class-wc-api-resource.php:327
#: includes/legacy/api/v2/class-wc-api-products.php:464
#: includes/legacy/api/v2/class-wc-api-resource.php:386
#: includes/legacy/api/v3/class-wc-api-products.php:523
#: includes/legacy/api/v3/class-wc-api-resource.php:394
msgid "Permanently deleted %s"
msgstr "%s supprimé(s) définitivement"

#: includes/legacy/api/v1/class-wc-api-resource.php:323
#: includes/legacy/api/v2/class-wc-api-products.php:455
#: includes/legacy/api/v2/class-wc-api-resource.php:382
#: includes/legacy/api/v3/class-wc-api-products.php:514
#: includes/legacy/api/v3/class-wc-api-products.php:2987
#: includes/legacy/api/v3/class-wc-api-resource.php:390
msgid "This %s cannot be deleted"
msgstr "%s ne peut être supprimé"

#: includes/legacy/api/v1/class-wc-api-resource.php:315
#: includes/legacy/api/v2/class-wc-api-resource.php:374
#: includes/legacy/api/v3/class-wc-api-resource.php:382
msgid "The customer cannot be deleted"
msgstr "Le client ne peut être supprimé"

#: includes/legacy/api/v1/class-wc-api-resource.php:313
#: includes/legacy/api/v2/class-wc-api-resource.php:372
#: includes/legacy/api/v3/class-wc-api-resource.php:380
msgid "Permanently deleted customer"
msgstr "Client définitivement supprimé"

#: includes/legacy/api/v1/class-wc-api-resource.php:107
#: includes/legacy/api/v2/class-wc-api-resource.php:131
#: includes/legacy/api/v2/class-wc-api-webhooks.php:502
#: includes/legacy/api/v3/class-wc-api-resource.php:140
#: includes/legacy/api/v3/class-wc-api-webhooks.php:502
msgid "You do not have permission to delete this %s"
msgstr "Vous n’avez pas le droit de supprimer ce %s"

#: includes/legacy/api/v1/class-wc-api-resource.php:101
#: includes/legacy/api/v2/class-wc-api-resource.php:125
#: includes/legacy/api/v2/class-wc-api-webhooks.php:496
#: includes/legacy/api/v3/class-wc-api-resource.php:134
#: includes/legacy/api/v3/class-wc-api-webhooks.php:496
msgid "You do not have permission to edit this %s"
msgstr "Vous n’avez pas le droit de modifier ce %s"

#: includes/legacy/api/v1/class-wc-api-resource.php:95
#: includes/legacy/api/v2/class-wc-api-resource.php:119
#: includes/legacy/api/v2/class-wc-api-webhooks.php:490
#: includes/legacy/api/v3/class-wc-api-resource.php:128
#: includes/legacy/api/v3/class-wc-api-webhooks.php:490
msgid "You do not have permission to read this %s"
msgstr "Vous n’avez pas le droit de voir ce %s"

#: includes/legacy/api/v1/class-wc-api-resource.php:87
#: includes/legacy/api/v2/class-wc-api-resource.php:111
#: includes/legacy/api/v3/class-wc-api-resource.php:119
msgid "Invalid %s"
msgstr "%s non valide"

#: includes/legacy/api/v1/class-wc-api-reports.php:475
#: includes/legacy/api/v2/class-wc-api-reports.php:322
#: includes/legacy/api/v3/class-wc-api-reports.php:326
msgid "You do not have permission to read this report"
msgstr "Vous n’avez pas le droit de voir ce rapport"

#: includes/legacy/api/v1/class-wc-api-products.php:147
#: includes/legacy/api/v2/class-wc-api-products.php:185
#: includes/legacy/api/v3/class-wc-api-products.php:234
msgid "You do not have permission to read the products count"
msgstr "Vous n’avez pas le droit de lire le comptage des produits"

#: includes/legacy/api/v1/class-wc-api-orders.php:255
#: includes/legacy/api/v2/class-wc-api-orders.php:299
#: includes/legacy/api/v3/class-wc-api-orders.php:336
msgid "You do not have permission to read the orders count"
msgstr "Vous n’avez pas le droit de lire le comptage des commandes"

#: includes/legacy/api/v1/class-wc-api-json-handler.php:62
#: includes/legacy/api/v2/class-wc-api-json-handler.php:61
#: includes/legacy/api/v3/class-wc-api-json-handler.php:61
msgid "The JSONP callback function is invalid"
msgstr "L’appel à la fonction JSONP est non valide"

#: includes/legacy/api/v1/class-wc-api-json-handler.php:55
#: includes/legacy/api/v2/class-wc-api-json-handler.php:54
#: includes/legacy/api/v3/class-wc-api-json-handler.php:54
msgid "JSONP support is disabled on this site"
msgstr "Le support JSONP est d&eacute;sactiv&eacute; sur ce site"

#: includes/legacy/api/v1/class-wc-api-customers.php:461
#: includes/legacy/api/v2/class-wc-api-customers.php:746
#: includes/legacy/api/v3/class-wc-api-customers.php:736
msgid "You do not have permission to delete this customer"
msgstr "Vous n’avez pas le droit de supprimer ce client"

#: includes/legacy/api/v1/class-wc-api-customers.php:455
#: includes/legacy/api/v2/class-wc-api-customers.php:740
#: includes/legacy/api/v3/class-wc-api-customers.php:730
msgid "You do not have permission to edit this customer"
msgstr "Vous n’avez pas le droit de modifier ce client"

#: includes/legacy/api/v1/class-wc-api-customers.php:449
#: includes/legacy/api/v2/class-wc-api-customers.php:734
#: includes/legacy/api/v3/class-wc-api-customers.php:724
msgid "You do not have permission to read this customer"
msgstr "Vous n’avez pas le droit d’accéder à ce client"

#: includes/legacy/api/v1/class-wc-api-customers.php:202
#: includes/legacy/api/v2/class-wc-api-customers.php:356
#: includes/legacy/api/v3/class-wc-api-customers.php:357
msgid "You do not have permission to create this customer"
msgstr "Vous n’avez pas le droit de créer ce client"

#: includes/legacy/api/v1/class-wc-api-customers.php:186
#: includes/legacy/api/v2/class-wc-api-customers.php:231
#: includes/legacy/api/v3/class-wc-api-customers.php:232
msgid "You do not have permission to read the customers count"
msgstr "Vous n’avez pas le droit de lire le comptage des clients"

#: includes/legacy/api/v1/class-wc-api-authentication.php:387
#: includes/legacy/api/v2/class-wc-api-authentication.php:384
#: includes/legacy/api/v3/class-wc-api-authentication.php:391
msgid "The API key provided does not have write permissions."
msgstr "La clé d’API fournie ne possède pas les droits d’écriture."

#: includes/legacy/api/v1/class-wc-api-authentication.php:378
#: includes/legacy/api/v2/class-wc-api-authentication.php:375
#: includes/legacy/api/v3/class-wc-api-authentication.php:382
msgid "The API key provided does not have read permissions."
msgstr "La clé d’API fournie ne possède pas des droits de lecture."

#: includes/legacy/api/v1/class-wc-api-authentication.php:343
#: includes/legacy/api/v2/class-wc-api-authentication.php:340
#: includes/legacy/api/v3/class-wc-api-authentication.php:347
msgid "Invalid nonce - nonce has already been used."
msgstr "Nonce non valide - Le nonce a déjà été utilisé."

#: includes/legacy/api/v1/class-wc-api-authentication.php:333
#: includes/legacy/api/v2/class-wc-api-authentication.php:330
#: includes/legacy/api/v3/class-wc-api-authentication.php:337
msgid "Invalid timestamp."
msgstr "Horodatage non valide."

#: includes/legacy/api/v1/class-wc-api-authentication.php:275
#: includes/legacy/api/v2/class-wc-api-authentication.php:272
#: includes/legacy/api/v3/class-wc-api-authentication.php:272
msgid "Invalid signature - provided signature does not match."
msgstr "Signature non valide - La signature fournie ne correspond pas."

#: includes/legacy/api/v1/class-wc-api-authentication.php:267
#: includes/legacy/api/v2/class-wc-api-authentication.php:264
#: includes/legacy/api/v3/class-wc-api-authentication.php:263
msgid "Invalid signature - signature method is invalid."
msgstr "Signature non valide - La méthode de signature est non valide."

#: includes/legacy/api/v1/class-wc-api-authentication.php:253
#: includes/legacy/api/v2/class-wc-api-authentication.php:250
#: includes/legacy/api/v3/class-wc-api-authentication.php:243
msgid "Invalid signature - failed to sort parameters."
msgstr "Signature non valide - Échec du tri des paramètres."

#: includes/legacy/api/v1/class-wc-api-authentication.php:203
#: includes/legacy/api/v2/class-wc-api-authentication.php:200
#: includes/legacy/api/v3/class-wc-api-authentication.php:199
msgid "API user is invalid"
msgstr "L’API utilisateur/utilisatrice n’est pas valide"

#. translators: %s: parameter name
#: includes/legacy/api/v1/class-wc-api-authentication.php:149
#: includes/legacy/api/v2/class-wc-api-authentication.php:147
#: includes/legacy/api/v3/class-wc-api-authentication.php:144
msgid "%s parameter is missing"
msgstr "Le paramètre %s est manquant"

#: includes/legacy/api/v1/class-wc-api-authentication.php:115
#: includes/legacy/api/v2/class-wc-api-authentication.php:114
#: includes/legacy/api/v3/class-wc-api-authentication.php:83
msgid "Consumer secret is invalid."
msgstr "La clé secrète client/cliente est non valide."

#: includes/legacy/api/v1/class-wc-api-authentication.php:109
#: includes/legacy/api/v2/class-wc-api-authentication.php:108
msgid "Consumer secret is missing."
msgstr "La clé secrète client/cliente est manquante."

#. translators: 1: variation id 2: product name
#: includes/legacy/api/class-wc-rest-legacy-products-controller.php:532
#: includes/legacy/api/v2/class-wc-api-products.php:1293
#: includes/legacy/api/v3/class-wc-api-products.php:1783
msgid "Variation #%1$s of %2$s"
msgstr "Variation n°%1$s sur %2$s"

#: includes/legacy/api/class-wc-rest-legacy-coupons-controller.php:129
#: includes/legacy/api/v2/class-wc-api-coupons.php:233
#: includes/legacy/api/v2/class-wc-api-coupons.php:344
#: includes/legacy/api/v3/class-wc-api-coupons.php:231
#: includes/legacy/api/v3/class-wc-api-coupons.php:342
msgid "The coupon code already exists"
msgstr "Ce code promo existe déjà"

#: includes/legacy/api/class-wc-rest-legacy-coupons-controller.php:113
msgid "The coupon code cannot be empty."
msgstr "Le code promo ne peut pas être vide."

#: includes/class-wc-api.php:80
msgid "The Rest API is unavailable."
msgstr "L’API REST est indisponible."

#. Author URI of the plugin
#: woocommerce-legacy-rest-api.php
msgid "https://woocommerce.com"
msgstr "https://woocommerce.com"

#. Author of the plugin
#: woocommerce-legacy-rest-api.php
msgid "WooCommerce"
msgstr "WooCommerce"

#. Description of the plugin
#: woocommerce-legacy-rest-api.php
msgid "The legacy WooCommerce REST API, which used to be part of WooCommerce itself but is removed as of WooCommerce 9.0."
msgstr "L’ancienne API REST WooCommerce, qui faisait partie de WooCommerce lui-même, mais qui est supprimée à partir de WooCommerce 9.0."

#. Plugin URI of the plugin
#: woocommerce-legacy-rest-api.php
msgid "https://github.com/woocommerce/woocommerce-legacy-rest-api"
msgstr "https://github.com/woocommerce/woocommerce-legacy-rest-api"

#. Plugin Name of the plugin
#: woocommerce-legacy-rest-api.php
msgid "WooCommerce Legacy REST API"
msgstr "WooCommerce Legacy REST API"

#: includes/legacy/api/v2/class-wc-api-coupons.php:529
#: includes/legacy/api/v2/class-wc-api-customers.php:792
#: includes/legacy/api/v2/class-wc-api-orders.php:1783
#: includes/legacy/api/v2/class-wc-api-products.php:2257
#: includes/legacy/api/v3/class-wc-api-coupons.php:529
#: includes/legacy/api/v3/class-wc-api-customers.php:782
#: includes/legacy/api/v3/class-wc-api-orders.php:1828
#: includes/legacy/api/v3/class-wc-api-products.php:3049
#: includes/legacy/api/v3/class-wc-api-taxes.php:465
msgid "Unable to accept more than %s items for this request."
msgstr "Impossible d’accepter plus de %s éléments pour cette demande."

#: includes/legacy/api/v1/class-wc-api-products.php:459
#: includes/legacy/api/v1/class-wc-api-products.php:460
#: includes/legacy/api/v2/class-wc-api-products.php:1620
#: includes/legacy/api/v2/class-wc-api-products.php:1621
#: includes/legacy/api/v3/class-wc-api-products.php:2119
#: includes/legacy/api/v3/class-wc-api-products.php:2120
msgid "Placeholder"
msgstr "Texte indicatif"

#: includes/legacy/api/v1/class-wc-api-customers.php:441
#: includes/legacy/api/v2/class-wc-api-customers.php:726
#: includes/legacy/api/v3/class-wc-api-customers.php:716
msgid "Invalid customer"
msgstr "Client/cliente non valide"

#: includes/legacy/api/v1/class-wc-api-customers.php:434
#: includes/legacy/api/v2/class-wc-api-customers.php:719
#: includes/legacy/api/v3/class-wc-api-customers.php:709
msgid "Invalid customer ID"
msgstr "ID client/cliente invalide"

#: includes/legacy/api/v2/class-wc-api-webhooks.php:265
#: includes/legacy/api/v3/class-wc-api-webhooks.php:265
msgid "Webhook topic must be valid."
msgstr "Le sujet du crochet web doit être valide."

#: includes/legacy/api/v1/class-wc-api-resource.php:333
#: includes/legacy/api/v2/class-wc-api-products.php:468
#: includes/legacy/api/v2/class-wc-api-products.php:2176
#: includes/legacy/api/v2/class-wc-api-resource.php:390
#: includes/legacy/api/v3/class-wc-api-products.php:527
#: includes/legacy/api/v3/class-wc-api-products.php:866
#: includes/legacy/api/v3/class-wc-api-products.php:1058
#: includes/legacy/api/v3/class-wc-api-products.php:2732
#: includes/legacy/api/v3/class-wc-api-products.php:2994
#: includes/legacy/api/v3/class-wc-api-products.php:3305
#: includes/legacy/api/v3/class-wc-api-resource.php:400
#: includes/legacy/api/v3/class-wc-api-taxes.php:354
#: includes/legacy/api/v3/class-wc-api-taxes.php:623
msgid "Deleted %s"
msgstr "%s a été supprimé"

#: includes/legacy/api/v2/class-wc-api-orders.php:521
#: includes/legacy/api/v2/class-wc-api-orders.php:1592
#: includes/legacy/api/v3/class-wc-api-orders.php:559
#: includes/legacy/api/v3/class-wc-api-orders.php:1637
msgid "Order ID is invalid"
msgstr "L’ID de la commande est invalide"

#: includes/legacy/api/v2/class-wc-api-orders.php:900
#: includes/legacy/api/v3/class-wc-api-orders.php:944
msgid "Product is invalid."
msgstr "Ce produit est invalide."

#: includes/legacy/api/v2/class-wc-api-orders.php:1159
#: includes/legacy/api/v3/class-wc-api-orders.php:1204
msgid "Coupon code is required."
msgstr "Le code promo est obligatoire."

#: includes/legacy/api/v2/class-wc-api-products.php:593
#: includes/legacy/api/v3/class-wc-api-products.php:652
msgid "Invalid product category ID"
msgstr "L’ID de cette catégorie de produit est invalide"

#: includes/legacy/api/v2/class-wc-api-products.php:2199
msgid "Invalid product SKU"
msgstr "L’UGS du produit est invalide."

#: includes/legacy/api/v2/class-wc-api-products.php:949
#: includes/legacy/api/v3/class-wc-api-products.php:1434
msgid "The SKU already exists on another product."
msgstr "L‘UGS existe déjà sur un autre produit."

#: includes/legacy/api/v1/class-wc-api-coupons.php:174
#: includes/legacy/api/v2/class-wc-api-coupons.php:189
#: includes/legacy/api/v3/class-wc-api-coupons.php:187
msgid "Invalid coupon code"
msgstr "Ce code promo est invalide"

#: includes/legacy/api/v1/class-wc-api-resource.php:74
#: includes/legacy/api/v2/class-wc-api-resource.php:94
#: includes/legacy/api/v2/class-wc-api-webhooks.php:476
#: includes/legacy/api/v3/class-wc-api-resource.php:96
#: includes/legacy/api/v3/class-wc-api-webhooks.php:476
msgid "Invalid %s ID"
msgstr "L’ID %s est invalide"

#: includes/legacy/api/v1/class-wc-api-authentication.php:184
#: includes/legacy/api/v2/class-wc-api-authentication.php:182
#: includes/legacy/api/v3/class-wc-api-authentication.php:179
msgid "Consumer key is invalid."
msgstr "La clé client est invalide."

#: includes/legacy/api/v1/class-wc-api-coupons.php:111
#: includes/legacy/api/v2/class-wc-api-coupons.php:117
#: includes/legacy/api/v3/class-wc-api-coupons.php:117
msgid "Invalid coupon ID"
msgstr "L’ID de ce code promo est invalide"

#: includes/legacy/api/class-wc-rest-legacy-orders-controller.php:249
#: includes/legacy/api/v2/class-wc-api-orders.php:388
#: includes/legacy/api/v2/class-wc-api-orders.php:535
#: includes/legacy/api/v3/class-wc-api-orders.php:423
#: includes/legacy/api/v3/class-wc-api-orders.php:573
msgid "Customer ID is invalid."
msgstr "L’ID client/cliente est invalide."

#: includes/legacy/api/v2/class-wc-api-customers.php:207
#: includes/legacy/api/v2/class-wc-api-customers.php:210
#: includes/legacy/api/v3/class-wc-api-customers.php:208
#: includes/legacy/api/v3/class-wc-api-customers.php:211
msgid "Invalid customer email"
msgstr "Cet e-mail client/cliente est invalide."

#: includes/legacy/api/v2/class-wc-api-products.php:1882
#: includes/legacy/api/v3/class-wc-api-products.php:2435
#: includes/legacy/api/v3/class-wc-api-products.php:2800
msgid "Invalid product attribute ID"
msgstr "L’ID de l’attribut produit est invalide."

#: includes/legacy/api/v2/class-wc-api-products.php:2105
#: includes/legacy/api/v3/class-wc-api-products.php:2659
msgid "Could not edit the attribute"
msgstr "Impossible de modifier l’attribut."

#: includes/legacy/api/v2/class-wc-api-products.php:2157
#: includes/legacy/api/v3/class-wc-api-products.php:2712
msgid "Could not delete the attribute"
msgstr "Impossible de supprimer l’attribut."

#: includes/legacy/api/v3/class-wc-api-products.php:918
msgid "Invalid product tag ID"
msgstr "L’ID de l’étiquette produit est invalide."

#: includes/legacy/api/v3/class-wc-api-products.php:3300
msgid "Could not delete the shipping class"
msgstr "Impossible de supprimer la classe d’expédition."

#: includes/legacy/api/v1/class-wc-api-authentication.php:93
#: includes/legacy/api/v2/class-wc-api-authentication.php:92
msgid "Consumer key is missing."
msgstr "La clé client est manquante."

#: includes/legacy/api/v3/class-wc-api-products.php:3214
msgid "Product shipping class parent is invalid"
msgstr "Classe parente de livraison non valide"

#: includes/legacy/api/v3/class-wc-api-products.php:732
msgid "Product category parent is invalid"
msgstr "Catégorie parente produit non valide"

#: includes/legacy/api/v2/class-wc-api-webhooks.php:199
#: includes/legacy/api/v3/class-wc-api-webhooks.php:199
msgctxt "Webhook created on date parsed by DateTime::format"
msgid "M d, Y @ h:i A"
msgstr "j M Y à H:i"

#: includes/legacy/api/v2/class-wc-api-orders.php:1445
#: includes/legacy/api/v3/class-wc-api-orders.php:1490
msgid "This order note cannot be deleted"
msgstr "Cette note de commande ne peut être supprimée"

#: includes/legacy/api/v3/class-wc-api-products.php:1047
msgid "You do not have permission to delete product tag"
msgstr "Vous n’avez pas le droit de supprimer les étiquettes produit."

#: includes/legacy/api/v3/class-wc-api-products.php:1012
msgid "You do not have permission to edit product tags"
msgstr "Vous n’avez pas le droit de modifier les étiquettes produit."

#: includes/legacy/api/v3/class-wc-api-products.php:964
msgid "You do not have permission to create product tags"
msgstr "Vous n’avez pas le droit de créer des étiquettes produit."

#: includes/legacy/api/v3/class-wc-api-products.php:929
msgid "A product tag with the provided ID could not be found"
msgstr "Une étiquette produit avec cet ID est introuvable."

#: includes/legacy/api/v3/class-wc-api-products.php:1024
msgid "Could not edit the tag"
msgstr "Impossible de modifier l’étiquette."