<?php
/**
 * Image Optimization Functions
 *
 * This file contains functions to optimize images for better performance
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

class TCL_Image_Optimizer {

    public function __construct() {
        add_filter( 'wp_generate_attachment_metadata', array( $this, 'optimize_uploaded_image' ), 10, 2 );
        add_filter( 'wp_get_attachment_image_attributes', array( $this, 'add_responsive_images' ), 10, 3 );
        add_action( 'wp_enqueue_scripts', array( $this, 'enqueue_lazy_loading_script' ) );
    }

    /**
     * Optimize images when uploaded
     */
    public function optimize_uploaded_image( $metadata, $attachment_id ) {
        if ( ! isset( $metadata['file'] ) ) {
            return $metadata;
        }

        $upload_dir = wp_upload_dir();
        $file_path = $upload_dir['basedir'] . '/' . $metadata['file'];

        // Only optimize if file exists and is an image
        if ( ! file_exists( $file_path ) || ! wp_attachment_is_image( $attachment_id ) ) {
            return $metadata;
        }

        $this->compress_image( $file_path );

        // Optimize thumbnails
        if ( isset( $metadata['sizes'] ) && is_array( $metadata['sizes'] ) ) {
            foreach ( $metadata['sizes'] as $size => $size_data ) {
                $thumbnail_path = $upload_dir['basedir'] . '/' . dirname( $metadata['file'] ) . '/' . $size_data['file'];
                if ( file_exists( $thumbnail_path ) ) {
                    $this->compress_image( $thumbnail_path );
                }
            }
        }

        return $metadata;
    }

    /**
     * Compress image file
     */
    private function compress_image( $file_path ) {
        $image_info = getimagesize( $file_path );

        if ( ! $image_info ) {
            return false;
        }

        $mime_type = $image_info['mime'];

        switch ( $mime_type ) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg( $file_path );
                if ( $image ) {
                    imagejpeg( $image, $file_path, 85 ); // 85% quality
                    imagedestroy( $image );
                }
                break;

            case 'image/png':
                $image = imagecreatefrompng( $file_path );
                if ( $image ) {
                    imagepng( $image, $file_path, 6 ); // Compression level 6
                    imagedestroy( $image );
                }
                break;

            case 'image/webp':
                if ( function_exists( 'imagecreatefromwebp' ) ) {
                    $image = imagecreatefromwebp( $file_path );
                    if ( $image ) {
                        imagewebp( $image, $file_path, 85 ); // 85% quality
                        imagedestroy( $image );
                    }
                }
                break;
        }

        return true;
    }

    /**
     * Add responsive image attributes
     */
    public function add_responsive_images( $attr, $attachment, $size ) {
        if ( ! is_admin() ) {
            // Add loading attribute for lazy loading
            $attr['loading'] = 'lazy';

            // Add decoding attribute for better performance
            $attr['decoding'] = 'async';

            // Add fetchpriority for above-the-fold images
            if ( is_front_page() && $size === 'large' ) {
                $attr['fetchpriority'] = 'high';
                $attr['loading'] = 'eager'; // Don't lazy load hero images
            }
        }

        return $attr;
    }

    /**
     * Enqueue lazy loading script
     */
    public function enqueue_lazy_loading_script() {
        if ( ! is_admin() ) {
            wp_add_inline_script( 'jquery', $this->get_lazy_loading_script() );
        }
    }

    /**
     * Get lazy loading JavaScript
     */
    private function get_lazy_loading_script() {
        return "
        document.addEventListener('DOMContentLoaded', function() {
            // Intersection Observer for lazy loading
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            if (img.dataset.src) {
                                img.src = img.dataset.src;
                                img.classList.remove('lazy');
                                img.classList.add('loaded');
                                imageObserver.unobserve(img);
                            }
                        }
                    });
                });

                document.querySelectorAll('img[data-src]').forEach(img => {
                    imageObserver.observe(img);
                });
            }

            // Fallback for browsers without Intersection Observer
            else {
                document.querySelectorAll('img[data-src]').forEach(img => {
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    img.classList.add('loaded');
                });
            }
        });
        ";
    }

    /**
     * Generate WebP versions of images
     */
    public function generate_webp_version( $file_path ) {
        if ( ! function_exists( 'imagewebp' ) ) {
            return false;
        }

        $image_info = getimagesize( $file_path );
        if ( ! $image_info ) {
            return false;
        }

        $webp_path = preg_replace( '/\.(jpg|jpeg|png)$/i', '.webp', $file_path );

        switch ( $image_info['mime'] ) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg( $file_path );
                break;
            case 'image/png':
                $image = imagecreatefrompng( $file_path );
                break;
            default:
                return false;
        }

        if ( $image ) {
            $result = imagewebp( $image, $webp_path, 85 );
            imagedestroy( $image );
            return $result ? $webp_path : false;
        }

        return false;
    }
}

// Initialize the image optimizer
new TCL_Image_Optimizer();