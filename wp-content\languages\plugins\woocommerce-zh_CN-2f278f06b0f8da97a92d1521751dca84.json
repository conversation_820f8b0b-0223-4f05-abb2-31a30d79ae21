{"translation-revision-date": "2025-07-21 17:33:10+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "zh_CN"}, "Revenue is now reported from paid orders ✅": ["现在，收入基于付费订单进行报告 ✅"], "Analytics date settings": ["分析日期设置"], "We now collect orders in this table based on when the payment went through, rather than when they were placed. You can change this in <link>settings</link>.": ["现在，我们根据付款完成日期（而非订单下达日期）在这个表格中收集订单。 您可以在<link>设置</link>中更改此项。"], "Full refunds are not deducted from tax or net sales totals": ["全额退款不会从税款或净销售总额中扣除"], "Previous year:": ["上一年："], "Previous period:": ["前一个时段："], "All Revenue": ["所有收入"], "A sentence describing filters for Revenue. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ\u0004Revenue Matches <select/> Filters": ["与 <select/> 筛选条件匹配的收入"], "Got it": ["知道了"], "day": ["天"], "No data for the selected date range": ["所选日期范围内没有数据"], "No data for the current search": ["没有与当前搜索匹配的数据"], "order": ["订单"], "Advanced Filters": ["高级过滤器"], "Gross sales": ["销售总额"], "Returns": ["退货"], "Net sales": ["净销售额"], "Revenue": ["收入"], "Show": ["显示"], "Total sales": ["销售额"], "Shipping": ["配送"], "Date": ["日期"], "Taxes": ["税费"], "Orders": ["订单"], "Coupons": ["优惠券"]}}, "comment": {"reference": "assets/client/admin/chunks/analytics-report-revenue.js"}}