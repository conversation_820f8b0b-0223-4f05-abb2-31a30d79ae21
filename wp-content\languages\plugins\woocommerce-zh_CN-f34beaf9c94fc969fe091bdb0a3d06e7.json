{"translation-revision-date": "2025-07-21 17:33:10+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "zh_CN"}, "Now displaying a preview of the Hand-picked Products block.": ["现在将显示“精选产品”区块的预览。"], "An error has prevented the block from being updated.": ["发生错误，区块未能成功更新。"], "Edit selected products": ["编辑所选产品"], "Loading…": ["载入中…"], "The following error was returned": ["系统返回了以下错误"], "Align Buttons": ["对齐按钮"], "The following error was returned from the API": ["API 返回以下错误"], "Search results updated.": ["搜索结果已更新。"], "%d item selected": ["已选择 %d项"], "Search for items": ["搜索项"], "No results for %s": ["无 %s的相关结果"], "No items found.": ["未找到项。"], "Clear all selected items": ["清除所有选定项"], "Clear all": ["全部清除"], "Remove %s": ["移除 %s"], "Display a selection of hand-picked products in a grid.": ["在网格中显示一组精选产品。"], "Hand-picked Products": ["精选产品"], "%d product selected": ["已选择 %d个产品"], "Search for products to display": ["搜索要显示的产品"], "Clear all products": ["清除所有产品"], "Product search results updated.": ["产品搜索结果已更新。"], "Your store doesn't have any products.": ["您的商店没有任何产品。"], "Order By": ["排序依据"], "Done": ["完成"], "Buttons follow content.": ["按钮跟随内容。"], "Layout": ["布局"], "Buttons are aligned vertically.": ["按钮垂直对齐。"], "Columns": ["列"], "Add to Cart button": ["“加入购物车”按钮"], "Menu Order": ["菜单排序"], "Title - alphabetical": ["标题 - 按字母顺序"], "Sales - most first": ["销量 - 从多到少"], "Price - high to low": ["价格 - 从高到低"], "Price - low to high": ["价格 - 从低到高"], "Newness - newest first": ["新鲜度 - 从新到旧"], "Order products by": ["产品排序依据"], "Product price": ["产品价格"], "Product title": ["产品标题"], "Product rating": ["产品评级"], "Rating - highest first": ["评级 - 从高到低"], "Product image": ["产品图片"], "Content": ["内容"], "Products": ["产品"]}}, "comment": {"reference": "assets/client/blocks/handpicked-products.js"}}