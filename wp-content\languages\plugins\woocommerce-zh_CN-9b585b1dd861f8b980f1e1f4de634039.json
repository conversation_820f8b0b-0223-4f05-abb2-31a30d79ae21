{"translation-revision-date": "2025-07-21 17:33:10+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "zh_CN"}, "Something went wrong with the importation process.": ["导入进程出错。"], "Are you sure you want to reset all settings to default values?": ["是否确定要将所有设置重置为默认值？"], "Save settings": ["保存设置"], "Analytics settings": ["分析设置"], "Your settings have been successfully saved.": ["您的设置已成功保存。"], "You have unsaved changes. If you proceed, they will be lost.": ["您有未保存的更改。如果您继续进行，这些更改将会丢失。"], "There was an error saving your settings. Please try again.": ["保存您的设置时出错。 请重试。"], "Reset defaults": ["重置为默认值"], "There was a problem stopping your current import.": ["停止您当前的导入时出现问题。"], "There was a problem rebuilding your report data.": ["重新生成您的报告数据时出现问题。"], "There was a problem deleting your previous data.": ["删除您之前的数据时出现问题。"], "Orders and Refunds": ["订单和退款"], "Registered Customers": ["已注册客户"], "This tool populates historical analytics data by processing customers and orders created prior to activating WooCommerce Admin.": ["此工具通过处理在激活 WooCommerce Admin 之前创建的客户和订单来填充历史分析数据。"], "Skip previously imported customers and orders": ["跳过以前导入的客户和订单"], "Historical data from %s onward imported": ["自 %s 起的历史数据均已导入"], "All historical data imported": ["所有历史数据均已导入"], "Finalizing": ["完成"], "Importing Orders": ["导入订单"], "Importing Customers": ["导入客户"], "Initializing": ["初始化"], "Ready To Import": ["准备导入"], "Nothing To Import": ["没有要导入的内容"], "%(progress)s of %(total)s": ["%(progress)s/%(total)s"], "Imported %(label)s": ["已导入 %(label)s"], "Beginning on": ["开始时间"], "Import historical data": ["导入历史数据"], "Re-import Data": ["重新导入数据"], "Delete Previously Imported Data": ["删除以前导入的数据"], "Start": ["开始"], "Navigating away from this page will not affect the import.": ["离开此页面不会影响导入。"], "Imported data will not be lost if the import is stopped.": ["如果停止导入，已导入的数据将不会丢失。"], "Stop Import": ["停止导入"], "MM/DD/YYYY": ["MM/DD/YYYY"], "Status:": ["状态："]}}, "comment": {"reference": "assets/client/admin/chunks/analytics-settings.js"}}