{"translation-revision-date": "2025-07-21 17:33:10+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "zh_CN"}, "Attribution": ["归属"], "Analytics date settings": ["分析日期设置"], "We now collect orders in this table based on when the payment went through, rather than when they were placed. You can change this in <link>settings</link>.": ["现在，我们根据付款完成日期（而非订单下达日期）在这个表格中收集订单。 您可以在<link>设置</link>中更改此项。"], "Orders are now reported based on the payment dates ✅": ["现在，订单基于付款日期进行报告 ✅"], "Previous year:": ["上一年："], "Item sold": ["已售项目"], "Previous period:": ["前一个时段："], "Got it": ["知道了"], " Customer": ["客户"], "net sales": ["净销售额"], "Customer type": ["客户类型"], "Product(s)": ["产品"], "%1$s× %2$s": ["%1$s× %2$s"], "Order #": ["订单号"], "Net sales": ["净销售额"], "Items sold": ["售出项目"], "Order Number": ["订单编号"], "Coupon(s)": ["优惠券"], "Coupon": ["优惠券"], "Customer": ["客户"], "Products": ["产品"], "Date": ["日期"], "Status": ["状态"], "Order": ["订单"], "Orders": ["订单"], "Coupons": ["优惠券"], "Product": ["产品"]}}, "comment": {"reference": "assets/client/admin/chunks/analytics-report-orders.js"}}