{"translation-revision-date": "2025-07-21 17:33:10+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "zh_CN"}, "Accept cash on delivery if the order is virtual": ["虚拟订单可使用货到付款"], "Select shipping methods for which this payment method is enabled.": ["选择已启用此付款方式的配送方式。"], "Cash on delivery payments": ["货到付款"], "Payment method name that the customer will see during checkout.": ["客户在结账时会看到的付款方式名称。"], "Enable and customise": ["启用并自定义"], "Failed to update settings": ["设置更新失败"], "Settings updated successfully": ["设置更新成功"], "Enable cash on delivery payments": ["启用货到付款"], "Choose how you want to present cash on delivery payments to your customers during checkout.": ["选择您希望在结账时向客户展示货到付款选项的方式。"], "Payment method description that the customer will see during checkout.": ["客户在结账时会看到的付款方式描述。"], "Title": ["标题"], "Instructions": ["说明"], "Accept for virtual orders": ["应用于虚拟订单"], "Instructions that will be added to the thank you page and emails.": ["说明将会被显示在订单确认页面和相关邮件中"], "Enable for shipping methods": ["启用此配送方式"], "Save changes": ["保存更改"], "Description": ["描述"]}}, "comment": {"reference": "assets/client/admin/chunks/settings-payments-cod.js"}}