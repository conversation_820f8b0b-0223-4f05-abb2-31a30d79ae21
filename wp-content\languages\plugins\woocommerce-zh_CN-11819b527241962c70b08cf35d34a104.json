{"translation-revision-date": "2025-07-21 17:33:10+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "zh_CN"}, "You can learn more about the benefits of switching to blocks, compatibility with extensions, and how to switch back to shortcodes <a>in our documentation</a>.": ["您可以<a>在我们的文档中</a>进一步了解切换为区块的效用、与扩展程序的兼容性以及切换回短代码的方法。"], "Classic Shortcode Placeholder": ["经典短代码占位符"], "Classic shortcode transformed to blocks.": ["转换为区块的经典短代码。"], "This block will render the classic cart shortcode. You can optionally transform it into blocks for more control over the cart experience.": ["该区块将呈现经典购物车短代码。 您可以选择性地将其转换为区块，以便更好地把控购物车体验。"], "Classic Cart": ["经典购物车"], "This block will render the classic checkout shortcode. You can optionally transform it into blocks for more control over the checkout experience.": ["该区块将呈现经典结账短代码。 您可以选择性地将其转换为区块，以便更好地把控结账体验。"], "Classic Checkout": ["经典结账"], "Renders the classic checkout shortcode.": ["呈现经典结账短代码。"], "Checkout Cart": ["结账购物车"], "Renders the classic cart shortcode.": ["呈现经典购物车短代码。"], "Cart Shortcode": ["购物车短代码"], "Transform into blocks": ["转换为区块"], "Undo": ["撤消"], "Learn more": ["了解更多"], "WooCommerce": ["WooCommerce"]}}, "comment": {"reference": "assets/client/blocks/classic-shortcode.js"}}