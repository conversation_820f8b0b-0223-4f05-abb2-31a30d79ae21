{"translation-revision-date": "2024-10-02 16:14:23+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "Align the last block to the bottom.": ["Align the last block to the bottom."], "Align the last block to the bottom": ["Align the last block to the bottom"], "An error has prevented the block from being updated.": ["An error has prevented the block from being updated."], "Stock status \"%s\" hidden.": ["Stock status \"%s\" hidden."], "Stock status \"%s\" visible.": ["Stock status \"%s\" visible."], "%1$s, has %2$d review": ["%1$s, has %2$d review", "%1$s, has %2$d reviews"], "%1$s, has %2$d product": ["%1$s, has %2$d product", "%1$s, has %2$d products"], "Loading…": ["Loading…"], "This block shows on-sale products. There are currently no discounted products in your store.": ["This block shows on-sale products. There are currently no discounted products in your store."], "Display a grid of products currently on sale.": ["Display a grid of products currently on sale."], "The last inner block will follow other content.": ["The last inner block will follow other content."], "The following error was returned": ["The following error was returned"], "The following error was returned from the API": ["The following error was returned from the API"], "%d review": ["%d review", "%d reviews"], "Search results updated.": ["Search results updated."], "%d item selected": ["%d item selected", "%d items selected"], "Search for items": ["Search for items"], "No results for %s": ["No results for %s"], "No items found.": ["No items found."], "Clear all selected items": ["Clear all selected items"], "Clear all": ["Clear all"], "Remove %s": ["Remove %s"], "%d category selected": ["%d category selected", "%d categories selected"], "All selected categories": ["All selected categories"], "Any selected categories": ["Any selected categories"], "Category search results updated.": ["Category search results updated."], "Clear all product categories": ["Clear all product categories"], "Columns": ["Columns"], "Layout": ["Layout"], "Pick at least two categories to use this setting.": ["Pick at least two categories to use this setting."], "Rows": ["Rows"], "Search for product categories": ["Search for product categories"], "Your store doesn't have any product categories.": ["Your store doesn't have any product categories."], "On Sale Products": ["On Sale Products"], "Filter by Product Category": ["Filter by Product Category"], "Order By": ["Order By"], "Add to Cart button": ["Add to Basket button"], "Display products matching": ["Display products matching"], "Newness - newest first": ["Newness - newest first"], "Order products by": ["Order products by"], "Price - high to low": ["Price - high to low"], "Price - low to high": ["Price - low to high"], "Product price": ["Product price"], "Product rating": ["Product rating"], "Product title": ["Product title"], "Rating - highest first": ["Rating - highest first"], "Sales - most first": ["Sales - most first"], "Title - alphabetical": ["Title - alphabetical"], "Menu Order": ["Menu Order"], "Filter by stock status": ["Filter by stock status"], "Product image": ["Product image"], "%d product": ["%d product", "%d products"], "Content": ["Content"], "Product Categories": ["Product Categories"], "WooCommerce": ["WooCommerce"]}}, "comment": {"reference": "assets/client/blocks/product-on-sale.js"}}