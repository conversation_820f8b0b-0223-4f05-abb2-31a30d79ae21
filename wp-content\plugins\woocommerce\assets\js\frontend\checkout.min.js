jQuery(function(e){if("undefined"==typeof wc_checkout_params)return!1;e.blockUI.defaults.overlayCSS.cursor="default";var o={updateTimer:!1,dirtyInput:!1,selectedPaymentMethod:!1,xhr:!1,$order_review:e("#order_review"),$checkout_form:e("form.checkout"),init:function(){e(document.body).on("update_checkout",this.update_checkout),e(document.body).on("init_checkout",this.init_checkout),this.$checkout_form.on("click",'input[name="payment_method"]',this.payment_method_selected),e(document.body).hasClass("woocommerce-order-pay")&&(this.$order_review.on("click",'input[name="payment_method"]',this.payment_method_selected),this.$order_review.on("submit",this.submitOrder),this.$order_review.attr("novalidate","novalidate")),this.$checkout_form.attr("novalidate","novalidate"),this.$checkout_form.on("submit",this.submit),this.$checkout_form.on("input validate change focusout",".input-text, select, input:checkbox",this.validate_field),this.$checkout_form.on("update",this.trigger_update_checkout),this.$checkout_form.on("change",'select.shipping_method, input[name^="shipping_method"], #ship-to-different-address input, .update_totals_on_change select, .update_totals_on_change input[type="radio"], .update_totals_on_change input[type="checkbox"]',this.trigger_update_checkout),this.$checkout_form.on("change",".address-field select",this.input_changed),this.$checkout_form.on("change",".address-field input.input-text, .update_totals_on_change input.input-text",this.maybe_input_changed),this.$checkout_form.on("keydown",".address-field input.input-text, .update_totals_on_change input.input-text",this.queue_update_checkout),this.$checkout_form.on("change","#ship-to-different-address input",this.ship_to_different_address),this.$checkout_form.find("#ship-to-different-address input").trigger("change"),this.init_payment_methods(),"1"===wc_checkout_params.is_checkout&&e(document.body).trigger("init_checkout"),"yes"===wc_checkout_params.option_guest_checkout&&e("input#createaccount").on("change",this.toggle_create_account).trigger("change")},init_payment_methods:function(){var t=e(".woocommerce-checkout").find('input[name="payment_method"]');1===t.length&&t.eq(0).hide(),o.selectedPaymentMethod&&e("#"+o.selectedPaymentMethod).prop("checked",!0),0===t.filter(":checked").length&&t.eq(0).prop("checked",!0);var r=t.filter(":checked").eq(0).prop("id");t.length>1&&e('div.payment_box:not(".'+r+'")').filter(":visible").slideUp(0),t.filter(":checked").eq(0).trigger("click")},get_payment_method:function(){return o.$checkout_form.find('input[name="payment_method"]:checked').val()},payment_method_selected:function(t){if(t.stopPropagation(),e(".payment_methods input.input-radio").length>1){var r=e("div.payment_box."+e(this).attr("ID")),i=e(this).is(":checked");i&&!r.is(":visible")&&(e("div.payment_box").filter(":visible").slideUp(230),i&&r.slideDown(230))}else e("div.payment_box").show();e(this).data("order_button_text")?e("#place_order").text(e(this).data("order_button_text")):e("#place_order").text(e("#place_order").data("value"));var c=e('.woocommerce-checkout input[name="payment_method"]:checked').attr("id");c!==o.selectedPaymentMethod&&e(document.body).trigger("payment_method_selected"),o.selectedPaymentMethod=c},toggle_create_account:function(){e("div.create-account").hide(),e(this).is(":checked")&&(e("#account_password").val("").trigger("change"),e("div.create-account").slideDown())},init_checkout:function(){e(document.body).trigger("update_checkout")},maybe_input_changed:function(e){o.dirtyInput&&o.input_changed(e)},input_changed:function(e){o.dirtyInput=e.target,o.maybe_update_checkout()},queue_update_checkout:function(e){if(9===(e.keyCode||e.which||0))return!0;o.dirtyInput=this,o.reset_update_checkout_timer(),o.updateTimer=setTimeout(o.maybe_update_checkout,"1000")},trigger_update_checkout:function(t){o.reset_update_checkout_timer(),o.dirtyInput=!1,e(document.body).trigger("update_checkout",{current_target:t?t.currentTarget:null})},maybe_update_checkout:function(){var t=!0;if(e(o.dirtyInput).length){var r=e(o.dirtyInput).closest("div").find(".address-field.validate-required");r.length&&r.each(function(){""===e(this).find("input.input-text").val()&&(t=!1)})}t&&o.trigger_update_checkout()},ship_to_different_address:function(){e("div.shipping_address").hide(),e(this).is(":checked")&&e("div.shipping_address").slideDown()},reset_update_checkout_timer:function(){clearTimeout(o.updateTimer)},is_valid_json:function(e){try{var o=JSON.parse(e);return o&&"object"==typeof o}catch(t){return!1}},validate_field:function(o){var t=e(this),r=t.closest(".form-row"),i=!0,c=r.is(".validate-required"),n=r.is(".validate-email"),a=r.is(".validate-phone"),u="",d=o.type;"input"===d&&(t.removeAttr("aria-invalid").removeAttr("aria-describedby"),r.find(".checkout-inline-error-message").remove(),r.removeClass("woocommerce-invalid woocommerce-invalid-required-field woocommerce-invalid-email woocommerce-invalid-phone woocommerce-validated")),"validate"!==d&&"change"!==d&&"focusout"!==d||(c&&("checkbox"===t.attr("type")&&!t.is(":checked")||""===t.val())&&(t.attr("aria-invalid","true"),r.removeClass("woocommerce-validated").addClass("woocommerce-invalid woocommerce-invalid-required-field"),i=!1),n&&t.val()&&((u=new RegExp(/^([a-z\d!#$%&'*+\-\/=?^_`{|}~\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+(\.[a-z\d!#$%&'*+\-\/=?^_`{|}~\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+)*|"((([ \t]*\r\n)?[ \t]+)?([\x01-\x08\x0b\x0c\x0e-\x1f\x7f\x21\x23-\x5b\x5d-\x7e\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]|\\[\x01-\x09\x0b\x0c\x0d-\x7f\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))*(([ \t]*\r\n)?[ \t]+)?")@(([a-z\d\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]|[a-z\d\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF][a-z\d\-._~\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]*[a-z\d\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])\.)+([a-z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]|[a-z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF][a-z\d\-._~\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]*[0-9a-z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])\.?$/i)).test(t.val())||(t.attr("aria-invalid","true"),r.removeClass("woocommerce-validated").addClass("woocommerce-invalid woocommerce-invalid-email"),i=!1)),a&&(u=new RegExp(/[\s\#0-9_\-\+\/\(\)\.]/g),0<t.val().replace(u,"").length&&(t.attr("aria-invalid","true"),r.removeClass("woocommerce-validated").addClass("woocommerce-invalid woocommerce-invalid-phone"),i=!1)),i&&(t.removeAttr("aria-invalid").removeAttr("aria-describedby"),r.find(".checkout-inline-error-message").remove(),r.removeClass("woocommerce-invalid woocommerce-invalid-required-field woocommerce-invalid-email woocommerce-invalid-phone").addClass("woocommerce-validated")))},update_checkout:function(e,t){o.reset_update_checkout_timer(),o.updateTimer=setTimeout(o.update_checkout_action,"5",t)},update_checkout_action:function(t){if(o.xhr&&o.xhr.abort(),0!==e("form.checkout").length){t=void 0!==t?t:{update_shipping_method:!0};var r=e("#billing_country").val(),i=e("#billing_state").val(),c=e(":input#billing_postcode").val(),n=e("#billing_city").val(),a=e(":input#billing_address_1").val(),u=e(":input#billing_address_2").val(),d=r,s=i,m=c,l=n,p=a,h=u,_=e(o.$checkout_form).find(".address-field.validate-required:visible"),f=!0;_.length&&_.each(function(){""===e(this).find(":input").val()&&(f=!1)}),e("#ship-to-different-address").find("input").is(":checked")&&(d=e("#shipping_country").val(),s=e("#shipping_state").val(),m=e(":input#shipping_postcode").val(),l=e("#shipping_city").val(),p=e(":input#shipping_address_1").val(),h=e(":input#shipping_address_2").val());var g={security:wc_checkout_params.update_order_review_nonce,payment_method:o.get_payment_method(),country:r,state:i,postcode:c,city:n,address:a,address_2:u,s_country:d,s_state:s,s_postcode:m,s_city:l,s_address:p,s_address_2:h,has_full_address:f,post_data:e("form.checkout").serialize()};if(!1!==t.update_shipping_method){var v={};e('select.shipping_method, input[name^="shipping_method"][type="radio"]:checked, input[name^="shipping_method"][type="hidden"]').each(function(){v[e(this).data("index")]=e(this).val()}),g.shipping_method=v}e(".woocommerce-checkout-payment, .woocommerce-checkout-review-order-table").block({message:null,overlayCSS:{background:"#fff",opacity:.6}}),o.xhr=e.ajax({type:"POST",url:wc_checkout_params.wc_ajax_url.toString().replace("%%endpoint%%","update_order_review"),data:g,success:function(r){if(r&&!0===r.reload)window.location.reload();else{e(".woocommerce-NoticeGroup-updateOrderReview").remove();var i=e("#terms").prop("checked"),c={};if(e(".payment_box :input").each(function(){var o=e(this).attr("id");o&&(-1!==e.inArray(e(this).attr("type"),["checkbox","radio"])?c[o]=e(this).prop("checked"):c[o]=e(this).val())}),r&&r.fragments&&(e.each(r.fragments,function(t,r){o.fragments&&o.fragments[t]===r||e(t).replaceWith(r),e(t).unblock()}),o.fragments=r.fragments),i&&e("#terms").prop("checked",!0),e.isEmptyObject(c)||e(".payment_box :input").each(function(){var o=e(this).attr("id");o&&(-1!==e.inArray(e(this).attr("type"),["checkbox","radio"])?e(this).prop("checked",c[o]).trigger("change"):-1!==e.inArray(e(this).attr("type"),["select"])?e(this).val(c[o]).trigger("change"):null!==e(this).val()&&0===e(this).val().length&&e(this).val(c[o]).trigger("change"))}),r&&"failure"===r.result){var n=e("form.checkout");e(".woocommerce-error, .woocommerce-message, .is-error, .is-success").remove(),r.messages?n.prepend('<div class="woocommerce-NoticeGroup woocommerce-NoticeGroup-updateOrderReview">'+r.messages+"</div>"):n.prepend(r),n.find(".input-text, select, input:checkbox").trigger("validate").trigger("blur"),o.scroll_to_notices()}o.init_payment_methods(),r&&"success"===r.result&&t.current_target&&-1!==t.current_target.id.indexOf("shipping_method")&&document.getElementById(t.current_target.id).focus(),e(document.body).trigger("updated_checkout",[r])}}})}},handleUnloadEvent:function(e){return-1===navigator.userAgent.indexOf("MSIE")&&!document.documentMode||(e.preventDefault(),undefined)},attachUnloadEventsOnSubmit:function(){e(window).on("beforeunload",this.handleUnloadEvent)},detachUnloadEventsOnSubmit:function(){e(window).off("beforeunload",this.handleUnloadEvent)},blockOnSubmit:function(e){1!==e.data("blockUI.isBlocked")&&e.block({message:null,overlayCSS:{background:"#fff",opacity:.6}})},submitOrder:function(){o.blockOnSubmit(e(this))},submit:function(){o.reset_update_checkout_timer();var t=e(this);return!t.is(".processing")&&(!1!==t.triggerHandler("checkout_place_order",[o])&&!1!==t.triggerHandler("checkout_place_order_"+o.get_payment_method(),[o])&&(t.addClass("processing"),o.blockOnSubmit(t),o.attachUnloadEventsOnSubmit(),e.ajaxSetup({dataFilter:function(e,t){if("json"!==t)return e;if(o.is_valid_json(e))return e;var r=e.match(/{"result.*}/);return null===r?console.log("Unable to fix malformed JSON #1"):o.is_valid_json(r[0])?(console.log("Fixed malformed JSON. Original:"),console.log(e),e=r[0]):console.log("Unable to fix malformed JSON #2"),e}}),e.ajax({type:"POST",url:wc_checkout_params.checkout_url,data:t.serialize(),dataType:"json",success:function(r){o.detachUnloadEventsOnSubmit(),e(".checkout-inline-error-message").remove();try{if("success"!==r.result||!1===t.triggerHandler("checkout_place_order_success",[r,o]))throw"failure"===r.result?"Result failure":"Invalid response";-1===r.redirect.indexOf("https://")||-1===r.redirect.indexOf("http://")?window.location=r.redirect:window.location=decodeURI(r.redirect)}catch(a){if(!0===r.reload)return void window.location.reload();if(!0===r.refresh&&e(document.body).trigger("update_checkout"),r.messages){var i=e(r.messages).removeAttr("role").attr("tabindex","-1"),c=o.wrapMessagesInsideLink(i),n=e('<div role="alert"></div>').append(c);o.submit_error(n.prop("outerHTML")),o.show_inline_errors(i)}else o.submit_error('<div class="woocommerce-error">'+wc_checkout_params.i18n_checkout_error+"</div>")}},error:function(e,t,r){o.detachUnloadEventsOnSubmit();var i=r;"object"==typeof wc_checkout_params&&null!==wc_checkout_params&&wc_checkout_params.hasOwnProperty("i18n_checkout_error")&&"string"==typeof wc_checkout_params.i18n_checkout_error&&""!==wc_checkout_params.i18n_checkout_error.trim()&&(i=wc_checkout_params.i18n_checkout_error),o.submit_error('<div class="woocommerce-error">'+i+"</div>")}})),!1)},submit_error:function(t){e(".woocommerce-NoticeGroup-checkout, .woocommerce-error, .woocommerce-message, .is-error, .is-success").remove(),o.$checkout_form.prepend('<div class="woocommerce-NoticeGroup woocommerce-NoticeGroup-checkout">'+t+"</div>"),o.$checkout_form.removeClass("processing").unblock(),o.$checkout_form.find(".input-text, select, input:checkbox").trigger("validate").trigger("blur"),o.scroll_to_notices(),o.$checkout_form.find('.woocommerce-error[tabindex="-1"], .wc-block-components-notice-banner.is-error[tabindex="-1"]').focus(),e(document.body).trigger("checkout_error",[t])},wrapMessagesInsideLink:function(o){return o.find("li[data-id]").each(function(){const o=e(this),t=o.attr("data-id");if(t){const r=e("<a>",{href:"#"+t,html:o.html()});o.empty().append(r)}}),o},show_inline_errors:function(o){o.find("li[data-id]").each(function(){const o=e(this),t=o.attr("data-id"),r=e("#"+t);if(1===r.length){const e=t+"_description",i=o.text().trim(),c=r.closest(".form-row"),n=document.createElement("p");n.id=e,n.className="checkout-inline-error-message",n.textContent=i,c&&n.textContent.length>0&&c.append(n),r.attr("aria-describedby",e),r.attr("aria-invalid","true")}})},scroll_to_notices:function(){var o=e(".woocommerce-NoticeGroup-updateOrderReview, .woocommerce-NoticeGroup-checkout");o.length||(o=e("form.checkout")),e.scroll_to_notices(o)}},t={init:function(){e(document.body).on("click","a.showcoupon",this.show_coupon_form),e(document.body).on("click",".woocommerce-remove-coupon",this.remove_coupon),e(document.body).on("keydown",".woocommerce-remove-coupon",this.on_keydown_remove_coupon),e(document.body).on("change input","#coupon_code",this.remove_coupon_error),e("form.checkout_coupon").hide().on("submit",this.submit.bind(this))},show_coupon_form:function(){var o=e(this);return e(".checkout_coupon").slideToggle(400,function(){var t=e(this);t.is(":visible")?(o.attr("aria-expanded","true"),t.find(":input:eq(0)").trigger("focus")):o.attr("aria-expanded","false")}),!1},show_coupon_error:function(o,t){if(0!==t.length){this.remove_coupon_error();var r=e(e.parseHTML(o)).text().trim();""!==r&&(t.find("#coupon_code").focus().addClass("has-error").attr("aria-invalid","true").attr("aria-describedby","coupon-error-notice"),e("<span>",{"class":"coupon-error-notice",id:"coupon-error-notice",role:"alert",text:r}).appendTo(t))}},remove_coupon_error:function(){var o=e("#coupon_code");0!==o.length&&o.removeClass("has-error").removeAttr("aria-invalid").removeAttr("aria-describedby").next(".coupon-error-notice").remove()},clear_coupon_input:function(){e("#coupon_code").val("").removeClass("has-error").removeAttr("aria-invalid").removeAttr("aria-describedby").next(".coupon-error-notice").remove()},submit:function(t){var r=e(t.currentTarget),i=r.find("#coupon_code"),c=this;if(c.remove_coupon_error(),r.is(".processing"))return!1;r.addClass("processing").block({message:null,overlayCSS:{background:"#fff",opacity:.6}});var n={security:wc_checkout_params.apply_coupon_nonce,coupon_code:r.find('input[name="coupon_code"]').val(),billing_email:o.$checkout_form.find('input[name="billing_email"]').val()};return e.ajax({type:"POST",url:wc_checkout_params.wc_ajax_url.toString().replace("%%endpoint%%","apply_coupon"),data:n,success:function(o){e(".woocommerce-error, .woocommerce-message, .is-error, .is-success, .checkout-inline-error-message").remove(),r.removeClass("processing").unblock(),o&&(-1===o.indexOf("woocommerce-error")&&-1===o.indexOf("is-error")?(r.slideUp(400,function(){e("a.showcoupon").attr("aria-expanded","false"),r.before(o)}),c.clear_coupon_input()):c.show_coupon_error(o,i.parent()),e(document.body).trigger("applied_coupon_in_checkout",[n.coupon_code]),e(document.body).trigger("update_checkout",{update_shipping_method:!1}))},dataType:"html"}),!1},remove_coupon:function(o){o.preventDefault();var r=e(this).parents(".woocommerce-checkout-review-order"),i=e(this).data("coupon");r.addClass("processing").block({message:null,overlayCSS:{background:"#fff",opacity:.6}});var c={security:wc_checkout_params.remove_coupon_nonce,coupon:i};e.ajax({type:"POST",url:wc_checkout_params.wc_ajax_url.toString().replace("%%endpoint%%","remove_coupon"),data:c,success:function(o){e(".woocommerce-error, .woocommerce-message, .is-error, .is-success").remove(),r.removeClass("processing").unblock(),o&&(e("form.woocommerce-checkout").before(o),e(document.body).trigger("removed_coupon_in_checkout",[c.coupon]),e(document.body).trigger("update_checkout",{update_shipping_method:!1}),t.clear_coupon_input(),e("form.checkout_coupon").slideUp(400,function(){e("a.showcoupon").attr("aria-expanded","false")}))},error:function(e){wc_checkout_params.debug_mode&&console.log(e.responseText)},dataType:"html"})},on_keydown_remove_coupon:function(o){" "===o.key&&(o.preventDefault(),e(this).trigger("click"))}},r={init:function(){e(document.body).on("click","a.showlogin",this.show_login_form)},show_login_form:function(){var o=e("form.login, form.woocommerce-form--login");return o.is(":visible")?o.slideToggle({duration:400}):o.slideToggle({duration:400,complete:function(){o.is(":visible")&&e("html, body").animate({scrollTop:o.offset().top-50},300)}}),!1}},i={init:function(){e(document.body).on("click","a.woocommerce-terms-and-conditions-link",this.toggle_terms)},toggle_terms:function(){if(e(".woocommerce-terms-and-conditions").length)return e(".woocommerce-terms-and-conditions").slideToggle(function(){var o=e(".woocommerce-terms-and-conditions-link");e(".woocommerce-terms-and-conditions").is(":visible")?(o.addClass("woocommerce-terms-and-conditions-link--open"),o.removeClass("woocommerce-terms-and-conditions-link--closed")):(o.removeClass("woocommerce-terms-and-conditions-link--open"),o.addClass("woocommerce-terms-and-conditions-link--closed"))}),!1}};o.init(),t.init(),r.init(),i.init()});