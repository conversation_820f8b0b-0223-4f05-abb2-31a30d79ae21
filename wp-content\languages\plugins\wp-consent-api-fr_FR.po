# Translation of Plugins - WP Consent API - Stable (latest release) in French (France)
# This file is distributed under the same license as the Plugins - WP Consent API - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-01-27 08:22:37+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: fr\n"
"Project-Id-Version: Plugins - WP Consent API - Stable (latest release)\n"

#: api.php:283
msgid "Missing consent category. A functional, preferences, statistics-anonymous, statistics or marketing category should be passed when using wp_setcookie."
msgstr "Catégorie de consentement manquante. Une catégorie fonctionnelle, de préférences, de statistiques anonymes, de statistiques ou de marketing doit être transmise lors de l’utilisation de wp_setcookie."

#. Description of the plugin
#: wp-consent-api.php
msgid "Consent Level API to read and register the current consent level for cookie management and improving compliance with privacy laws."
msgstr "Consent Level API pour lire et enregistrer le niveau de consentement actuel pour la gestion des cookies et l’amélioration de la conformité avec les lois sur la protection de la vie privée."

#: site-health.php:113
msgid "Not all plugins have declared to follow Consent API guidelines. Please contact the developer."
msgstr "Toutes les extensions n’ont pas déclaré suivre les directives de l’API du consentement. Veuillez contacter le développeur ou la développeuse."

#: site-health.php:112
msgid "One or more plugins are not conforming to the Consent API."
msgstr "Une ou plusieurs extensions ne sont pas conformes à l’API de consentement."

#: site-health.php:104
msgid "All plugins have declared in their code that they are following the guidelines from the WP Consent API. When used in combination with a Cookie Management plugin, this will improve compliancy for your site."
msgstr "Toutes les extensions ont déclaré dans leur code qu’elles suivent les directives de l’API WP Consent. Utilisé en combinaison avec une extension de gestion des cookies, il améliorera la conformité de votre site."

#: site-health.php:99
msgid "Compliance"
msgstr "Conformité"

#: site-health.php:96
msgid "All plugins have declared to use the Consent API"
msgstr "Toutes les extensions ont déclaré utiliser l’API de consentement."

#: site-health.php:69
msgid "WP Consent API test"
msgstr "Test de l’API WP Consent"

#: wp-consent-api.php:58
msgid "This plugin requires WordPress 5.0 or higher"
msgstr "Cette extension nécessite WordPress version %s ou suivantes"

#: wp-consent-api.php:53
msgid "This plugin requires PHP 5.6 or higher"
msgstr "Cette extension nécessite PHP 5.6 ou plus."

#. translators: %s the name of the PHP Class used.
#: config.php:34 cookie-info.php:56 site-health.php:51
msgid "%s is a singleton class and you cannot create a second instance."
msgstr "%s est une classe unique et vous ne pouvez pas créer une deuxième instance."

#. Author URI of the plugin
#: wp-consent-api.php
msgid "https://github.com/rlankhorst/wp-consent-level-api"
msgstr "https://github.com/rlankhorst/wp-consent-level-api"

#. Author of the plugin
#: wp-consent-api.php
msgid "RogierLankhorst"
msgstr "RogierLankhorst"

#. Plugin URI of the plugin
#: wp-consent-api.php
msgid "https://wordpress.org/plugins/wp-consent-api"
msgstr "https://fr.wordpress.org/plugins/wp-consent-api"

#. Plugin Name of the plugin
#: wp-consent-api.php
msgid "WP Consent API"
msgstr "WP Consent API"