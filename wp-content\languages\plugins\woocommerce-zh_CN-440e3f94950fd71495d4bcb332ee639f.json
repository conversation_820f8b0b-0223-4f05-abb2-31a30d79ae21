{"translation-revision-date": "2025-07-21 17:33:10+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "zh_CN"}, "Upgrade to Product Collection": ["升级为“产品集合”"], "Upgrade all Products (Beta) blocks on this page to <strongText /> for more features!": ["将此页面上的所有“产品”（测试版）区块升级为 <strongText />，以获得更多功能！"], "Product Collection": ["产品集合"], "Pick some products": ["挑选一些产品"], "Manage attributes": ["管理属性"], "Related Products Controls": ["相关产品控件"], "Display related products.": ["显示相关产品。"], "Product template": ["产品模板"], "Contains the block elements used to render a product, like its name, featured image, rating, and more.": ["包含用于渲染产品的区块元素，如产品名称、特色图片、评分等。"], "Products (Beta)": ["产品（测试版）"], "A block that displays a selection of products in your store.": ["用于显示您商店中的产品选择的区块。"], "Toggle to use the global query context that is set with the current template, such as variations of the product catalog or search. Disable to customize the filtering independently.": ["切换为使用以当前模板设置的全局查询上下文，例如产品目录或搜索的变体。 禁用以独立定制筛选。"], "Inherit query from template": ["沿用模板中的查询"], "Choose among these pre-sets": ["在这些预设中选择"], "Arrange products by popular pre-sets.": ["按照热门预设排列产品。"], "Popular Filters": ["热门过滤器"], "Top Rated": ["评分最高"], "Best Selling": ["最畅销"], "Newest": ["最新"], "Sorted by title": ["按标题排序"], "Sale status": ["销售状态"], "Show only products on sale": ["仅显示促销产品"], "An error has prevented the block from being updated.": ["发生错误，区块未能成功更新。"], "%d term": ["%d 个字词"], "%1$s, has %2$d product": ["%1$s，有%2$d产品"], "%1$s, has %2$d term": ["%1$s，有%2$d项"], "Loading…": ["载入中…"], "Advanced Filters": ["高级过滤器"], "Product Summary": ["产品摘要"], "Display a short description about a product.": ["显示有关产品的简短描述。"], "The following error was returned": ["系统返回了以下错误"], "The following error was returned from the API": ["API 返回以下错误"], "Search results updated.": ["搜索结果已更新。"], "%d item selected": ["已选择 %d项"], "Search for items": ["搜索项"], "No results for %s": ["无 %s的相关结果"], "No items found.": ["未找到项。"], "Clear all selected items": ["清除所有选定项"], "Clear all": ["全部清除"], "Remove %s": ["移除 %s"], "Hand-picked Products": ["精选产品"], "All selected attributes": ["所有选定属性"], "Any selected attributes": ["任何选定属性"], "Pick at least two attributes to use this setting.": ["选择至少两个属性以使用此设置。"], "Product attribute search results updated.": ["已更新产品属性搜索结果。"], "%d attribute selected": ["已选择 %d个属性"], "Search for product attributes": ["搜索产品属性"], "Your store doesn't have any product attributes.": ["您的商店没有任何产品属性。"], "Clear all product attributes": ["清除所有产品属性"], "Display products matching": ["显示产品匹配"], "Product Attributes": ["产品属性"], "Related products": ["相关产品"], "%d product": ["%d 个产品"], "Stock status": ["库存状态"], "Attributes": ["属性"]}}, "comment": {"reference": "assets/client/blocks/product-query.js"}}