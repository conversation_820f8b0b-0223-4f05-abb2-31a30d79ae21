<?php
/**
 * Flatsome Child Theme Functions
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Enqueue parent and child theme styles
function flatsome_child_enqueue_styles() {
    // Enqueue parent theme style
    wp_enqueue_style( 'flatsome-parent-style', get_template_directory_uri() . '/style.css' );

    // Enqueue child theme style
    wp_enqueue_style( 'flatsome-child-style',
        get_stylesheet_directory_uri() . '/style.css',
        array( 'flatsome-parent-style' ),
        wp_get_theme()->get('Version')
    );
}
add_action( 'wp_enqueue_scripts', 'flatsome_child_enqueue_styles' );

// Performance optimizations
function flatsome_child_performance_optimizations() {
    // Remove unnecessary WordPress features
    remove_action( 'wp_head', 'wp_generator' );
    remove_action( 'wp_head', 'wlwmanifest_link' );
    remove_action( 'wp_head', 'rsd_link' );
    remove_action( 'wp_head', 'wp_shortlink_wp_head' );

    // Remove emoji scripts and styles
    remove_action( 'wp_head', 'print_emoji_detection_script', 7 );
    remove_action( 'wp_print_styles', 'print_emoji_styles' );
    remove_action( 'admin_print_scripts', 'print_emoji_detection_script' );
    remove_action( 'admin_print_styles', 'print_emoji_styles' );

    // Disable embeds
    remove_action( 'wp_head', 'wp_oembed_add_discovery_links' );
    remove_action( 'wp_head', 'wp_oembed_add_host_js' );
}
add_action( 'init', 'flatsome_child_performance_optimizations' );

// Add lazy loading to images
function flatsome_child_add_lazy_loading( $attr, $attachment, $size ) {
    if ( ! is_admin() ) {
        $attr['loading'] = 'lazy';
        $attr['class'] = isset( $attr['class'] ) ? $attr['class'] . ' lazy' : 'lazy';
    }
    return $attr;
}
add_filter( 'wp_get_attachment_image_attributes', 'flatsome_child_add_lazy_loading', 10, 3 );

// Optimize database queries
function flatsome_child_limit_post_revisions( $num, $post ) {
    return 3;
}
add_filter( 'wp_revisions_to_keep', 'flatsome_child_limit_post_revisions', 10, 2 );

// Add preload for critical resources
function flatsome_child_add_preload_links() {
    // Preload critical CSS
    echo '<link rel="preload" href="' . get_stylesheet_directory_uri() . '/style.css" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">' . "\n";

    // Preload critical fonts (adjust paths as needed)
    echo '<link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">' . "\n";
}
add_action( 'wp_head', 'flatsome_child_add_preload_links', 1 );

// Security enhancements
function flatsome_child_security_headers() {
    if ( ! is_admin() ) {
        header( 'X-Content-Type-Options: nosniff' );
        header( 'X-Frame-Options: SAMEORIGIN' );
        header( 'X-XSS-Protection: 1; mode=block' );
        header( 'Referrer-Policy: strict-origin-when-cross-origin' );
    }
}
add_action( 'send_headers', 'flatsome_child_security_headers' );

// Optimize WooCommerce
if ( class_exists( 'WooCommerce' ) ) {
    // Remove WooCommerce scripts on non-shop pages
    function flatsome_child_optimize_woocommerce_scripts() {
        if ( ! is_woocommerce() && ! is_cart() && ! is_checkout() && ! is_account_page() ) {
            wp_dequeue_style( 'woocommerce-general' );
            wp_dequeue_style( 'woocommerce-layout' );
            wp_dequeue_style( 'woocommerce-smallscreen' );
            wp_dequeue_script( 'wc-cart-fragments' );
            wp_dequeue_script( 'woocommerce' );
        }
    }
    add_action( 'wp_enqueue_scripts', 'flatsome_child_optimize_woocommerce_scripts', 99 );
}

// Include additional optimization files
require_once get_stylesheet_directory() . '/image-optimizer.php';
require_once get_stylesheet_directory() . '/database-cleanup.php';