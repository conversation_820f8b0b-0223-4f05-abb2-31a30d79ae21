{"translation-revision-date": "2025-07-21 17:33:10+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "zh_CN"}, "An error has prevented the block from being updated.": ["发生错误，区块未能成功更新。"], "Align the last block to the bottom.": ["将最后一个区块与底部对齐。"], "Align the last block to the bottom": ["将最后一个区块与底部对齐"], "Stock status \"%s\" hidden.": ["库存状态“%s”已隐藏。"], "Stock status \"%s\" visible.": ["库存状态“%s”可见。"], "Edit selected attribute": ["编辑所选属性"], "%d term": ["%d 个字词"], "%1$s, has %2$d product": ["%1$s，有%2$d产品"], "%1$s, has %2$d term": ["%1$s，有%2$d项"], "Loading…": ["载入中…"], "The last inner block will follow other content.": ["最后一个内部区块位于其他内容之后。"], "The following error was returned": ["系统返回了以下错误"], "The following error was returned from the API": ["API 返回以下错误"], "Search results updated.": ["搜索结果已更新。"], "%d item selected": ["已选择 %d项"], "Search for items": ["搜索项"], "No results for %s": ["无 %s的相关结果"], "No items found.": ["未找到项。"], "Clear all selected items": ["清除所有选定项"], "Clear all": ["全部清除"], "Remove %s": ["移除 %s"], "Showing Products by Attribute block preview.": ["显示“按属性列出产品”区块预览。"], "Display a grid of products from your selected attributes.": ["根据您选择的属性显示产品网格。"], "Products by Attribute": ["按属性列出产品"], "Filter by Product Attribute": ["按产品属性过滤"], "All selected attributes": ["所有选定属性"], "Any selected attributes": ["任何选定属性"], "Pick at least two attributes to use this setting.": ["选择至少两个属性以使用此设置。"], "Product attribute search results updated.": ["已更新产品属性搜索结果。"], "%d attribute selected": ["已选择 %d个属性"], "Search for product attributes": ["搜索产品属性"], "Your store doesn't have any product attributes.": ["您的商店没有任何产品属性。"], "Clear all product attributes": ["清除所有产品属性"], "Order By": ["排序依据"], "Done": ["完成"], "Layout": ["布局"], "Rows": ["行"], "Columns": ["列"], "Add to Cart button": ["“加入购物车”按钮"], "Menu Order": ["菜单排序"], "Title - alphabetical": ["标题 - 按字母顺序"], "Sales - most first": ["销量 - 从多到少"], "Price - high to low": ["价格 - 从高到低"], "Price - low to high": ["价格 - 从低到高"], "Newness - newest first": ["新鲜度 - 从新到旧"], "Order products by": ["产品排序依据"], "Display products matching": ["显示产品匹配"], "Product price": ["产品价格"], "Product title": ["产品标题"], "Product rating": ["产品评级"], "Rating - highest first": ["评级 - 从高到低"], "Filter by stock status": ["按库存状态过滤"], "Product image": ["产品图片"], "%d product": ["%d 个产品"], "Content": ["内容"]}}, "comment": {"reference": "assets/client/blocks/products-by-attribute.js"}}