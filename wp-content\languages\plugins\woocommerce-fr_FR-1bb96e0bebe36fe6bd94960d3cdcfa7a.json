{"translation-revision-date": "2025-07-27 07:54:28+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "fr"}, "Pickup": ["Retrait"], "Admin settings\u0004General": ["Général"], "By enabling Local Pickup with more valuable features for your store, it's recommended that you remove the legacy Local Pickup option from your <a>shipping zones</a>.": ["En activant le Retrait avec des fonctionnalités plus intéressantes pour votre boutique, il est recommandé de supprimer l’ancienne option Retrait de vos <a>zones de livraison</a>."], "If local pickup is enabled, the \"Hide shipping costs until an address is entered\" setting will be ignored.": ["Si le retrait est activé, le réglage « Masquer les coûts d’expédition jusqu’à ce qu’une adresse soit saisie » est ignoré."], "Add pickup location": ["Ajouter un lieu de retrait"], "When you add a pickup location, it will appear here.": ["Lorsque vous ajoutez un lieu de retrait, il apparaît ici."], "Define pickup locations for your customers to choose from during checkout.": ["Définissez les lieux de retrait parmi lesquels vos clients pourront choisir lors de la validation de la commande."], "Pickup locations": ["Lieux de retrait"], "Delete location": ["Supprimer le lieu"], "Edit pickup location": ["Modifier un lieu de retrait"], "Pickup details": ["Détails du retrait"], "A Location title is required": ["Un titre de lieu est obligatoire"], "Location name": ["Nom du lieu"], "Not taxable": ["Non imposable"], "By default, the local pickup shipping method is free.": ["<PERSON><PERSON> <PERSON><PERSON>, le mode de livraison par retrait est gratuit."], "Add a price for customers who choose local pickup": ["Ajouter un prix pour les clients qui choisissent le retrait"], "Local pickup title is required": ["Le titre du retrait est obligatoire"], "This is the shipping method title shown to customers.": ["Il s’agit du titre du mode de livraison indiqué aux clients."], "When enabled, local pickup will appear as an option on the block based checkout.": ["Lorsqu’il est activé, le retrait apparaît comme une option dans la validation de commande basée sur des blocs."], "View checkout page": ["Voir la page de validation de commande"], "Enable or disable local pickup on your store, and define costs. Local pickup is only available from the block checkout.": ["<PERSON>z ou désactivez le retrait dans votre boutique et définissez les coûts. Le retrait n’est disponible que dans la validation de commande basée sur des blocs."], "Local Pickup settings have been saved.": ["Les réglages de retrait ont été enregistrés."], "Optional cost to charge for local pickup.": ["Coût facultatif à facturer pour le retrait."], "If a cost is defined, this controls if taxes are applied to that cost.": ["Si un coût est défini, cela contrôle si des taxes sont appliquées à ce coût."], "Pickup location": ["Lieu de retrait"], "Done": ["<PERSON><PERSON><PERSON><PERSON>"], "Country / State": ["Pays/État"], "Title": ["Titre"], "Enable local pickup": ["<PERSON><PERSON> le <PERSON> de vente"], "Postcode / ZIP": ["Code postal"], "Save changes": ["Enregistrer les modifications"], "Free": ["<PERSON><PERSON><PERSON>"], "Cost": ["Coût"], "Enabled": ["Activé"], "Learn more": ["En savoir plus"], "Taxable": ["Taxable"], "Address": ["<PERSON><PERSON><PERSON>", "Adresses"], "Cancel": ["Annuler"], "City": ["Ville"], "State": ["État"], "Taxes": ["Taxes"], "Edit": ["Modifier"]}}, "comment": {"reference": "assets/client/blocks/wc-shipping-method-pickup-location.js"}}