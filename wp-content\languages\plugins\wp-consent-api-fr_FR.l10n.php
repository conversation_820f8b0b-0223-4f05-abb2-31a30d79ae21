<?php
return ['x-generator'=>'GlotPress/4.0.1','translation-revision-date'=>'2025-01-27 08:22:37+0000','plural-forms'=>'nplurals=2; plural=n > 1;','project-id-version'=>'Plugins - WP Consent API - Stable (latest release)','language'=>'fr','messages'=>['Missing consent category. A functional, preferences, statistics-anonymous, statistics or marketing category should be passed when using wp_setcookie.'=>'Catégorie de consentement manquante. Une catégorie fonctionnelle, de préférences, de statistiques anonymes, de statistiques ou de marketing doit être transmise lors de l’utilisation de wp_setcookie.','Consent Level API to read and register the current consent level for cookie management and improving compliance with privacy laws.'=>'Consent Level API pour lire et enregistrer le niveau de consentement actuel pour la gestion des cookies et l’amélioration de la conformité avec les lois sur la protection de la vie privée.','Not all plugins have declared to follow Consent API guidelines. Please contact the developer.'=>'Toutes les extensions n’ont pas déclaré suivre les directives de l’API du consentement. Veuillez contacter le développeur ou la développeuse.','One or more plugins are not conforming to the Consent API.'=>'Une ou plusieurs extensions ne sont pas conformes à l’API de consentement.','All plugins have declared in their code that they are following the guidelines from the WP Consent API. When used in combination with a Cookie Management plugin, this will improve compliancy for your site.'=>'Toutes les extensions ont déclaré dans leur code qu’elles suivent les directives de l’API WP Consent. Utilisé en combinaison avec une extension de gestion des cookies, il améliorera la conformité de votre site.','Compliance'=>'Conformité','All plugins have declared to use the Consent API'=>'Toutes les extensions ont déclaré utiliser l’API de consentement.','WP Consent API test'=>'Test de l’API WP Consent','This plugin requires WordPress 5.0 or higher'=>'Cette extension nécessite WordPress version %s ou suivantes','This plugin requires PHP 5.6 or higher'=>'Cette extension nécessite PHP 5.6 ou plus.','%s is a singleton class and you cannot create a second instance.'=>'%s est une classe unique et vous ne pouvez pas créer une deuxième instance.','https://github.com/rlankhorst/wp-consent-level-api'=>'https://github.com/rlankhorst/wp-consent-level-api','RogierLankhorst'=>'RogierLankhorst','https://wordpress.org/plugins/wp-consent-api'=>'https://fr.wordpress.org/plugins/wp-consent-api','WP Consent API'=>'WP Consent API']];