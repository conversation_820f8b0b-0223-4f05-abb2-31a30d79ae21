{"translation-revision-date": "2025-07-21 17:33:10+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "zh_CN"}, "Toggle to disable opening the Mini-Cart drawer when clicking the cart icon, and instead navigate to the checkout page.": ["切换为在点击购物车图标时禁止打开迷你购物车抽屉，而是导航至结账页面。"], "Navigate to checkout when clicking the Mini-Cart, instead of opening the drawer.": ["点击迷你购物车时会导航至结账页面，而不是打开抽屉。"], "The editor does not display the real count value, but a placeholder to indicate how it will look on the front-end.": ["编辑器并不会显示真正的计数值，而是会显示一个占位符来表示它在前端的外观。"], "Only if cart has items": ["仅在购物车中有商品时"], "Always (even if empty)": ["始终（即使为空）"], "Show Cart Item Count:": ["显示购物车商品数："], "Product Count": ["产品数量"], "Cart Icon": ["购物车图标"], "Icon": ["图标"], "Toggle to open the Mini-Cart drawer when a shopper adds a product to their cart.": ["当购物者将产品添加到购物车时，切换为打开“迷你购物车”抽屉。"], "Open drawer when adding": ["添加时打开抽屉"], "Behavior": ["行为"], "Edit Mini-Cart Drawer template": ["编辑“迷你购物车抽屉”模板"], "When opened, the Mini-Cart drawer gives shoppers quick access to view their selected products and checkout.": ["打开之后，购物者可以通过“迷你购物车”抽屉快速查看他们所选的产品并结账。"], "Cart Drawer": ["购物车抽屉"], "Toggle to display the total price of products in the shopping cart. If no products have been added, the price will not display.": ["切换为显示购物车中产品的总价。 如果没有添加产品，则不会显示价格。"], "Display total price": ["显示总价"], "Select how the Mini-Cart behaves in the Cart and Checkout pages. This might affect the header layout.": ["选择迷你购物车在“购物车”和“结账”页面中的行为。 这可能会影响标题布局。"], "Mini-Cart in cart and checkout pages": ["“购物车”和“结账”页面中的迷你购物车"], "Hide": ["隐藏"], "Display": ["显示"], "Never": ["永久有效"], "Price": ["价格"], "Remove": ["删除"], "Settings": ["设置"]}}, "comment": {"reference": "assets/client/blocks/mini-cart.js"}}