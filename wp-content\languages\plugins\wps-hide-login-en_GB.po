# Translation of Plugins - WPS Hide Login - Stable (latest release) in English (UK)
# This file is distributed under the same license as the Plugins - WPS Hide Login - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-06-13 12:07:26+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: en_GB\n"
"Project-Id-Version: Plugins - WPS Hide Login - Stable (latest release)\n"

#: classes/plugin.php:392
msgid "WPBoutik"
msgstr "WPBoutik"

#: classes/plugin.php:392
msgid "You want to find out how to simplify ecommerce with WordPress, try"
msgstr "You want to find out how to simplify eCommerce with WordPress, try"

#: classes/plugin.php:874
msgid "WPS Hide Login : Please note that the comment_registration option “Users must be registered and logged in to comment” is activated on your site, the connection link will not be hidden on the comments block."
msgstr "WPS Hide Login: please note that the comment_registration option “Users must be registered and logged in to comment” is activated on your site, the connection link will not be hidden on the comments block."

#: classes/plugin.php:847
msgid "WPS Hide Login : Please note, if you check the comment_registration option \"Users must be registered and logged in to comment\", the login link will not be hidden on the comment block."
msgstr "WPS Hide Login: please note, if you tick the comment_registration option \"Users must be registered and logged in to comment\", the login link will not be hidden on the comment block."

#. Author of the plugin
#: wps-hide-login.php
msgid "WPServeur, NicolasKulka, wpformation"
msgstr "WPServeur, NicolasKulka, wpformation"

#: classes/plugin.php:436
msgid "Redirect URL when someone tries to access the wp-login.php page and the wp-admin directory while not logged in."
msgstr "Redirect URL when someone tries to access the wp-login.php page and the wp-admin directory while not logged in."

#: classes/plugin.php:421
msgid "Protect your website by changing the login URL and preventing access to the wp-login.php page and the wp-admin directory to non-connected people."
msgstr "Protect your website by changing the log in URL and preventing access to the wp-login.php page and the wp-admin directory to non-connected people."

#: classes/plugin.php:391
msgid "WPS Limit Login"
msgstr "WPS Limit Login"

#: classes/plugin.php:391
msgid "and"
msgstr "and"

#: classes/plugin.php:390
msgid "WPS Cleaner"
msgstr "WPS Cleaner"

#: classes/plugin.php:389
msgid "WPS Bidouille"
msgstr "WPS Bidouille"

#: classes/plugin.php:389 classes/plugin.php:390
msgid "the plugin"
msgstr "the plugin"

#: classes/plugin.php:388
msgid "Discover our other plugins:"
msgstr "Discover our other plugins:"

#: classes/plugin.php:387
msgid "WordPress specialized hosting"
msgstr "WordPress specialised hosting"

#: classes/plugin.php:303
msgid "Redirection url"
msgstr "Redirection URL"

#: classes/plugin.php:252
msgid "Redirection url default"
msgstr "Redirection URL default"

#. Author URI of the plugin
#: wps-hide-login.php
msgid "https://wpserveur.net"
msgstr "https://wpserveur.net"

#: classes/plugin.php:540
msgid "This has been disabled"
msgstr "This has been disabled"

#. Description of the plugin
#: wps-hide-login.php
msgid "Protect your website by changing the login URL and preventing access to wp-login.php page and wp-admin directory while not logged-in"
msgstr "Protect your website by changing the login URL and preventing access to wp-login.php page and wp-admin directory while not logged in"

#: classes/plugin.php:498
msgid "This feature is not enabled."
msgstr "This feature is not enabled."

#: classes/plugin.php:461 classes/plugin.php:465
msgid "Settings"
msgstr "Settings"

#: classes/plugin.php:450
msgid "Your login page is now here: <strong><a href=\"%1$s\">%2$s</a></strong>. Bookmark this page!"
msgstr "Your login page is now here: <strong><a href=\"%1$s\">%2$s</a></strong>. Bookmark this page!"

#: classes/plugin.php:401
msgid "To set a networkwide default, go to <a href=\"%s\">Network Settings</a>."
msgstr "To set a networkwide default, go to <a href=\"%s\">Network Settings</a>."

#: classes/plugin.php:295
msgid "Login url"
msgstr "Login URL"

#: classes/plugin.php:250
msgid "Networkwide default"
msgstr "Networkwide default"

#: classes/plugin.php:247 classes/plugin.php:387
msgid "Need help? Try the <a href=\"%1$s\" target=\"_blank\">support forum</a>. This plugin is kindly brought to you by <a href=\"%2$s\" target=\"_blank\">WPServeur</a>"
msgstr "Need help? Try the <a href=\"%1$s\" target=\"_blank\">support forum</a>. This plugin is kindly brought to you by <a href=\"%2$s\" target=\"_blank\">WPServeur</a>"

#: classes/plugin.php:246
msgid "This option allows you to set a networkwide default, which can be overridden by individual sites. Simply go to to the site’s permalink settings to change the url."
msgstr "This option allows you to set a networkwide default, which can be overridden by individual sites. Simply go to to the site’s permalink settings to change the URL."

#: classes/plugin.php:228
msgid "WPS Hide Login could not be activated because you already have Rename wp-login.php active. Please uninstall rename wp-login.php to use WPS Hide Login"
msgstr "WPS Hide Login could not be activated because you already have Rename wp-login.php active. Please uninstall Rename wp-login.php to use WPS Hide Login"

#. Plugin Name of the plugin
#: wps-hide-login.php classes/plugin.php:222 classes/plugin.php:245
#: classes/plugin.php:734 classes/plugin.php:743
msgid "WPS Hide Login"
msgstr "WPS Hide Login"

#: classes/plugin.php:222
msgid "Please upgrade to the latest version of WordPress to activate"
msgstr "Please upgrade to the latest version of WordPress to activate"