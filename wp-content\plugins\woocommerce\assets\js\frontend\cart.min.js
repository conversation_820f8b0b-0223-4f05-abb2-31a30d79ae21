jQuery(function(t){if("undefined"==typeof wc_cart_params)return!1;var e=function(t){return wc_cart_params.wc_ajax_url.toString().replace("%%endpoint%%",t)},o=function(t){return t.is(".processing")||t.parents(".processing").length},c=function(t){o(t)||t.addClass("processing").block({message:null,overlayCSS:{background:"#fff",opacity:.6}})},r=function(t){t.removeClass("processing").unblock()},i=function(e,o){var c=t.parseHTML(e),r=t(".woocommerce-cart-form",c),i=t(".cart_totals",c),u=function(e){var o=new Set,c=[];return e.each(function(){const e=t(this).text();o.has(e)||(o.add(e),c.push(this))}),t(c)}(t(".woocommerce-error, .woocommerce-message, .woocommerce-info, .is-error, .is-info, .is-success",c));if(0!==t(".woocommerce-cart-form").length){if(o||t(".woocommerce-error, .woocommerce-message, .woocommerce-info, .is-error, .is-info, .is-success, .coupon-error-notice").remove(),0===r.length){if(t(".woocommerce-checkout").length)return void window.location.reload();var m=t(".wc-empty-cart-message",c).closest(".woocommerce");t(".woocommerce-cart-form__contents").closest(".woocommerce").replaceWith(m),u.length>0&&a(u),t(document.body).trigger("wc_cart_emptied")}else{t(".woocommerce-checkout").length&&t(document.body).trigger("update_checkout");var p=t("#coupon_code").val(),d=t("#coupon_code").closest(".coupon").find(".coupon-error-notice");if(t(".woocommerce-cart-form").replaceWith(r),t(".woocommerce-cart-form").find(':input[name="update_cart"]').prop("disabled",!0),o&&d.length>0){var l=t(".woocommerce-cart-form").find("#coupon_code"),_=l.closest(".coupon");l.val(p),l.focus(),s(d,_,!0)}u.length>0&&a(u),n(i)}t(document.body).trigger("updated_wc_div")}else window.location.reload()},n=function(e){t(".cart_totals").replaceWith(e),t(document.body).trigger("updated_cart_totals")},a=function(e,o){o||(o=t(".woocommerce-notices-wrapper:first")||t(".wc-empty-cart-message").closest(".woocommerce")||t(".woocommerce-cart-form")),o.prepend(e)},s=function(e,o,c){if(0!==o.length){var r=e;if("string"==typeof e){var i=t(t.parseHTML(e)).text().trim();if(""===i)return;r=t("<p>",{"class":"coupon-error-notice",id:"coupon-error-notice",text:i})}c&&r.attr("role","alert"),o.find("#coupon_code").addClass("has-error").attr("aria-invalid","true").attr("aria-describedby","coupon-error-notice"),o.append(r)}},u={init:function(e){this.cart=e,this.toggle_shipping=this.toggle_shipping.bind(this),this.shipping_method_selected=this.shipping_method_selected.bind(this),this.shipping_calculator_submit=this.shipping_calculator_submit.bind(this),t(document).on("click",".shipping-calculator-button",this.toggle_shipping),t(document).on("change","select.shipping_method, :input[name^=shipping_method]",this.shipping_method_selected),t(document).on("submit","form.woocommerce-shipping-calculator",this.shipping_calculator_submit),t(".shipping-calculator-form").hide()},toggle_shipping:function(e){var o=t(e.currentTarget);return t(".shipping-calculator-form").slideToggle("slow",function(){var e=this;setTimeout(function(){var c=t(e);o.attr("aria-expanded",c.is(":visible")?"true":"false")},0)}),t("select.country_to_state, input.country_to_state").trigger("change"),t(document.body).trigger("country_to_state_changed"),!1},shipping_method_selected:function(o){var i={};t("select.shipping_method, :input[name^=shipping_method][type=radio]:checked, :input[name^=shipping_method][type=hidden]").each(function(){i[t(this).data("index")]=t(this).val()}),c(t("div.cart_totals"));var a={security:wc_cart_params.update_shipping_method_nonce,shipping_method:i};t.ajax({type:"post",url:e("update_shipping_method"),data:a,dataType:"html",success:function(t){n(t);var e=document.getElementById(o.currentTarget.id);e&&e.focus()},complete:function(){r(t("div.cart_totals")),t(document.body).trigger("updated_shipping_method")}})},shipping_calculator_submit:function(e){e.preventDefault();var o=t(e.currentTarget);c(t("div.cart_totals")),c(o),t("<input />").attr("type","hidden").attr("name","calc_shipping").attr("value","x").appendTo(o),t.ajax({type:o.attr("method"),url:o.attr("action"),data:o.serialize(),dataType:"html",success:function(t){i(t)},complete:function(){r(o),r(t("div.cart_totals"))}})}},m={init:function(){this.update_cart_totals=this.update_cart_totals.bind(this),this.input_keypress=this.input_keypress.bind(this),this.cart_submit=this.cart_submit.bind(this),this.submit_click=this.submit_click.bind(this),this.apply_coupon=this.apply_coupon.bind(this),this.remove_coupon_clicked=this.remove_coupon_clicked.bind(this),this.remove_coupon_error=this.remove_coupon_error.bind(this),this.quantity_update=this.quantity_update.bind(this),this.item_remove_clicked=this.item_remove_clicked.bind(this),this.item_restore_clicked=this.item_restore_clicked.bind(this),this.update_cart=this.update_cart.bind(this),t(document).on("wc_update_cart added_to_cart",function(){m.update_cart.apply(m,[].slice.call(arguments,1))}),t(document).on("click",".woocommerce-cart-form :input[type=submit]",this.submit_click),t(document).on("keypress",".woocommerce-cart-form :input[type=number]",this.input_keypress),t(document).on("submit",".woocommerce-cart-form",this.cart_submit),t(document).on("click","a.woocommerce-remove-coupon",this.remove_coupon_clicked),t(document).on("keydown","a.woocommerce-remove-coupon",this.on_keydown_remove_coupon),t(document).on("click",".woocommerce-cart-form .product-remove > a",this.item_remove_clicked),t(document).on("keydown",".woocommerce-cart-form .product-remove > a",this.on_keydown_remove_item),t(document).on("click",".woocommerce-cart .restore-item",this.item_restore_clicked),t(document).on("change input",".woocommerce-cart-form .cart_item :input",this.input_changed),t(document).on("change input","#coupon_code",this.remove_coupon_error),t('.woocommerce-cart-form :input[name="update_cart"]').prop("disabled",!0)},input_changed:function(){t('.woocommerce-cart-form :input[name="update_cart"]').prop("disabled",!1)},update_cart:function(e){var o=t(".woocommerce-cart-form");c(o),c(t("div.cart_totals")),t.ajax({type:o.attr("method"),url:o.attr("action"),data:o.serialize(),dataType:"html",success:function(t){i(t,e)},complete:function(){r(o),r(t("div.cart_totals")),t.scroll_to_notices(t('[role="alert"]'))}})},update_cart_totals:function(){c(t("div.cart_totals")),t.ajax({url:e("get_cart_totals"),dataType:"html",success:function(t){n(t)},complete:function(){r(t("div.cart_totals"))}})},input_keypress:function(e){if(13===e.keyCode){var o=t(e.currentTarget).parents("form");try{o[0].checkValidity()&&(e.preventDefault(),this.cart_submit(e))}catch(c){e.preventDefault(),this.cart_submit(e)}}},cart_submit:function(e){var c=t(document.activeElement),r=t(":input[type=submit][clicked=true]"),i=t(e.currentTarget);if(i.is("form")||(i=t(e.currentTarget).parents("form")),0!==i.find(".woocommerce-cart-form__contents").length)return!o(i)&&void(r.is(':input[name="update_cart"]')||c.is("input.qty")?(e.preventDefault(),this.quantity_update(i)):(r.is(':input[name="apply_coupon"]')||c.is("#coupon_code"))&&(e.preventDefault(),this.apply_coupon(i)))},submit_click:function(e){t(":input[type=submit]",t(e.target).parents("form")).removeAttr("clicked"),t(e.target).attr("clicked","true")},apply_coupon:function(o){c(o);var i=this,n=t("#coupon_code"),u=n.val(),m={security:wc_cart_params.apply_coupon_nonce,coupon_code:u};t.ajax({type:"POST",url:e("apply_coupon"),data:m,dataType:"html",success:function(e){if(t(".woocommerce-error, .woocommerce-message, .woocommerce-info, .is-error, .is-info, .is-success, .coupon-error-notice").remove(),-1===e.indexOf("woocommerce-error")&&-1===e.indexOf("is-error"))a(e);else{var o=n.closest(".coupon");o.length>0&&s(e,o,!1)}t(document.body).trigger("applied_coupon",[u])},complete:function(){r(o),i.update_cart(!0)}})},remove_coupon_clicked:function(o){o.preventDefault();var i=this,n=t(o.currentTarget).closest(".cart_totals"),s=t(o.currentTarget).attr("data-coupon");c(n);var u={security:wc_cart_params.remove_coupon_nonce,coupon:s};t.ajax({type:"POST",url:e("remove_coupon"),data:u,dataType:"html",success:function(e){t(".woocommerce-error, .woocommerce-message, .woocommerce-info, .is-error, .is-info, .is-success").remove(),a(e),t(document.body).trigger("removed_coupon",[s]),t("#coupon_code").val("").removeClass("has-error").removeAttr("aria-invalid").removeAttr("aria-describedby").closest(".coupon").find(".coupon-error-notice").remove(),r(n)},complete:function(){i.update_cart(!0)}})},on_keydown_remove_coupon:function(e){" "===e.key&&(e.preventDefault(),t(e.currentTarget).trigger("click"))},remove_coupon_error:function(e){t(e.currentTarget).removeClass("has-error").removeAttr("aria-invalid").removeAttr("aria-describedby").closest(".coupon").find(".coupon-error-notice").remove()},quantity_update:function(e){c(e),c(t("div.cart_totals")),t("<input />").attr("type","hidden").attr("name","update_cart").attr("value","Update Cart").appendTo(e),t.ajax({type:e.attr("method"),url:e.attr("action"),data:e.serialize(),dataType:"html",success:function(t){i(t)},complete:function(){r(e),r(t("div.cart_totals")),t.scroll_to_notices(t('[role="alert"]'))}})},item_remove_clicked:function(e){e.preventDefault();var o=t(e.currentTarget),n=o.parents("form");c(n),c(t("div.cart_totals")),t.ajax({type:"GET",url:o.attr("href"),dataType:"html",success:function(t){i(t)},complete:function(){r(n),r(t("div.cart_totals")),t.scroll_to_notices(t('[role="alert"]')),t(document.body).trigger("item_removed_from_classic_cart")}})},on_keydown_remove_item:function(e){" "===e.key&&(e.preventDefault(),t(e.currentTarget).trigger("click"))},item_restore_clicked:function(e){e.preventDefault();var o=t(e.currentTarget),n=t("form.woocommerce-cart-form");c(n),c(t("div.cart_totals")),t.ajax({type:"GET",url:o.attr("href"),dataType:"html",success:function(t){i(t)},complete:function(){r(n),r(t("div.cart_totals"))}})}};u.init(m),m.init()});