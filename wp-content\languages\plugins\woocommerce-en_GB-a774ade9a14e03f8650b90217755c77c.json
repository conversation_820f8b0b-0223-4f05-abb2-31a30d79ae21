{"translation-revision-date": "2024-10-02 16:14:23+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "Align the last block to the bottom.": ["Align the last block to the bottom."], "Align the last block to the bottom": ["Align the last block to the bottom"], "An error has prevented the block from being updated.": ["An error has prevented the block from being updated."], "%1$s, has %2$d review": ["%1$s, has %2$d review", "%1$s, has %2$d reviews"], "%1$s, has %2$d product": ["%1$s, has %2$d product", "%1$s, has %2$d products"], "Loading…": ["Loading…"], "The last inner block will follow other content.": ["The last inner block will follow other content."], "The following error was returned": ["The following error was returned"], "The following error was returned from the API": ["The following error was returned from the API"], "%d review": ["%d review", "%d reviews"], "Search results updated.": ["Search results updated."], "%d item selected": ["%d item selected", "%d items selected"], "Search for items": ["Search for items"], "No results for %s": ["No results for %s"], "No items found.": ["No items found."], "Clear all selected items": ["Clear all selected items"], "Clear all": ["Clear all"], "Remove %s": ["Remove %s"], "%d category selected": ["%d category selected", "%d categories selected"], "All selected categories": ["All selected categories"], "Any selected categories": ["Any selected categories"], "Category search results updated.": ["Category search results updated."], "Clear all product categories": ["Clear all product categories"], "Columns": ["Columns"], "Layout": ["Layout"], "Pick at least two categories to use this setting.": ["Pick at least two categories to use this setting."], "Rows": ["Rows"], "Search for product categories": ["Search for product categories"], "Your store doesn't have any product categories.": ["Your store doesn't have any product categories."], "Filter by Product Category": ["Filter by Product Category"], "Add to Cart button": ["Add to Basket button"], "Display products matching": ["Display products matching"], "Product price": ["Product price"], "Product rating": ["Product rating"], "Product title": ["Product title"], "Product image": ["Product image"], "%d product": ["%d product", "%d products"], "Content": ["Content"], "Product Categories": ["Product Categories"]}}, "comment": {"reference": "assets/client/blocks/product-best-sellers.js"}}