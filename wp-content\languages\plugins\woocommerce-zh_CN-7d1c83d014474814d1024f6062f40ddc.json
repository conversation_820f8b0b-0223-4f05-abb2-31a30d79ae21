{"translation-revision-date": "2025-07-21 17:33:10+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "zh_CN"}, "Upgrade all Filter blocks": ["升级所有筛选器区块"], "Upgrade all Filter blocks on this page for better performance and more customizability": ["升级此页面上的所有筛选器区块，以获得更优性能和更多自定义功能"], "Max. Price": ["最高 价格"], "Min. Price": ["最低 价格"], "Product Filters": ["产品筛选器"], "To filter your products by price you first need to assign prices to your products.": ["要按价格筛选产品，您首先需要为产品分配价格。"], "Show 'Apply filters' button": ["显示“应用过滤器”按钮"], "Show input fields inline with the slider.": ["使用滑块显示内嵌输入字段。"], "Inline input fields": ["内嵌输入字段"], "Price Range Selector": ["价格范围选择器"], "Filter by Price": ["按价格筛选"], "Reset price filter": ["重置价格过滤器"], "Reset filter": ["重置过滤器"], "Block title": ["区块标题"], "Apply price filter": ["应用价格过滤器"], "Apply filter": ["应用过滤器"], "Filter products by maximum price": ["按照最高价格筛选产品"], "Filter products by minimum price": ["按照最低价格筛选产品"], "Editable": ["可编辑"], "Products will update when the button is clicked.": ["点击此按钮后将更新产品。"], "Display a slider to filter products in your store by price.": ["显示过滤产品的价格滑块。"], "Text": ["文本"], "Filter by price": ["按价格筛选"], "Add new product": ["添加新产品"], "Learn more": ["了解更多"], "Reset": ["复位"], "Apply": ["应用"], "Settings": ["设置"]}}, "comment": {"reference": "assets/client/blocks/price-filter.js"}}