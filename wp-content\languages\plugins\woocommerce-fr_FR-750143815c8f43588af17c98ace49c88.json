{"translation-revision-date": "2025-07-27 07:54:28+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "fr"}, "Toggle to disable opening the Mini-Cart drawer when clicking the cart icon, and instead navigate to the checkout page.": ["Basculez pour désactiver l’ouverture du menu tiroir de mini-panier en cliquant sur l’icône de panier et accéder à la place à la page de validation de la commande."], "Navigate to checkout when clicking the Mini-Cart, instead of opening the drawer.": ["Accédez à la validation de la commande en cliquant sur le mini-panier au lieu d’ouvrir le menu tiroir."], "The editor does not display the real count value, but a placeholder to indicate how it will look on the front-end.": ["L’éditeur n’affiche pas la valeur réelle de décompte mais un texte indicatif pour montrer à quoi il ressemblera sur le front-end."], "Always (even if empty)": ["Toujours (même s’il est vide)"], "Show Cart Item Count:": ["Afficher le nombre d’articles dans le panier :"], "Only if cart has items": ["Uniquement si le panier contient des articles"], "Product Count": ["Nombre de produits"], "Cart Icon": ["Icône du panier"], "Icon": ["Icône"], "Behavior": ["Comportement"], "Edit Mini-Cart Drawer template": ["Modifier le modèle d’espace de mini panier"], "Display total price": ["Afficher le prix total"], "Toggle to display the total price of products in the shopping cart. If no products have been added, the price will not display.": ["Basculez pour afficher le prix total des produits dans le panier. Si aucun produit n’a été ajouté, le prix ne s’affiche pas."], "Open drawer when adding": ["<PERSON><PERSON><PERSON><PERSON><PERSON> le tiroir lors de l’ajout"], "When opened, the Mini-Cart drawer gives shoppers quick access to view their selected products and checkout.": ["Lorsqu’il est ouvert, le tiroir du mini panier permet aux acheteurs d’accéder rapidement aux produits qu’ils ont sélectionnés et de valider la commande."], "Cart Drawer": ["T<PERSON>ir du panier"], "Toggle to open the Mini-Cart drawer when a shopper adds a product to their cart.": ["Basculez pour ouvrir le tiroir du mini panier lorsqu’un acheteur ajoute un produit à son panier."], "Select how the Mini-Cart behaves in the Cart and Checkout pages. This might affect the header layout.": ["Sélectionnez le comportement du mini panier sur les pages de panier et de validation de la commande. Cela peut avoir une incidence sur la mise en page de l’en-tête."], "Mini-Cart in cart and checkout pages": ["Mini panier sur les pages de panier et de validation de la commande"], "Hide": ["Masquer"], "Display": ["Affichage"], "Never": ["<PERSON><PERSON>"], "Price": ["Prix"], "Remove": ["<PERSON><PERSON><PERSON><PERSON>"], "Settings": ["Réglages"]}}, "comment": {"reference": "assets/client/blocks/mini-cart.js"}}