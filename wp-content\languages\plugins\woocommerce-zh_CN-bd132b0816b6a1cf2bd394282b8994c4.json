{"translation-revision-date": "2025-07-21 17:33:10+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "zh_CN"}, "Upgrade to the Add to Cart + Options block": ["升级至“添加到购物车 + 选项”区块"], "blockified experience": ["区块化体验"], "Gain access to more customization options when you upgrade to the <strongText />.": ["升级到 <strongText /> 后可访问更多自定义选项。"], "Stepper": ["步进器"], "Input": ["输入"], "Quantity Selector": ["数量选择器"], "Shoppers can use buttons to change the number of items to add to cart.": ["购物者可以使用按钮来更改要添加到购物车中的商品数量。"], "Shoppers can enter a number of items to add to cart.": ["购物者可以输入要添加到购物车中的商品数量。"], "Customer will see product add-to-cart options in this space, dependent on the product type.": ["客户将在此空间看到将产品加入购物车的选项，具体取决于产品类型。"], "Add to cart": ["加入购物车"]}}, "comment": {"reference": "assets/client/blocks/add-to-cart-form.js"}}