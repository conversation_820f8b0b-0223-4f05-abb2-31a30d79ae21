{"translation-revision-date": "2025-07-21 17:33:10+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "zh_CN"}, "This will affect product category pages": ["这将影响产品分类页面"], "Only show children of current category": ["只显示当前分类的子分类"], "Category images are hidden.": ["类别图像隐藏。"], "Category images are visible.": ["类别图像可见。"], "Show category images": ["显示类别图像"], "Display style": ["显示样式"], "List Settings": ["列表设置"], "Show empty categories": ["显示空类别"], "Show product count": ["显示产品数量"], "Product Categories List": ["产品类别列表"], "This block displays the product categories for your store. To use it you first need to create a product and assign it to a category.": ["此区块会显示您商店的产品分类。 为了使用此区块，您首先要创建一个产品，然后将其分类。"], "List": ["列表"], "Dropdown": ["下拉"], "Show hierarchy": ["体现层级关系"], "Content": ["内容"]}}, "comment": {"reference": "assets/client/blocks/product-categories.js"}}