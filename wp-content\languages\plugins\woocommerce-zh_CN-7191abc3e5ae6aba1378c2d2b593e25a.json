{"translation-revision-date": "2025-07-21 17:33:10+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "zh_CN"}, "Item sold": ["已售项目"], "Advanced filters": ["高级过滤器"], "Single category": ["单个类别"], "A sentence describing filters for Categories. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ\u0004Categories match <select/> filters": ["与 <select/> 筛选条件匹配的分类"], "Check at least two categories below to compare": ["在下面选中至少两个类别进行比较"], "Check at least two products below to compare": ["在下面选中至少两个产品进行比较"], "%d categories": ["%d 个类别"], "Search by category name": ["按类别名称搜索"], "Compare Categories": ["比较类别"], "Search for categories to compare": ["搜索类别以进行比较"], "Type to search for a category": ["输入以搜索类别"], "Single Category": ["单个类别"], "%d products": ["%d 个产品"], "Search by product name or SKU": ["按产品名称或 SKU 搜索"], "categories\u0004+%d more": ["另外 %d 个"], "Indication of a low quantity\u0004Low": ["低"], "Comparison": ["比较"], "Net sales": ["净销售额"], "Items sold": ["售出项目"], "Compare": ["比较"], "Product title": ["产品标题"], "All categories": ["全部分类"], "Category": ["分类"], "Show": ["显示"], "N/A": ["N/A"], "Variations": ["变量"], "Products": ["产品"], "SKU": ["SKU"], "Stock": ["库存"], "Categories": ["分类"], "Status": ["状态"], "Order": ["订单"], "Orders": ["订单"], "Product": ["产品"]}}, "comment": {"reference": "assets/client/admin/chunks/analytics-report-categories.js"}}