<?php
return ['x-generator'=>'GlotPress/4.0.1','translation-revision-date'=>'2025-02-07 10:34:26+0000','plural-forms'=>'nplurals=1; plural=0;','project-id-version'=>'Plugins - Migration, Backup, Staging – WPvivid Backup &amp; Migration - Stable (latest release)','language'=>'zh_CN','messages'=>['Back up symlink folders. Including symlink folders may cause backup/migration failure. Uncheck this option unless you know how symlink folders work.'=>'备份符号链接文件夹。包含符号链接文件夹可能会导致备份/迁移失败。除非您知道符号链接文件夹的工作原理，否则请取消选中此选项。','WPvivid Backup & Migration'=>'WPvivid Backup & Migration','Try our AVIF and WebP conversion tool, it\'s free'=>'尝试我们的AVIF和WebP转换工具，它是免费的','Backup compression method.'=>'备用压缩方法。',' for higher task success rate.'=>' 为了提高任务成功率。','Adjust Advanced Settings'=>'调整高级设置','Adjust'=>'调整',' for faster solutions.'=>' 为了更快的解决方案。','Troubleshooting page'=>'故障排除页面','Read'=>'阅读','review will motivate us a lot.'=>'给个好评将极大地激励我们。','Like the plugin? A'=>'喜欢这个插件吗？','Exclude images by folder path'=>'排除文件夹路径中的图片','The value of \'Maximum size of sql file to be imported per request for restoration\' can\'t be empty.'=>'“每次恢复请求导入的SQL文件最大尺寸”的值不能为空。','The value of \'Maximum rows of data to be processed per request for restoration\' can\'t be empty.'=>'“每次恢复请求处理的数据最大行数”的值不能为空。','The value of \'Split a sql file every this size\' can\'t be empty.'=>'“每此大小分割sql文件。”的值不能为空','The value of \'The number of files compressed to the backup zip each time\' can\'t be empty.'=>'“每次备份zip压缩的文件数量”的值不能为空。','We detected that there is already a running backup task. Please wait until it completes then try again.'=>'我们检测到已有一个正在运行的备份任务。请等待它完成后再尝试。','The backup is not responding for a while, do you want to force cancel it?'=>'备份一段时间内没有响应，您想强制取消吗？','Specify the number of files to be extracted per request. The lower the number is, the slower the restoration, but the lower the chance of a timeout error or restore failure.'=>'指定每次请求提取的文件数量。数字越低，恢复速度越慢，但超时错误或恢复失败的概率越低。','Extract files by index for restoration'=>'根据索引提取文件以进行恢复','Maximum rows of data to be processed per request.'=>'每次请求处理的数据最大行数。','Maximum size of sql file to be imported per request for restoration'=>'每次恢复请求导入的SQL文件最大尺寸','The smaller it is, the slower the restoration will be, but the lower the chance of a timeout error.'=>'越小，恢复速度越慢，但超时错误的可能性越低。','Maximum rows of data to be processed per request for restoration'=>'每次恢复请求处理的最大数据行数','Some web hosting providers limit large zip files (e.g. 200MB), and therefore splitting your backup into many parts is an ideal way to avoid hitting the limitation if you are running a big website. Please try to adjust the value if you are encountering backup errors. If you use a value of 0 MB, any backup files won\'t be split.'=>'一些网络托管提供商限制大压缩文件（例如200MB），因此将您的备份分成多个部分是避免达到限制的理想方式，如果您运营一个大型的网站。请尝试调整值，如果您遇到备份错误。如果您使用0MB的值，任何备份文件都不会被分割。','MB'=>'MB','Split a sql file every this size'=>'将sql文件每此大小分割','When taking a backup, the plugin will compress this number of files to the backup zip each time. The default value is 500. The lower the value, the longer time the backup will take, but the higher the backup success rate. If you encounter a backup timeout issue, try to decrease this value.'=>'备份时，插件每次会将这么多文件压缩到备份zip中。默认值为500。值越低，备份所需时间越长，但备份成功率越高。如果您遇到备份超时问题，请尝试降低此值。','Files'=>'文件','The number of files compressed to the backup zip each time'=>'每次压缩到备份zip的文件数量','PclZip is a much slower but more stable zip method that is included in every WordPress install. WPvivid will automatically switch to PclZip if the ZIP extension is not installed within your PHP.'=>'PclZip 是一个速度较慢但更稳定的压缩方法，包含在每个 WordPress 安装中。如果您的 PHP 中未安装 ZIP 扩展，WPvivid 将会自动切换到 PclZip。','ZipArchive has a better flexibility which provides a higher backup success rate and speed. WPvivid Backup Plugin uses ZipArchive method by default. Using this method requires the ZIP extension to be installed within your PHP.'=>'ZipArchive 具有更好的灵活性，提供了更高的备份成功率和速度。WPvivid Backup 插件默认使用 ZipArchive 方法。使用此方法需要在您的 PHP 中安装 ZIP 扩展。','Unlimited domains'=>'无限域名','Comment'=>'评论','Prefix'=>'前缀','Type'=>'类型','Time'=>'时间','cb'=>'cb','If you need any help with our plugin, start a thread on the plugin support forum and we will respond shortly.'=>'如果您需要我们插件的任何帮助，请在插件支持论坛上发帖，我们将很快回复。','Get Support on Forum'=>'在论坛上获取支持','Restore Database Snapshots'=>'回复数据库快照','Create Database Snapshots'=>'创建数据库快照','Documentation'=>'文档','Are you sure to delete the selected snapshots? These snapshots will be deleted permanently.'=>'您确定要删除所选快照吗？这些快照将被永久删除。','Are you sure you want to delete this snapshot?'=>'您确定要删除这个快照吗？','Are you sure you want to restore this snapshot?'=>'您确定要恢复这个快照吗？','Database Snapshots'=>'数据库快照','See Plans'=>'查看计划','To add Dropbox, please get Dropbox authentication first. Once authenticated, you will be redirected to this page, then you can add storage information and save it.'=>'要添加Dropbox，请先获取Dropbox身份验证。一旦验证通过，您将被重定向到此页面，然后您可以添加存储信息并保存。','Junk Size:'=>'垃圾大小：','Backup Cache Size:'=>'备份缓存大小：','Logs Size:'=>'日志大小：','Backup Size:'=>'备份大小：','lsolated Folder Path: '=>'孤立的文件夹路径： ','This tab displays the isolated images and their locations. You can choose to restore or delete specific isolated images.'=>'此选项卡显示孤立的图像及其位置。您可以选择恢复或删除特定的孤立图像。','All Folders'=>'所有文件夹','Search'=>'搜索','Clicking the \'Scan\' button to find unused images in your media folder. Currently it only scans JPG and PNG images.'=>'点击「扫描」按钮，在媒体文件夹中查找未使用的图像。目前，它只扫描JPG和PNG格式的图像。','Media path: '=>'媒体路径： ','In the tab, you can scan your media folder (uploads) to find unused images and isolate specific or all unused images.'=>'在选项卡中，您可以扫描媒体文件夹（上传）以查找未使用的图像，并隔离特定或所有未使用的图像。','Isolated Media'=>'孤立的媒体','Scan Media'=>'扫描媒体','Restoring images...'=>'正在还原图像…','Delete all images'=>'删除所有图像','Delete selected images'=>'删除选中的图像','Restore all images'=>'恢复所有图像','Restore selected images'=>'恢复选中的图像','Isolating images...'=>'正在隔离图像…','Apply'=>'应用','Isolate all images'=>'隔离所有图像','Isolate selected images'=>'隔离选中的图像','Bulk Actions'=>'批量操作','Select bulk action'=>'选择批量操作','With this option checked, all staging sites you have created will be retained when the plugin is deleted, just in case you still need them later. The sites will show up again after the plugin is reinstalled.'=>'选中此选项，插件删除后，您创建的所有过渡站点都将保留，以防您以后仍然需要它们。重新安装插件后，这些网站将再次出现。','When checked, this option allows you to keep the current permalink structure when you create a staging site or push a staging site to live.'=>'选中后，您在创建过渡站点或推送过渡站点时将保留当前的固定链接结构。','When the option is checked, anyone will be able to visit the staging site without the need to login. Uncheck it to request a login to visit the staging site.'=>'选中此项后，任何人无需登录即可访问过渡站点。取消选中后，用户需要登录才能访问过渡网站。','Are you sure to restart this staging site?'=>'你确定要重启这个过渡站点吗？','Are you sure to delete this staging site?'=>'你确定要删除这个过渡站点吗？','Click OK to start creating the staging site.'=>'点击确定开始创建过渡站点.','Click OK to start creating fresh WordPress install.'=>'点击确定开始创建全新的WordPress安装.','Warning: Root directory is forbidden to set to \'/\'.'=>'警告：根目录禁止设置为“/”。','Authentication is done, please continue to enter the storage information, then click \'Add Now\' button to save it.'=>'认证已完成，请继续输入存储信息，然后点击“立即添加”按钮保存。','Add a storage'=>'添加存储','Remove the remote storage'=>'删除这个远程存储','Edit the remote storage'=>'编辑这个远程存储','Delete the backup'=>'删除此备份','Click the button to complete website restore or migration'=>'单击按钮完成网站恢复或迁移','Uploaded Backup: '=>'上传的备份： ','You have successfully changed your default remote storage.'=>'您已成功更改默认远程存储。','Manual'=>'手动','Clone then Transfer'=>'克隆并传输','Save'=>'保存','The key has expired.'=>'密钥已过期。','The key is invalid.'=>'密钥无效。','A key is required.'=>'必要为必填项。','24 hours'=>'24小时','8 hours'=>'8小时','2 hours'=>'2小时','Reset Filters'=>'重置过滤器','Please select an existing author to start importing.'=>'请选择现有作者以开始导入。','Import failed.'=>'导入失败.','Import completed successfully.'=>'成功完成导入。','Are you sure you want to delete all the exported files in the /ImportandExport folder? All the export files in the folder will be permanently deleted.'=>'您确定要删除/ImportandExport文件夹中的所有导出文件吗？文件夹中的所有导出文件都将被永久删除。','Show Pages'=>'显示页面','Show Posts'=>'显示文章','You can not use word \'wpvivid\' to comment the post.'=>'您不能使用“wpvivid”一词来评论文章。','Seconds'=>'秒','The selected item(s) will be permanently deleted. Are you sure you want to continue?'=>'所选项目将被永久删除。你确定要继续吗？','Out of date backups have been removed.'=>'过时的备份已被删除。','Deleting a remote storage will make it unavailable until it is added again. Are you sure to continue?'=>'删除远程存储将使其不可用，直到它再次添加。你确定要继续吗？','Restore failed.'=>'恢复失败。','Are you sure to continue?'=>'确定要继续吗？','Prepare to download the backup'=>'准备下载备份','Downloaded Size: '=>'下载文件大小： ','Retriving (remote storage to web server)'=>'正在检索（远程存储到Web服务器）','Prepare to Download'=>'准备下载','Send succeeded.'=>'发送成功。','VPS hosting'=>'VPS主机','share hosting'=>'共享主机','Already Done'=>'已完成','Never'=>'从不','Maybe Later'=>'稍后','Rate Us'=>'给我们评价','Retrying'=>'正在重试','Send backups to remote storage (You can choose whether to keep the backup in localhost after it is uploaded to cloud storage in Settings.)'=>'发送文件到远程存储(您可以在设置中选择上传到云存储后是否将备份保留在本地主机中)','Are you sure to remove the selected backups? These backups will be deleted permanently.'=>'您确定要删除选定的备份吗？这些备份将被永久删除。','Are you sure to remove this backup? This backup will be deleted permanently.'=>'您确定要删除此备份吗？此备份将被永久删除。','Click to get Microsoft authentication.'=>'点击获取Microsoft身份验证。','To add OneDrive, please get Microsoft authentication first. Once authenticated, you will be redirected to this page, then you can add storage information and save it'=>'要添加OneDrive，请先获得Microsoft身份验证。认证成功后会跳转到这个页面，然后添加存储信息并保存','Click to get Google authentication.'=>'点击以获得谷歌身份验证。','To add Google Drive, please get Google authentication first. Once authenticated, you will be redirected to this page, then you can add storage information and save it'=>'要添加 Google Drive，请先获得 Google 身份验证。认证成功后会跳转到这个页面，然后你可以添加存储信息并保存','To add Dropbox, please get Dropbox authentication first. Once authenticated, you will be redirected to this page, then you can add storage information and save it'=>'要添加Dropbox，请先获得Dropbox身份验证。认证成功后会跳转到这个页面，然后添加存储信息并保存','Click to get Dropbox authentication.'=>'点击获取Dropbox身份验证。','Click the button to add the storage.'=>'点击按钮添加存储。','Add Now'=>'现在添加','The value of \'Delay Between Requests\' can\'t be empty.'=>'“请求之间的延迟”的值不能为空。','The value of \'PHP Scripts Execution Timeout\' can\'t be empty.'=>'“PHP 脚本执行超时”的值不能为空。','The value of \'Staging Memory Limit\' can\'t be empty.'=>'“Staging Memory Limit”的值不能为空。','The value of \'Max File Size\' can\'t be empty.'=>'“最大文件大小”的值不能为空。','The value of \'File Copy Count\' can\'t be empty.'=>'“文件复制计数”的值不能为空。','The value of \'DB Replace Count\' can\'t be empty.'=>'“数据库替换计数”的值不能为空。','The value of \'DB Copy Count\' can\'t be empty.'=>'“数据库复制计数”的值不能为空。','The value of \'Media Files Quantity Processed Per Request\' can\'t be empty.'=>'“每个请求处理的媒体文件数量”的值不能为空。','The value of \'Posts Quantity Processed Per Request\' can\'t be empty.'=>'“每个请求处理的文章数量”的值不能为空。','The value of \'Chunk Size\' can\'t be empty.'=>'“分块大小”的值不能为空。','The value of \'PHP memory limit for restoration\' can\'t be empty.'=>'“用于还原的PHP内存限制”的值不能为空。','The value of \'PHP memory limit for backup\' can\'t be empty.'=>'“用于备份的PHP内存限制”的值不能为空。','The value of \'PHP scripts execution timeout for restore\' can\'t be empty.'=>'“还原PHP脚本执行超时”的值不能为空。','The value of \'PHP scripts execution timeout for backup\' can\'t be empty.'=>'“备份PHP脚本执行超时”的值不能为空。','The value of \'Exclude files which are larger than\' can\'t be empty.'=>'\'Exclude files which are larger than\' 的值不能为空.','The value of \'Compress file every\' can\'t be empty.'=>'‘压缩文件间隔’的值不能为空。','Back'=>'返回','Because Dropbox has upgraded their API on September 30, 2021, the new API is no longer compatible with the previous app\'s settings. Please re-add your Dropbox storage to ensure that it works properly.'=>'Dropbox 已于 2021 年 9 月 30 日升级其 API，因此新 API 不再与之前应用的设置兼容。请重新添加您的 Dropbox 存储以确保其正常工作。','In order to execute the scheduled backups properly, please set the DISABLE_WP_CRON constant to false. If you are using an external cron system, simply click \'X\' to dismiss this message.'=>'为了正确执行计划备份，请将 DISABLE_WP_CRON 常量设置为 false。如果您使用的是外部 cron 系统，点击“X”即可关闭此消息。','<strong>Tips: </strong>Some web hosts may restrict the connection between the two sites, so you may get a 403 error or unstable connection issue when performing auto migration. In that case, it is recommended to manually transfer the site.'=>'<strong>小贴士：</strong>部分主机可能会限制两个站点之间的连接，您在执行自动迁移时可能会遇到 403 错误或连接不稳定的问题。在这种情况下，建议手动转移站点。','Staging'=>'过渡站点','Create A Fresh WordPress Install'=>'创建全新的WordPress安装','Create A Staging Site'=>'创建过渡站点','WPvivid Plugins - Staging'=>'WPvivid Plugins - 过渡站点','Subsite Description'=>'子网站描述','Subsite Title'=>'子网站标题','Subsite Tables/Folders'=>'子站点数据表/文件夹','Subsite URL'=>'子站点URL','Keep staging sites when deleting the plugin'=>'删除插件后保留过渡站点','Keep permalink when transferring website'=>'转移网站时保存固定链接','Anyone can visit the staging site'=>'所有人都可以访问过渡站点',' times when encountering a time-out error'=>' 遇到超时错误的次数','A lower value will help speed up the process of creating a staging site. However, if your server has a limit on the number of requests, a higher value is recommended.'=>'较低的值将有助于加快创建过渡站点的过程。但是，如果您的服务器对请求数有限制，则建议使用更高的值。','Delay Between Requests'=>'请求之间的延时','The time-out is not your server PHP time-out. With the execution time exhausted, our plugin will shut down the progress of 
                creating a staging site. If the progress  encounters a time-out, that means you have a medium or large sized website. Please try to 
                scale the value bigger.'=>'此超时不是您的服务器PHP超时。达到执行时间后，我们的插件将停止创建临时站点。如果遇到超时，则意味着您拥有中型或大型网站。请尝试将该值调大。','PHP Script Execution Timeout'=>'PHP 脚本执行超时','Adjust this value to apply for a temporary PHP memory limit for the plugin to create a staging site. 
                We set this value to 256M by default. Increase the value if you encounter a memory exhausted error. Note: some 
                web hosting providers may not support this.'=>'调整此值，为插件申请临时PHP内存限制，以创建过渡站点。 
我们默认将此值设置为 256M。如果遇到内存耗尽错误，请增加该值。注意：一些虚拟主机提供商可能不支持此功能。','Staging Memory Limit'=>'过渡站点内存限制','Maximum size of the files copied to a staging site. All files larger than this value will be ignored. If you set the value of 0 MB, all files will be copied to a staging site.'=>'复制到临时站点的最大文件。所有大于此值的文件都将被忽略。如果设置为 0MB，则所有文件都将复制到临时站点。','Max File Size'=>'最大文件尺寸','Number of files to copy that will be copied within one ajax request. The higher value makes the file copy process faster. 
                Please try a high value to find out the highest possible value. If you encounter timeout errors, try lower values until no more errors occur.'=>'将在一个ajax请求中复制的文件数量。更高的值使文件复制过程更快。

请尝试高该值，以找到尽可能高的值。如果您遇到超时错误，请尝试降低该值，直到错误不再发生。','File Copy Count'=>'文件复制总数','Number of DB rows, that are processed within one ajax query. The higher value makes the DB replacement process faster. 
                If timeout erros occur, decrease the value because this process consumes a lot of memory.'=>'在一个Ajax查询中处理的数据表行数。该值越高，数据库替换过程越快。 
 如果出现超时错误，请减小该值，因为此进程会消耗大量内存。','DB Replace Count'=>'数据库替换总数','Number of DB rows, that are copied within one ajax query. The higher value makes the database copy process faster. 
                Please try a high value to find out the highest possible value. If you encounter timeout errors, try lower values until no 
                more errors occur.'=>'在一个Ajax查询中复制的数据表行数。 值越高，数据库复制过程越快。请尝试高该值以找出可能的最高值。如果遇到超时错误，请尝试较低的值，直到没有错误出现。','DB Copy Count'=>'数据库复制计数','Staging Settings'=>'过渡站点设置','Staging Logs'=>'暂存日志','Action	'=>'操作	','Log File Name	'=>'日志文件名称	','Backup Logs'=>'备份日志','Up to 50 domains'=>'多达50个域名','Up to 10 domains'=>'多达10个域名','2 domains'=>'2个域名','Roles & Capabilities'=>'角色&能力','White Label'=>'白牌','Staging Pro'=>'过渡站点专业版','Mulitsite Support'=>'多站点支持','Backup & Migration Pro'=>'Backup & Migration Pro','Domains'=>'域名','Small Business'=>'小企业','Blogger'=>'博客','Features'=>'功能','File not found'=>'找不到文件','File Size: '=>'文件尺寸： ','File'=>'文件','Total Folders:'=>'文件夹总数：','Scanned:'=>'已扫描：','Total Posts:'=>'文章总数：','Please don\'t refresh the page while running a scan.'=>'扫描时请不要刷新此页面b。','Scan'=>'扫描','WPvivid Image Cleaner'=>'WPvivid图像清理器','Image Cleaner'=>'图像清理器','Size'=>'尺寸','Path'=>'路径','Thumbnail'=>'缩略图','Set how many media files to process per request. The value should be set depending on your server performance and the recommended value is 100.'=>'设置每个请求要处理多少媒体文件。该值应根据您的服务器性能设置，推荐值为 100。','Media Files Quantity Processed Per Request'=>'每个请求处理的媒体文件数量','Set how many posts to process per request. The value should be set depending on your server performance and the recommended value is 20.'=>'设置每个请求要处理的文章数量。该值应根据您的服务器性能设置，推荐值为 20。','Posts Quantity Processed Per Request'=>'每个请求处理的文章数量','With this option checked, when the image is deleted, the corresponding image url in the database that is not used anywhere on your website will also be deleted.'=>'选中此选项后，删除图像时，不在网站上使用的图像URL也将被从数据库中删除。','Delete Image URL'=>'删除图像URL','Checking this option will speed up your scans but may produce lower accuracy.'=>'选中此选项将加快您的扫描速度，但可能会降低准确性。','Enable Quick Scan'=>'启用快速扫描','Media Cleaner Settings'=>'媒体清理器设置','Post Type'=>'文章类型','File Regex'=>'文件正则表达式','Tip:'=>'使用技巧：','Please enter a valid email address.'=>'请输入一个有效的电子邮件地址。','User\'s email address is required.'=>'用户的电子邮件地址为必填项。','Save backups on localhost (web server)'=>'保存备份到本机(Web服务器)','Type:'=>'类型：','Invalid email address'=>'无效的电子邮件地址','The selected file is not the setting file for WPvivid. Please upload the right file.'=>'选中的文件不是WPvivid的设置文件，请上传正确的文件。','The pdo_mysql extension is not detected. Please install the extension first or choose wpdb option for Database connection method.'=>'未检测到 pdo_mysql 扩展。请先安装扩展或为数据库连接方法选择 wpdb 选项。','An email address is required.'=>'电子邮件地址为必填项。','The local storage path is required.'=>'本机存储路径为必填项。','Choose one storage from the list to be the default storage.'=>'从列表中选择一个存储作为默认存储。','The selected junk files have been deleted.'=>'所选的垃圾文件已被删除。','Choose at least one type of junk files for deleting.'=>'至少选择一个类型的垃圾文件来删除。','Getting backup directory failed. Please try again later.'=>'获取备份目录失败，请稍后重试。','Last Backup: '=>'最新备份： ','The last backup message not found.'=>'未找到最新备份消息。','The restore file not found. Please verify the file exists.'=>'还原文件未找到，请验证文件是否存在。','restore failed error unknown'=>'还原失败，错误未知','Failed to get the remote storage information. Please try again later.'=>'获取远程存储信息失败，请稍后重试。','Failed to delete the remote storage, can not retrieve the storage infomation. Please try again.'=>'删除远程存储失败，无法检索存储信息。请再试一次。','You have successfully added a remote storage.'=>'您已成功添加远程存储。','Unable to delete the locked backup. Please unlock it first and try again.'=>'无法删除锁定的备份。请先解锁，然后重试。','Retrieving the backup(s) information failed while deleting the selected backup(s). Please try again later.'=>'删除所选备份时检索备份信息失败，请稍后再试。','Task timed out.'=>'任务超时。','Too many resumption attempts.'=>'恢复尝试次数太多。','There is no default remote storage configured. Please set it up first.'=>'没有配置默认的远程存储。请先设置。','Choose at least one storage location for backups.'=>'至少选择一个存储位置进行备份。','A backup type is required.'=>'备份类型为必填项。','Creating scheduled tasks failed. Please try again later.'=>'创建计划任务失败，请稍后再试。','12Hours'=>'12小时','Monthly'=>'每月','Fortnightly'=>'每半月','Weekly'=>'每周','Daily'=>'每天','12 Hours'=>'21小时','With this option checked, Pages/posts already existing will be overwritten with the updated ones in an import.'=>'选中此选项后，现有页面/文章将被导入中已更新的页面/文章覆盖。','Overwrite existing pages'=>'覆盖现有页面','Import Setting'=>'导入设置','- Select -'=>'- 选择 -','Select an existing author:'=>'选择现有作者：','Assign author'=>'分配作者','The importing file info'=>'导入文件信息','Scan Uploaded Exports'=>'扫描上传的导出','Upload and Import'=>'上传并导入','Choose an export from your computer to import: '=>'从您的计算机中选择要导入的导出： ','Delete Exported Files In Folder'=>'删除文件夹中导出的文件','To properly display the imported content, please make sure that the importing and exporting sites have the same environment, for example, same theme or pages built with the same page builder.'=>'为了正确显示导入的内容，请确保导入和导出站点具有相同的环境，例如相同的主题或使用相同的页面构建器构建的页面。','Import posts or pages with images in bulk.'=>'批量导入带有图像的文章或页面。','File not found. Please retry again.'=>'文件未找到，请重试。','File size not match. please retry again.'=>'文件尺寸不匹配，请重试。','Task time out.'=>'任务超时。','Export error:'=>'导出错误：','The export task is not responding.'=>'导出任务无响应。','Ready to export. Progress: 0%, running time: 0second.'=>'准备导出。进度：0%，运行时间：0 秒。','Error occurred while parsing the request data. Please try to run export task again.'=>'解析请求数据时出错，请尝试再次运行导出任务。','Empty post id'=>'空白的文章ID','Export and Download'=>'导入并下载','Sample:'=>'示例：','Only letters (except for wpvivid) and numbers are allowed.'=>'只允许使用字母（wpvivid 除外）和数字。','Comment the export: '=>'导出注释： ','Comment the export (optional)'=>'导出注释：(选填)','&mdash; Select &mdash;'=>'&mdash; 选择 &mdash;','All Authors'=>'所有作者','All Categories'=>'所有分类目录','Filter Posts/Pages'=>'过滤文件/页面','Choose what to export'=>'选择需要导出的内容','Next Step'=>'下一步','More post types coming soon...'=>'更多文件类型即将到来…','Page'=>'页面','Post'=>'文章','Choose post type'=>'选择文章类型','Try to select fewer items when you are facing a shortage of server resources (typically presented as a timeout error).'=>'当您面临服务器资源短缺（通常表现为超时错误）时，请尝试选择更少的项目。','Note:'=>'备注：','This will contain all of your posts, pages, comments, terms and images (original images, featured images and thumbnails).'=>'这将包含您的所有文章、页面、评论、项目和图像（原始图像、特色图像和缩略图）。','Learn more'=>'了解更多','Export posts or pages with images in bulk.'=>'批量导出带有图像的文章和页面。','Export & Import'=>'导出 & 导入','Last Modified'=>'最新修改','Scheduled'=>'计划','Missed schedule'=>'错过的计划','Published'=>'已发布','Unpublished'=>'未发布','Comments'=>'评论','Author'=>'作者','column nameTitle'=>'标题','Note: The files you want to upload must be a backup created by WPvivid backup plugin. Make sure that uploading every part of a backup to the directory if the backup is split into many parts'=>'注意：上传的文件必须是由 WPvivid 备份插件创建的备份。如果备份被分成多个部分，请确保上传所有部分','Upload'=>'上传','Transfer succeeded. Please scan the backup list on the destination site to display the backup, then restore the backup.'=>'转移成功，请在目标站点上扫描备份列表以显示备份，然后恢复备份。','Choose what to migrate'=>'选择需要迁移的内容','3. Once done, the backup appears in backups list. Then, restore the backup.'=>'3. 完成后，备份将出现在备份列表中，然后，恢复备份。','2.1 Upload the backup to the upload section of WPvivid backup plugin in destination site.'=>'2.1将备份上传到目标站点的WPvivid备份插件的上传部分。','2. Upload the backup to destination site. There are two ways available to use:'=>'2. 将备份上传到目标站点，可以使用以下两种方法：','1. Download a backup in backups list to your computer.'=>'1. 将备份列表中的备份下载到您的计算机。','How to migrate Wordpress site manually to a new domain(site) with WPvivid backup plugin?'=>'如何使用WPvivid备份插件手动将Wordpress站点迁移到新域名（站点）？','2. Please migrate website with the manual way when using <strong>Local by Flywheel</strong> environment.'=>'2. 使用<strong>Local by Flywheel</strong>环境时，请手动迁移网站。','1. In order to successfully complete the migration, you\'d better deactivate <a href="https://wpvivid.com/best-redirect-plugins.html" target="_blank" style="text-decoration: none;">301 redirect plugin</a>, <a href="https://wpvivid.com/8-best-wordpress-firewall-plugins.html" target="_blank" style="text-decoration: none;">firewall and security plugin</a>, and <a href="https://wpvivid.com/best-free-wordpress-caching-plugins.html" target="_blank" style="text-decoration: none;">caching plugin</a> (if they exist) before transferring website.'=>'1. 为了成功完成迁移, 最好在迁移之前停用 <a href="https://wpvivid.com/best-redirect-plugins.html" target="_blank" style="text-decoration: none;">301 重定向插件</a>, <a href="https://wpvivid.com/8-best-wordpress-firewall-plugins.html" target="_blank" style="text-decoration: none;">防火墙和安全插件</a>和 <a href="https://wpvivid.com/best-free-wordpress-caching-plugins.html" target="_blank" style="text-decoration: none;">缓存插件</a> (如果存在)。','Note: '=>'备注： ','Choose the content you want to transfer'=>'选择需要转移的内容','The feature can help you transfer a Wordpress site to a new domain(site). It would be a convenient way to migrate your WP site from dev environment to live server or from old server to the new.'=>'该功能可以帮助你将 WordPress站点转移到新域名（站点）。这将是一种将您的WP站点从开发环境迁移到实时服务器或从旧服务器迁移到新服务器的快捷方式。','3. Go back to this page and paste the key in key box below. Lastly, click Save button.'=>'3. 返回此页面，将密钥粘贴到下方的密钥框中，最后，点击保存按钮。','2. Generate a key by clicking Generate button and copy it.'=>'2. 点击生成按钮生成密钥并复制。','1. Visit Key tab page of WPvivid backup plugin of destination site.'=>'1.访问目标站点的WPvivid备份插件的密钥标签页。','How to get a site key?'=>'如何获取站点密钥？','Please paste the key below.'=>'请在下面输入密钥。','A task is already running. Please wait until the running task is complete, and try again.'=>'一个任务已经在运行。请等待运行任务完成，然后重试。','Generate'=>'生成','Tips: For security reason, please choose an appropriate expiration time for the key.'=>'提示：出于安全考虑，请为密钥选择合适的过期时间。','The key will expire in '=>'密钥过期时间 ','In order to allow another site to send a backup to this site, please generate a key below. Once the key is generated, this site is ready to receive a backup from another site. Then, please copy and paste the key in sending site and save it.'=>'为了允许其他站点将备份发送到此站点，请在下面生成密钥。生成密钥后，此站点即可接收来自另一个站点的备份。然后，请复制粘贴此密钥到发送站点中并保存。','Key'=>'密钥','Auto-Migration'=>'自动迁移','Unable to send email. Please check the configuration of email server.'=>'无法发送电子邮件。请检查电子邮件服务器配置。','Unable to create directory %s. Is its parent directory writable by the server?'=>'无法创建目录%s。它的父目录是否可以被服务器写入？','The log not found.'=>'报不到日志。','Retrieving the backup information failed while showing log. Please try again later.'=>'显示日志时检索备份信息失败，请稍后重试。','Reading the log failed. Please try again.'=>'读取日志失败，请重试。','Unable to open the log file.'=>'无法打开日志文件。','Error occurred while parsing the request data. Please try to run backup again.'=>'解析请求数据时出错，请尝试重新运行备份。','Click the button to connect to SFTP server and add it to the storage list below.'=>'点击按钮连接到 SFTP 服务器并将其添加到下面的存储列表中。','Enter an absolute path and a custom subdirectory (optional) for holding the backups of current website. For example, /var/customfolder/'=>'输入用于保存当前网站备份的绝对路径和自定义子目录（可选），如，/var/customfolder/','Absolute path must exist(e.g. /var)'=>'绝对路径必须存在（如 /var）','Enter the server port.'=>'输入服务器端口。','Port'=>'端口','Enter the user password.'=>'输入用户密码。','User Password'=>'用户密码','Enter the user name.'=>'输入用户名。','User Name'=>'用户名','Enter the server address.'=>'输入服务器地址。','Server Address'=>'服务器地址','Enter a unique alias: e.g. SFTP-001'=>'输入唯一别名：如 SFTP-001','Enter Your SFTP Account'=>'输入SFTP账户','SFTP'=>'SFTP','The simplexml extension is not detected. Please install the extension first.'=>'未检测到 simplexml 扩展，请先安装。','Click the button to connect to Amazon S3 storage and add it to the storage list below.'=>'点击按钮连接到 Amazon S3 存储并将其添加到下面的存储列表中。','Check the option to use Amazon S3 server-side encryption to protect data.'=>'选中使用 Amazon S3 服务器端加密来保护数据的选项。','Server-side encryption.'=>'服务端加密。','Check the option to use Amazon S3 Standard-Infrequent Access (S3 Standard-IA) storage class for data transfer.'=>'选中使用 Amazon S3 标准-不频繁访问(S3 Standard-IA) 存储类进行数据传输的选项。','Storage class: Standard (infrequent access).'=>'存储类：Standard（不经常访问）。','Customize the directory where you want to store backups within the Bucket.'=>'自定义要在Bucket中存储备份的目录。','Enter an existed Bucket to create a custom backup storage directory.'=>'输入已存在的 Bucket 以创建自定义备份存储目录。','Amazon S3 Bucket Name(e.g. test)'=>'Amazon S3 Bucket名称（如 test）','How to get an Amazon S3 secret key.'=>'如何获得亚马逊S3密钥。','Enter your Amazon S3 secret key.'=>'输入Amazon S3密钥。','Amazon S3 secret key'=>'Amazon S3密钥','How to get an AmazonS3 access key.'=>'如何获取AmazonS3访问密钥。','Enter your Amazon S3 access key.'=>'输入Amazon S3访问密钥。','Amazon S3 access key'=>'Amazon S3访问密钥','Enter a unique alias: e.g. Amazon S3-001'=>'输入唯一别名：如Amazon S3-001','Enter Your Amazon S3 Account'=>'输入Amazon S3帐户','Amazon S3'=>'Amazon S3','Authenticate with Microsoft OneDrive'=>'使用Microsoft OneDrive进行身份验证','Enter a unique alias: e.g. OneDrive-001'=>'输入唯一别名：如 OneDrive-001','Enter Your Microsoft OneDrive Information'=>'输入您的Microsoft OneDrive信息','Microsoft OneDrive'=>'Microsoft OneDrive','You have authenticated the Microsoft OneDrive account as your remote storage.'=>'您已将 Microsoft OneDrive 帐户验证为远程存储。','Authenticate with Google Drive'=>'使用Google云端硬盘进行身份验证','Enter a unique alias: e.g. Google Drive-001'=>'输入唯别名：如 Google Drive-001','Enter Your Google Drive Information'=>'输入您的Google云端硬盘信息','Google Drive'=>'Google Drive','You have authenticated the Google Drive account as your remote storage.'=>'您已将 Google Drive 帐户验证为远程存储。','Authentication failed, the format of the client_secrets.json file is incorrect. Please delete and re-install the plugin to recreate the file.'=>'身份验证失败，client_secrets.json文件格式不正确。请删除并重新安装该插件以重新创建文件。','Authentication failed, the client_secrets.json file is missing. Please make sure the client_secrets.json file is in wpvivid-backuprestore\\includes\\customclass directory.'=>'身份验证失败，缺少client_secrets.json文件。请确保client_secrets.json文件位于wpvivid-backuprestore\\includes\\customclass 目录中。','Click the button to connect to DigitalOcean Spaces storage and add it to the storage list below.'=>'点击按钮以连接到DigitalOcean Spaces存储并将其添加到下面的存储列表中。','Enter the DigitalOcean Endpoint for the storage'=>'输入存储的DigitalOcean端点','region.digitaloceanspaces.com'=>'region.digitaloceanspaces.com','Customize the directory where you want to store backups within the Space.'=>'自定义要在Space中存储备份的目录。','Custom Path'=>'自定义路径','Enter an existed Space to create a custom backup storage directory.'=>'输入已存在的Space以创建自定义备份存储目录。','Space Name(e.g. test)'=>'Space名称(如test)','Enter your DigitalOcean Spaces secret key'=>'输入您的DigitalOcean Spaces密钥','DigitalOcean Spaces secret key'=>'DigitalOcean Spaces密钥','Enter your DigitalOcean Spaces access key'=>'输入您的DigitalOcean Spaces访问密钥','DigitalOcean Spaces access key'=>'DigitalOcean Spaces 访问密钥','Enter a unique alias: e.g. DOS-001'=>'输入唯一别名：如 DOS-001','Enter Your DigitalOcean Spaces Account'=>'输入您的 DigitalOcean Spaces 帐户','DigitalOcean Spaces'=>'DigitalOcean Spaces','Click the button to connect to FTP server and add it to the storage list below.'=>'点击按钮连接到 FTP 服务器并将其添加到下面的存储列表中。','Test and Add'=>'测试并添加','Uncheck the option to use FTP active mode when transferring files. Make sure the FTP server you are configuring supports the active FTP mode.'=>'取消选中传输文件时使用 FTP 活动模式的选项，确保您正在配置的 FTP 服务器支持主动 FTP 模式。','Uncheck this to enable FTP active mode.'=>'取消选中此项以启用 FTP 活动模式。','Enter an absolute path and a custom subdirectory (optional) for holding the backups of current website. For example, /home/<USER>/customfolder'=>'输入用于保存当前网站备份的绝对路径和自定义子目录（可选），如：/home/<USER>/customfolder','Absolute path must exist(e.g. /home/<USER>'=>'绝对路径必须存在（如 /home/<USER>','Enter the FTP server password.'=>'输入FTP服务器密码。','FTP password'=>'FTP密码','Enter your FTP server user name.'=>'输入FTP服务器用户名。','FTP login'=>'FTP用户名','Pro feature: Change the FTP default port number'=>'专业版功能：修改FTP默认端口号','Enter the FTP server.'=>'输入FTP服务器。','FTP server (server\'s port 21)'=>'FTP服务器(服务器端口21)','Enter an unique alias: e.g. FTP-001'=>'输入唯一别名: 如FTP-001','Enter Your FTP Account'=>'输入FTP账户','FTP'=>'FTP','Click the button to save the changes.'=>'点击按钮保存修改。','Authenticate with Dropbox'=>'使用Dropbox进行身份验证','Once checked, all this sites backups sent to a remote storage destination will be uploaded to this storage by default.'=>'选中后，默认情况下，所有发送到远程存储目标的站点备份都将上传到该存储。','Set as the default remote storage.'=>'设为默认远程存储。','Pro feature: Create a directory for storing the backups of the site'=>'专业版功能：创建用于存储站点备份的目录','All backups will be uploaded to this directory.'=>'所有备份将被上传到此目录。','A name to help you identify the storage if you have multiple remote storage connected.'=>'如果您连接了多个远程存储，该名称可帮助您识别他们。','Enter a unique alias: e.g. Dropbox-001'=>'输入唯一别名。如：Dropbox-001','Enter Your Dropbox Information'=>'输入Dropbox信息','Dropbox'=>'Dropbox','You have authenticated the Dropbox account as your remote storage.'=>'您已将 Dropbox 帐户验证为您的远程存储。','The backup will be canceled after backing up the current chunk ends.'=>'备份当前块结束后将取消备份。','running time: '=>'运行时间： ','Progress: '=>'进度： ','Ready to backup. Progress: 0%, running time: 0second.'=>'准备备份。进度：0%，运行时间：0 秒。','This does not appear to be a WXR file, missing/invalid WXR version number'=>'这似乎不是 WXR 文件，缺少/WXR 版本号无效','Details are shown above. The importer will now try again with a different parser...'=>'详细信息如上所示，导入器现在将使用不同的解析器重试…','There was an error when reading this WXR file'=>'读取此 WXR 文件时出错','Invalid file type'=>'无效的文件类型','Fetching attachments is not enabled'=>'未启用获取附件','Sorry, this file type is not permitted for security reasons.'=>'抱歉，处于安全原因，不允许使用这个类型的文件。','Last page'=>'尾页','Next page'=>'下一页','paging%1$s of %2$s'=>'%1$s / %2$s','Current Page'=>'当前页','Previous page'=>'上一页','First page'=>'首页','%s item'=>'%s 项','Type: '=>'类型： ','Media Files Size'=>'媒体文件尺寸','Count'=>'计数','Post Types'=>'文章类型','File Name'=>'文件名称','Select Files'=>'选择文件','Uploader: Drop files here - or - Select Filesor'=>'或','Drop files here'=>'拖放文件到这里','Scan uploaded backup or received backup'=>'扫描已上传备份或接收备份','Tips: Click the button below to scan all uploaded or received backups in directory'=>'使用技巧：点击下面的按钮扫描目录中所有已上传或接收的备份文件','Warning: There is no default remote storage available for the scheduled backups, please set up it first.'=>'警告：没有可用于计划备份的默认远程存储，请先设置。','Next Backup: '=>'下一次备份： ','Server Time: '=>'服务器时间： ','Schedule Status: '=>'计划状态： ','Backup Schedule'=>'备份计划','The settings are only for manual backup, which won\'t affect schedule settings.'=>'这些设置仅作用于手动备份，不会影响计划设置。','This backup can only be deleted manually'=>'此备份只能手动删除','Backup Now'=>'立即备份','Send Backup to Remote Storage:'=>'发送备份到远程存储：','Save Backups to Local'=>'保存备份到本机','rename directory'=>'重命名目录','Local Storage Directory:'=>'本机存储目录：','Back Up Manually'=>'手动备份','Cancel'=>'取消','Network Connection:'=>'网络连接：','Speed:'=>'速度：','Uploaded:'=>'上传：','The backup is stored on the remote storage, click on the button to download it to localhost.'=>'此备份存储在远程存储上，点击下面的按钮将其下载到本机。','Retrieve the backup to localhost'=>'下载备份到本机','Restore function will replace the current site\'s themes, plugins, uploads, database and/or other content directories with the existing equivalents in the selected backup.'=>'恢复功能会将当前站点的主题、插件、上传、数据库和/或其他内容目录替换为所选备份中的现有等效目录。','Please do not close the page or switch to other pages when a restore task is running, as it could trigger some unexpected errors.'=>'请不要在恢复任务运行时关闭页面或切换到其他页面，这可能会触发一些意外错误。','Restore backup from:'=>'从以下位置恢复备份：','Step Three: Click \'Restore\' button'=>'第三步：点击“还原”按钮','Step Two: Choose an option to complete restore, if any'=>'第二步：选择一项以完成还原（如果有）','Step One: In the backup list, click the \'Restore\' button on the backup you want to restore. This will bring up the restore tab'=>'第一步：在备份列表中，单击要还原的备份上的“还原”按钮。这将打开还原选项卡','This request might delete the backup being downloaded, are you sure you want to continue?'=>'此请求可能会删除正在下载的备份，您确定要继续吗？','Please select at least one item.'=>'请至少选择一项。','This request will delete the backup being downloaded, are you sure you want to continue?'=>'此请求将删除正在下载的备份，您确定要继续吗？','This backup is locked, are you sure to remove it? This backup will be deleted permanently from your hosting (localhost) and remote storages.'=>'此备份已锁定，您确定将其删除吗？此备份将从您的主机 (localhost) 和远程存储中永久删除。','Delete the selected backups'=>'删除选中的备份','Delete'=>'删除','Storage'=>'存储','Backup'=>'备份','Restore'=>'还原','Backups'=>'备份','How to restore your website from a backup(scheduled, manual, uploaded and received backup)'=>'如何从备份（计划、手动、上传和接收的备份）中恢复您的网站','->If backups are stored in web server, the plugin will list all relevant files immediately.'=>'->如果备份存储在你的Web服务器上，该插件将立即列出所有相关文件。','->If backups are stored in remote storage, our plugin will retrieve the backup to your web server first. This may take a little time depending on the size of backup files. Please be patient. Then you can download them to your PC.'=>'->如果备份保存在远程存储上，我们的插件会先把他下载到你的Web服务器上，此操作可能需要一些时间，具体取决于您的备份尺寸，请耐心等候，完成后，你可以将他们下载到你的PC上。','About backup download'=>'关于备份下载','Actions'=>'操作','Remote Storage Alias'=>'远程存储别名','Storage Provider'=>'存储提供商','Please choose one storage to save your backups (remote storage)'=>'请选择一个存储来保存你的备份(远程存储)','Storage Edit'=>'存储编辑','Storages'=>'存储','Advanced Settings'=>'高级设置','General Settings'=>'常规设置','e.g. if you choose a chunk size of 2MB, a 8MB file will use 4 chunks. Decreasing this value will break the ISP\'s transmission limit, for example:512KB'=>'例如，如果您选择2MB的块大小，一个8MB的文件将使用4个块。减小此值将超过ISP的传输限制，例如：512KB','Chunk Size'=>'分块尺寸','Adjust this value to apply for a temporary PHP memory limit for WPvivid backup plugin in restore process. We set this value to 256M by default. Increase the value if you encounter a memory exhausted error. Note: some web hosting providers may not support this.'=>'调整此值以在还原过程中为WPviid备份插件应用临时的PHP内存限制。默认情况下，我们将此值设置为256M。如果遇到内存耗尽错误，请增加该值。注意：某些虚拟主机提供商可能不支持此功能。','PHP Memory Limit for restoration'=>'用于恢复的 PHP 内存限制','Adjust this value to apply for a temporary PHP memory limit for WPvivid backup plugin to run a backup. We set this value to 256M by default. Increase the value if you encounter a memory exhausted error. Note: some web hosting providers may not support this.'=>'调整此值以应用临时的PHP内存限制，以便WPviid备份插件运行备份。默认情况下，我们将此值设置为256M。如果遇到内存耗尽错误，请增加该值。注意：某些虚拟主机提供商可能不支持此功能。','PHP Memory Limit for backup'=>'用于备份的PHP内存限制','The time-out is not your server PHP time-out. With the execution time exhausted, our plugin will shut the process of restore down. If the progress of restore encounters a time-out, that means you have a medium or large sized website, please try to scale the value bigger.'=>'超时不是您的服务器 PHP 超时。随着执行时间耗尽，我们的插件将关闭恢复过程。如果还原进度遇到超时，说明您有一个中型或大型网站，请尝试将值放大。','PHP script execution timeout for restore'=>'用于恢复的PHP脚本执行超时','The time-out is not your server PHP time-out. With the execution time exhausted, our plugin will shut the process of backup down. If the progress of backup encounters a time-out, that means you have a medium or large sized website, please try to scale the value bigger.'=>'超时不是您的服务器 PHP 超时。随着执行时间耗尽，我们的插件将关闭备份过程。如果备份进度遇到超时，说明您有一个中型或大型网站，请尝试将值放大。','PHP script execution timeout for backup'=>'用于备份的 PHP 脚本执行超时','Using the option will ignore the file larger than the certain size in MB when backing up, \'0\' (zero) means unlimited.'=>'使用该选项将在备份时忽略大于特定大小(MB)的文件，‘0’(零)表示无限制。','Exclude the files which are larger than'=>'排除大于该值的文件','Some web hosting providers limit large zip files (e.g. 200MB), and therefore splitting your backup into many parts is an ideal way to avoid hitting the limitation if you are running a big website.  Please try to adjust the value if you are encountering backup errors. When you set a value of 0MB, backups will be split every 4GB.'=>'一些网络托管提供商限制大压缩文件（例如200MB），因此将您的备份分成多个部分是避免达到限制的理想方法，如果您运营一个大型的网站。请尝试调整值，如果您遇到备份错误。当您设置0MB的值时，备份将每4GB分割一次。','Compress Files Every'=>'压缩文件时间间隔','It is recommended to choose PDO option if pdo_mysql extension is installed on your server, which lets you backup and restore your site faster.'=>'如果您的服务器上安装了PDO_MySQL扩展，则建议选择PDO选项，这样可以更快地备份和恢复站点。','WPDB option has a better compatibility, but the speed of backup and restore is slower.'=>'WPDB 选项具有更好的兼容性，但备份和恢复的速度较慢。','Database access method.'=>'数据库访问方法。','Importing the json file can help you set WPvivid\'s configuration on another wordpress site quickly.'=>'导入json文件可以帮你在另外一个WordPress站点上快速设置WPvivid选项。','Import'=>'导入','Click \'Export\' button to save WPvivid settings on your local computer.'=>'点击「导出」按钮保存WPvivid设置到你的电脑上。','Export'=>'导出','Empty'=>'清空','Calculate Sizes'=>'计算尺寸','Total Size:'=>'总尺寸：','Web-server disk space in use by WPvivid'=>'WPvivid使用的Web服务器磁盘空间','Pro feature: Add another email address to get report'=>'高级版功能：添加另外一个电子邮件来接受报告','Only send an email notification when a backup fails'=>'仅在备份失败时发送电子邮件','Always send an email notification when a backup is complete'=>'备份完成时发送电子邮件','Test Email'=>'测试电子邮件','Enable email report'=>'启用电子邮件报告','The action is irreversible! It will remove all backups are out-of-date (including local web server and remote storage) if they exist.'=>'此操作不可逆！如果存在过时的备份（包括Web服务器本机和远程存储），此操作将删除所有过时备份。','Remove'=>'删除','Remote Storage Directory:'=>'远程存储目录：','Web Server Directory:'=>'Web服务器目录：','Remove out-of-date backups'=>'移除过时备份','Display domain(url) of current site in backup name. (e.g. domain_wpvivid-5ceb938b6dca9_2019-05-27-07-36_backup_all.zip)'=>'在备份名称中显示当前站点的域名（url），（如 domain_wpvivid-5ceb938b6dca9_2019-05-27-07-36_backup_all.zip）','Local storage directory:'=>'本机存储目录：','Name your folder, this folder must be writable for creating backup files.'=>'命名您的文件夹，此文件夹必须可写才能创建备份文件。','Backup Folder'=>'备份目录','Keep storing the backups in localhost after uploading to remote storage'=>'上传到远程存储后，将备份存储在本机','Merge all the backup files into single package when a backup completes. This will save great disk spaces, though takes longer time. We recommended you check the option especially on sites with insufficient server resources.'=>'备份完成后，将所有备份文件合并到一个包中。此操作会节省大量磁盘空间，但需要更长的时间。我们建议您选中此项，尤其是在服务器资源不足的站点上。','Show WPvivid backup plugin on top admin bar'=>'在顶部管理栏上显示 WPvivid 备份插件','Calculate the size of files, folder and database before backing up'=>'备份前计算文件、文件夹和数据库的大小','Pro feature: Retain more backups'=>'专业版功能：保留更多备份','backups retained'=>'备份保留','Custom'=>'自定义','Only Database'=>'数据库','WordPress Files (Exclude Database)'=>'WordPress文件(不包含数据库)','Database + Files (WordPress Files)'=>'数据库+文件(WordPress文件)','Being subjected to mechanisms of PHP, a scheduled backup task for your site will be triggered only when the site receives at least a visit at any page.'=>'受限于PHP机制，只有每个页面被访问时，计划备份任务才被触发。','+ Add another schedule'=>'+ 添加另外一个计划','Highlighted icon illuminates that you have choosed a remote storage to store backups'=>'突出显示的图标表示您已选择用来存储备份的远程存储','Advanced Schedule'=>'高级计划','Pro feature: learn more'=>'专业版功能：了解更多','Enable Incremental Backup'=>'启用增量备份','Enable backup schedule'=>'启用备份计划','Schedule Settings'=>'计划设置','Support'=>'技术支持','Ultimate'=>'终极','Freelancer'=>'自由职业者','5. Set up remote storage for child sites in bulk (for WPvivid Backup Pro only)'=>'5. 为子站点批量设置远程存储（仅适用于 WPvivid Backup Pro）','4. Install, claim and update WPvivid Backup Pro for child sites in bulk'=>'4. 为子站点批量安装、声明和更新 WPvivid Backup Pro','3. Set WPvivid Backup Free and Pro settings for all child sites'=>'3. 为所有子站点设置 WPvivid Backup Free 和 Pro 选项','2. Set backup schedules for all child sites'=>'2. 为所有子站点设置备份计划','1. Create and download backups for a specific child site'=>'1. 为特定子站点创建和下载备份','Download WPvivid Backup for MainWP'=>'为 MainWP下载WPvivid Backup','If you are a MainWP user, you can set up and control WPvivid Backup Free and Pro for every child site directly from your MainWP dashboard, using our WPvivid Backup for MainWP extension.'=>'如果您是MainWP用户，可以使用我们的WPvivid Backup for MainWP扩展直接从您的MainWP仪表板设置和控制 WPvivid Backup 免费版和专业版。',' Next page > '=>' 下一页 > ',' < Pre page '=>' < 上一页 ','Action'=>'操作','Log File Name'=>'日志文件名称','Log Type'=>'日志类型','Date'=>'日期','Website Info Value'=>'网站信息值','Website Info Key'=>'网站信息键','Download'=>'下载','If you didn’t configure SMTP on your site, click the button below to download the relevant information (website info and error logs) to your PC when you are encountering some errors. Sending the files to us will help us diagnose what happened.'=>'如果您没有在您的站点上配置 SMTP，遇到错误时，请单击下面的按钮将相关信息（网站信息和错误日志）下载到您的 PC。然后发送给我们，这将帮助我们诊断发生的情况。','Method 2.'=>'方法2.','Send Debug Information to Us'=>'发送调试信息给我们','Please describe your problem here.'=>'请在这里描述你的问题。','My web hosting provider is:'=>'我的主机提供商是：','I am using:'=>'我正在使用：','Your email:'=>'您的电子邮件：','WPvivid support email:'=>'WPvivid 技术支持电子邮件:','If you have configured SMTP on your site, enter your email address and click the button below to send us the relevant information (website info and errors logs) when you are encountering errors. This will help us figure out what happened. Once the issue is resolved, we will inform you by your email address.'=>'如果您在您的网站上配置了 SMTP，请输入您的电子邮件地址，然后在遇到错误时单击下面的按钮将相关信息（网站信息和错误日志）发送给我们。这将帮助我们弄清楚发生了什么。问题解决后，我们将通过您的电子邮件地址通知您。','Method 1.'=>'方法1.','There are two ways available to send us the debug information. The first one is recommended.'=>'有两种方法可以向我们发送调试信息，推荐使用第一个。','Save Changes'=>'保存修改','Premium'=>'高级版','MainWP'=>'MainWP','Log'=>'日志','Logs'=>'日志','Debug'=>'调试','Remote Storage'=>'远程存储','Schedule'=>'计划','Class PclZip is not detected. Please update or reinstall your WordPress.'=>'未检测到 PclZip 类。请更新或重新安装您的 WordPress。','As Amazon S3 and DigitalOcean Space have upgraded their connection methods, please delete the previous connections and re-add your Amazon S3/DigitalOcean Space accounts to make sure the connections work.'=>'由于 Amazon S3 和 DigitalOcean Space 升级了连接方式，请删除之前的连接并重新添加您的 Amazon S3/DigitalOcean Space 帐户以确保连接正常。','Restore completed successfully.'=>'还原已成功完成。','Cheers! WPvivid Backup plugin has restored successfully your website. If you found WPvivid Backup plugin helpful, a 5-star rating would be highly appreciated, which motivates us to keep providing new features.'=>'干杯! WPvivid Backup 插件已成功恢复您的网站。如果您觉得 WPvivid Backup 插件有用，请给个5星好评，我们不胜感激，并且这将激励我们不断提供新功能。','Migration is complete and htaccess file is replaced. In order to successfully complete the migration, you\'d better reinstall 301 redirect plugin, firewall and security plugin, and caching plugin if they exist.'=>'迁移已完成，并替换了htaccess文件。为了成功完成迁移，您最好重新安装301重定向插件、防火墙和安全插件以及缓存插件(如果存在)。','Calculating the size of files, folder and database timed out. If you continue to receive this error, please go to the plugin settings, uncheck \'Calculate the size of files, folder and database before backing up\', save changes, then try again.'=>'计算文件、文件夹和数据库的大小超时。如果您持续收到此错误，请转到插件设置，取消选中“备份前计算文件、文件夹和数据库的大小”，保存更改，然后重试。','Warning: The alias already exists in storage list.'=>'警告：存储列表中已存在该别名。','Warning: An alias for remote storage is required.'=>'警告：远程存储别名为必填项。','Error:'=>'错误：','Warning:'=>'警告：','Migrate WordPress'=>'迁移WordPress','Restore Your Site from a Backup'=>'从备份中还原网站','Create a Manual Backup'=>'创建一个手动备份','WPvivid Backup Settings'=>'WPvivid备份设置','How-to'=>'使用教程','Troubleshooting'=>'故障排除','ChangeLog'=>'更新日志','Current Version: '=>'当前版本： ','Settings'=>'设置','Backup & Restore'=>'备份&恢复','https://wpvivid.com'=>'https://wpvivid.com','Clone or copy WP sites then move or migrate them to new host (new domain), schedule backups, transfer backups to leading remote storage. All in one.'=>'克隆或复制WP站点，然后将它们移动或迁移到新主机（新域名），计划备份，将备份传输到领先的远程存储。一站式解决方案。','WPvivid Backup Plugin'=>'WPvivid备份插件']];