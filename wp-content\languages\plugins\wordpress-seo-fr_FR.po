# Translation of Plugins - Yoast SEO &#8211; Advanced SEO with real-time guidance and built-in AI - Stable (latest release) in French (France)
# This file is distributed under the same license as the Plugins - Yoast SEO &#8211; Advanced SEO with real-time guidance and built-in AI - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-07-15 09:50:39+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: fr\n"
"Project-Id-Version: Plugins - Yoast SEO &#8211; Advanced SEO with real-time guidance and built-in AI - Stable (latest release)\n"

#: src/plans/user-interface/plans-page-integration.php:119 js/dist/plans.js:7
msgid "Plans"
msgstr "Formules"

#: src/llms-txt/application/file/file-failure-notification-presenter.php:49
#: src/llms-txt/user-interface/health-check/file-reports.php:86
msgid "You have activated the Yoast llms.txt feature, but we couldn't generate an llms.txt file, for unknown reasons."
msgstr "Vous avez activé la fonctionnalité llms.txt de Yoast, mais nous n’avons pas pu générer un fichier llms.txt, pour des raisons inconnues."

#. translators: 1,3: expand to opening paragraph tag, 2,4: expand to opening
#. paragraph tag.
#: src/llms-txt/user-interface/health-check/file-reports.php:77
msgid "%1$sYou have activated the Yoast llms.txt feature, but we couldn't generate an llms.txt file.%2$s%3$sIt looks like there aren't sufficient permissions on the web server's filesystem.%4$s"
msgstr "%1$sVous avez activé la fonctionnalité llms.txt de Yoast, mais nous n’avons pas pu générer le fichier llms.txt.%2$s%3$sIl semble que les droits du système de fichiers du serveur web soient insuffisantes.%4$s"

#. translators: 1,3,5: expand to opening paragraph tag, 2,4,6: expand to
#. opening paragraph tag.
#: src/llms-txt/user-interface/health-check/file-reports.php:64
msgid "%1$sYou have activated the Yoast llms.txt feature, but we couldn't generate an llms.txt file.%2$s%3$sIt looks like there is an llms.txt file already that wasn't created by Yoast, or the llms.txt file created by Yoast has been edited manually.%4$s%5$sWe don't want to overwrite this file's content, so if you want to let Yoast keep auto-generating the llms.txt file, you can manually delete the existing one. Otherwise, consider disabling the Yoast feature.%6$s"
msgstr "%1$sVous avez activé la fonctionnalité llms.txt de Yoast, mais nous n’avons pas pu générer le fichier llms.txt.%2$s%3$sIl semble qu’un fichier llms.txt existe déjà, qu’il n’a pas été créé par Yoast ou qu’il a été modifié manuellement.%4$s%5$sNous ne souhaitons pas écraser le contenu de ce fichier. Si vous souhaitez que Yoast continue à générer automatiquement le fichier llms.txt, vous pouvez supprimer manuellement celui qui existe actuellement. Dans le cas contraire, envisagez de désactiver la fonctionnalité Yoast.%6$s"

#: src/llms-txt/application/file/file-failure-notification-presenter.php:55
#: src/llms-txt/user-interface/health-check/file-reports.php:61
#: src/llms-txt/user-interface/health-check/file-reports.php:74
#: src/llms-txt/user-interface/health-check/file-reports.php:85
msgid "Your llms.txt file couldn't be auto-generated"
msgstr "Votre fichier llms.txt n’a pas pu être généré automatiquement"

#. translators: %s: Yoast SEO.
#: src/llms-txt/user-interface/health-check/file-reports.php:40
msgid "%s keeps your llms.txt file up-to-date. This helps LLMs access and provide your site's information more easily."
msgstr "%s maintient votre fichier llms.txt à jour. Cela aide les modèles LLM à accéder plus facilement aux informations de votre site et à les fournir."

#. translators: %s: Yoast SEO.
#: src/llms-txt/user-interface/health-check/file-reports.php:34
msgid "Your llms.txt file is auto-generated by %s"
msgstr "Votre fichier llms.txt est généré automatiquement par %s"

#: src/dashboard/user-interface/tracking/setup-steps-tracking-route.php:149
msgid "No valid parameters were passed."
msgstr "Aucun paramètre valide n’a été transmis."

#: src/integrations/admin/link-count-columns-integration.php:147
msgid "Number of internal links linking to this post."
msgstr "Nombre de liens internes renvoyant à cette publication."

#: src/integrations/admin/link-count-columns-integration.php:139
msgid "Number of outgoing internal links in this post."
msgstr "Nombre de liens internes sortants de cette publication."

#: inc/class-wpseo-rank.php:223 js/dist/externals/dashboardFrontend.js:4
msgid "Not analyzed"
msgstr "Non analysé"

#. translators: %s: Yoast SEO.
#: wp-seo-main.php:564
msgid "%s activation failed"
msgstr "Activation échouée : %s"

#. translators: %s: Yoast SEO.
#: src/presenters/admin/migration-error-presenter.php:42
msgid "%s is unable to create database tables"
msgstr "%s est incapable de créer les tables de la base de données"

#: src/presenters/admin/sidebar-presenter.php:81 js/dist/block-editor.js:27
#: js/dist/classic-editor.js:12 js/dist/elementor.js:12
#: js/dist/externals-components.js:199 js/dist/general-page.js:14
#: js/dist/integrations-page.js:48 js/dist/new-settings.js:14
#: js/dist/post-edit.js:12 js/dist/support.js:14 js/dist/term-edit.js:12
msgid "Buy now"
msgstr "Acheter maintenant"

#: src/presenters/admin/sidebar-presenter.php:70 js/dist/block-editor.js:26
#: js/dist/classic-editor.js:11 js/dist/elementor.js:11
#: js/dist/externals-components.js:198 js/dist/general-page.js:13
#: js/dist/integrations-page.js:47 js/dist/new-settings.js:13
#: js/dist/post-edit.js:11 js/dist/support.js:13 js/dist/term-edit.js:11
msgid "If you were thinking about upgrading, now's the time! 30% OFF ends 3rd Dec 11am (CET)"
msgstr "Si vous envisagiez de passer à la version supérieure, c’est le moment ! 30 % de réduction, fin le 3 déc. à 11 h (CET)"

#. translators: %1$s and %2$s expand to a span wrap to avoid linebreaks. %3$s
#. expands to "Yoast SEO Premium".
#: src/presenters/admin/sidebar-presenter.php:59 js/dist/block-editor.js:27
#: js/dist/classic-editor.js:12 js/dist/elementor.js:12
#: js/dist/externals-components.js:199 js/dist/general-page.js:14
#: js/dist/integrations-page.js:48 js/dist/new-settings.js:14
#: js/dist/post-edit.js:12 js/dist/support.js:14 js/dist/term-edit.js:12
msgid "%1$sBuy%2$s %3$s"
msgstr "%1$sAcheter%2$s %3$s"

#: src/presenters/admin/sidebar-presenter.php:20 js/dist/block-editor.js:27
#: js/dist/block-editor.js:282 js/dist/classic-editor.js:12
#: js/dist/classic-editor.js:267 js/dist/editor-modules.js:173
#: js/dist/elementor.js:12 js/dist/elementor.js:64
#: js/dist/externals-components.js:186 js/dist/externals-components.js:199
#: js/dist/general-page.js:14 js/dist/integrations-page.js:48
#: js/dist/new-settings.js:14 js/dist/post-edit.js:12 js/dist/support.js:14
#: js/dist/term-edit.js:12
msgid "30% OFF - BLACK FRIDAY"
msgstr "30 % DE RÉDUCTION – BLACK FRIDAY"

#. translators: %s expands to Yoast SEO Premium
#: admin/class-premium-upsell-admin-block.php:100
msgid "Upgrade now"
msgstr "Mettre à niveau maintenant"

#: src/integrations/admin/check-required-version.php:115
msgid "Required Yoast SEO version"
msgstr "Version requise de l’extension Yoast SEO"

#: src/integrations/admin/check-required-version.php:91
msgid "The package could not be installed because it's not supported by the currently installed Yoast SEO version."
msgstr "Le paquet n’a pas pu être installé, car il n’est pas pris en charge par la version actuelle de l’extension Yoast SEO."

#. translators: 1: Current Yoast SEO version, 2: Version required by the
#. uploaded plugin.
#: src/integrations/admin/check-required-version.php:84
msgid "The Yoast SEO version on your site is %1$s, however the uploaded plugin requires %2$s."
msgstr "La version de l’extension Yoast SEO sur votre site est %1$s, alors que la version téléversée nécessite %2$s."

#: src/user-meta/framework/custom-meta/noindex-author.php:108
msgid "Do not allow search engines to show this author's archives in search results."
msgstr "Ne pas autoriser les moteurs de recherche à afficher les archives de cet auteur/autrice dans les résultats de recherche."

#: admin/menu/class-base-menu.php:260 admin/menu/class-base-menu.php:264
msgid "Upgrades"
msgstr "Mises à niveau"

#: blocks/dynamic-blocks/breadcrumbs/block.json
msgctxt "block keyword"
msgid "site structure"
msgstr "structure de site"

#: blocks/dynamic-blocks/breadcrumbs/block.json
msgctxt "block keyword"
msgid "internal linking"
msgstr "maillage interne"

#: blocks/dynamic-blocks/breadcrumbs/block.json
msgctxt "block keyword"
msgid "breadcrumbs"
msgstr "fil d’Ariane"

#: blocks/dynamic-blocks/breadcrumbs/block.json
msgctxt "block description"
msgid "Adds the Yoast SEO breadcrumbs to your template or content."
msgstr "Ajoute le fil d‘Ariane de Yoast SEO à votre modèle ou à votre contenu."

#: blocks/dynamic-blocks/breadcrumbs/block.json
msgctxt "block title"
msgid "Yoast Breadcrumbs"
msgstr "Fil d‘Ariane Yoast"

#: blocks/structured-data-blocks/how-to/block.json
msgctxt "block keyword"
msgid "How to"
msgstr "Tutoriel"

#: blocks/structured-data-blocks/how-to/block.json
msgctxt "block keyword"
msgid "How-to"
msgstr "Tutoriel"

#: blocks/structured-data-blocks/how-to/block.json
msgctxt "block title"
msgid "Yoast How-to"
msgstr "Tutoriel Yoast"

#: blocks/structured-data-blocks/faq/block.json
#: blocks/structured-data-blocks/how-to/block.json
msgctxt "block keyword"
msgid "Structured Data"
msgstr "Données structurées"

#: blocks/dynamic-blocks/breadcrumbs/block.json
#: blocks/structured-data-blocks/faq/block.json
#: blocks/structured-data-blocks/how-to/block.json
msgctxt "block keyword"
msgid "SEO"
msgstr "SEO"

#: blocks/structured-data-blocks/faq/block.json
#: blocks/structured-data-blocks/how-to/block.json
msgctxt "block keyword"
msgid "Schema"
msgstr "Schéma"

#: blocks/structured-data-blocks/faq/block.json
msgctxt "block keyword"
msgid "Frequently Asked Questions"
msgstr "Foire aux questions"

#: blocks/structured-data-blocks/faq/block.json
msgctxt "block keyword"
msgid "FAQ"
msgstr "FAQ"

#: blocks/structured-data-blocks/faq/block.json
msgctxt "block description"
msgid "List your Frequently Asked Questions in an SEO-friendly way."
msgstr "Lister votre Foire aux questions optimisée pour les moteurs de recherche."

#: blocks/structured-data-blocks/faq/block.json
msgctxt "block title"
msgid "Yoast FAQ"
msgstr "FAQ Yoast"

#: admin/class-admin.php:315
#: src/user-meta/framework/additional-contactmethods/x.php:28
msgid "X username (without @)"
msgstr "Identifiant X (sans @)"

#: src/presenters/admin/sidebar-presenter.php:94 js/dist/block-editor.js:27
#: js/dist/classic-editor.js:12 js/dist/elementor.js:12
#: js/dist/externals-components.js:199 js/dist/general-page.js:14
#: js/dist/integrations-page.js:48 js/dist/new-settings.js:14
#: js/dist/post-edit.js:12 js/dist/support.js:14 js/dist/term-edit.js:12
msgid "30-day money back guarantee."
msgstr "Garantie satisfait-remboursé de 30 jours."

#. translators: 1: PHP class name, 2: PHP variable name
#: inc/class-yoast-dynamic-rewrites.php:67
msgid "The %1$s class must not be instantiated before the %2$s global is set."
msgstr "La classe %1$s ne doit pas être instanciée avant que le global %2$s ne soit défini."

#. translators: %s expands to Yoast SEO Premium
#. translators: %s expands to "Yoast SEO" Premium
#: admin/class-premium-upsell-admin-block.php:100 js/dist/block-editor.js:35
#: js/dist/classic-editor.js:20 js/dist/elementor.js:20
#: js/dist/externals-components.js:201 js/dist/general-page.js:22
#: js/dist/integrations-page.js:56 js/dist/new-settings.js:22
#: js/dist/post-edit.js:20 js/dist/support.js:22 js/dist/term-edit.js:20
msgid "Explore %s now!"
msgstr "Explorer %s maintenant !"

#. translators: %1$s expands to a strong opening tag, %2$s expands to a strong
#. closing tag.
#: admin/class-premium-upsell-admin-block.php:88 js/dist/block-editor.js:33
#: js/dist/classic-editor.js:18 js/dist/editor-modules.js:180
#: js/dist/elementor.js:18 js/dist/externals-components.js:148
#: js/dist/general-page.js:21 js/dist/integrations-page.js:55
#: js/dist/new-settings.js:21 js/dist/post-edit.js:19 js/dist/support.js:21
#: js/dist/term-edit.js:19
msgid "%1$s24/7 support%2$s: Also on evenings and weekends."
msgstr "%1$sSupport 24/7%2$s : Également le soir et le weekend."

#. translators: %1$s expands to a strong opening tag, %2$s expands to a strong
#. closing tag.
#: admin/class-premium-upsell-admin-block.php:82 js/dist/block-editor.js:32
#: js/dist/classic-editor.js:17 js/dist/editor-modules.js:179
#: js/dist/elementor.js:17 js/dist/externals-components.js:147
#: js/dist/general-page.js:20 js/dist/integrations-page.js:54
#: js/dist/new-settings.js:20 js/dist/post-edit.js:18 js/dist/support.js:20
#: js/dist/term-edit.js:18
msgid "%1$sAppealing social previews%2$s people actually want to click on."
msgstr "%1$sDes aperçus sociaux attrayants%2$s sur lesquels les gens ont envie de cliquer."

#. translators: %1$s expands to a strong opening tag, %2$s expands to a strong
#. closing tag.
#: admin/class-premium-upsell-admin-block.php:76 js/dist/block-editor.js:31
#: js/dist/classic-editor.js:16 js/dist/editor-modules.js:178
#: js/dist/elementor.js:16 js/dist/externals-components.js:146
#: js/dist/general-page.js:19 js/dist/integrations-page.js:53
#: js/dist/new-settings.js:19 js/dist/post-edit.js:17 js/dist/support.js:19
#: js/dist/term-edit.js:17
msgid "%1$sNo more broken links%2$s: Automatic redirect manager."
msgstr "%1$sPlus de liens cassés%2$s : Gestionnaire de redirection automatique."

#. translators: %1$s expands to a strong opening tag, %2$s expands to a strong
#. closing tag.
#: admin/class-premium-upsell-admin-block.php:70 js/dist/block-editor.js:30
#: js/dist/classic-editor.js:15 js/dist/editor-modules.js:177
#: js/dist/elementor.js:15 js/dist/externals-components.js:145
#: js/dist/general-page.js:18 js/dist/integrations-page.js:52
#: js/dist/new-settings.js:18 js/dist/post-edit.js:16 js/dist/support.js:18
#: js/dist/term-edit.js:16
msgid "%1$sSuper fast%2$s internal linking suggestions."
msgstr "Suggestions de liens internes %1$ssuper rapides%2$s."

#. translators: %1$s expands to a strong opening tag, %2$s expands to a strong
#. closing tag.
#: admin/class-premium-upsell-admin-block.php:64 js/dist/block-editor.js:29
#: js/dist/classic-editor.js:14 js/dist/editor-modules.js:176
#: js/dist/elementor.js:14 js/dist/externals-components.js:144
#: js/dist/general-page.js:17 js/dist/integrations-page.js:51
#: js/dist/new-settings.js:17 js/dist/post-edit.js:15 js/dist/support.js:17
#: js/dist/term-edit.js:15
msgid "%1$sMultiple keywords%2$s: Rank higher for more searches."
msgstr "%1$sMots-clés multiples%2$s : Obtenez un meilleur classement pour plus de recherches."

#. translators: %1$s expands to a strong opening tag, %2$s expands to a strong
#. closing tag.
#: admin/class-premium-upsell-admin-block.php:58 js/dist/block-editor.js:28
#: js/dist/classic-editor.js:13 js/dist/editor-modules.js:175
#: js/dist/elementor.js:13 js/dist/externals-components.js:143
#: js/dist/general-page.js:16 js/dist/integrations-page.js:50
#: js/dist/new-settings.js:16 js/dist/post-edit.js:14 js/dist/support.js:16
#: js/dist/term-edit.js:14
msgid "%1$sAI%2$s: Better SEO titles and meta descriptions, faster."
msgstr "%1$sIA%2$s : De meilleurs titres et méta descriptions pour le référencement, plus rapidement."

#: admin/watchers/class-slug-change-watcher.php:68
msgid "Search engines and other websites can still send traffic to your trashed content."
msgstr "Les moteurs de recherche et d’autres sites peuvent continuer à envoyer du trafic à votre contenu dans la corbeille."

#. translators: 1: Yoast SEO, 2: Link start tag to the Learn more link, 3: Link
#. closing tag.
#: src/presenters/admin/woocommerce-beta-editor-presenter.php:53
msgid "The %1$s interface is currently unavailable in the beta WooCommerce product editor. To resolve any issues, please disable the beta editor. %2$sLearn how to disable the beta WooCommerce product editor.%3$s"
msgstr "L‘interface de %1$s est actuellement indisponible dans la version bêta de l‘éditeur de produits de WooCommerce. Pour résoudre tout problème, veuillez désactiver l‘éditeur bêta. %2$sApprendre à désactiver l‘éditeur bêta de produits de WooCommerce.%3$s"

#: src/presenters/admin/woocommerce-beta-editor-presenter.php:50
msgid "Compatibility issue: Yoast SEO is incompatible with the beta WooCommerce product editor."
msgstr "Problème de compatibilité : Yoast SEO est incompatible avec la version bêta de l‘éditeur de produits de WooCommerce."

#: src/presenters/admin/sidebar-presenter.php:73 js/dist/block-editor.js:25
#: js/dist/classic-editor.js:10 js/dist/elementor.js:10
#: js/dist/externals-components.js:197 js/dist/general-page.js:12
#: js/dist/integrations-page.js:46 js/dist/new-settings.js:12
#: js/dist/post-edit.js:10 js/dist/support.js:12 js/dist/term-edit.js:10
msgid "Use AI to generate titles and meta descriptions, automatically redirect deleted pages, get 24/7 support, and much, much more!"
msgstr "Utilisez l‘IA pour générer des titres et des méta descriptions, redirigez automatiquement les pages supprimées, profitez du support 24/7 et bien plus encore !"

#. translators: %1$s is a <br> tag.
#: inc/class-addon-manager.php:413
msgid "%1$s Now with 30%% Black Friday Discount!"
msgstr "%1$s avec 30%% de remise pour le Black Friday !"

#: admin/class-premium-upsell-admin-block.php:116
#: admin/menu/class-base-menu.php:264 inc/class-wpseo-admin-bar-menu.php:597
#: js/dist/block-editor.js:33 js/dist/classic-editor.js:18
#: js/dist/elementor.js:18 js/dist/externals-components.js:199
#: js/dist/general-page.js:14 js/dist/integrations-page.js:48
#: js/dist/new-settings.js:14 js/dist/post-edit.js:12 js/dist/support.js:14
#: js/dist/term-edit.js:12
msgid "30% OFF"
msgstr "30% de réduction"

#: admin/class-premium-upsell-admin-block.php:115 js/dist/block-editor.js:33
#: js/dist/classic-editor.js:18 js/dist/elementor.js:18
#: js/dist/externals-components.js:199 js/dist/general-page.js:14
#: js/dist/integrations-page.js:48 js/dist/new-settings.js:14
#: js/dist/post-edit.js:12 js/dist/support.js:14 js/dist/term-edit.js:12
msgid "BLACK FRIDAY"
msgstr "BLACK FRIDAY"

#: admin/views/class-yoast-feature-toggles.php:209
msgid "Use the power of Yoast AI to automatically generate compelling titles and descriptions for your posts and pages."
msgstr "Utilisez le pouvoir de Yoast AI pour générer automatiquement des titres et descriptions convaincants pour vos publications."

#: admin/views/class-yoast-feature-toggles.php:206 js/dist/new-settings.js:38
#: js/dist/new-settings.js:312
msgid "AI title & description generator"
msgstr "Générateur de titre et description avec IA"

#. translators: %s expands to a unit of time (e.g. 1 day).
#. translators: %1$s, %2$s and %3$s expand to units of time (e.g. 1 day).
#: src/integrations/blocks/structured-data-blocks.php:178
#: js/dist/how-to-block.js:10 js/dist/how-to-block.js:16
msgid "%1$s, %2$s and %3$s"
msgstr "%1$s, %2$s et %3$s"

#. translators: %s expands to a unit of time (e.g. 1 day).
#. translators: %1$s and %2$s expand to units of time (e.g. 1 day).
#: src/integrations/blocks/structured-data-blocks.php:172
#: js/dist/how-to-block.js:9 js/dist/how-to-block.js:15
msgid "%1$s and %2$s"
msgstr "%1$s et %2$s"

#. translators: 1: Opening tag of the link to the Search appearance settings
#. page, 2: Link closing tag.
#: src/content-type-visibility/application/content-type-visibility-watcher-actions.php:157
msgid "You've added a new type of content. We recommend that you review the corresponding %1$sSearch appearance settings%2$s."
msgstr "Vous avez ajouté un nouveau type de contenu. Nous vous recommandons de passer en revue les types de publication correspondants dans les %1$sréglages SEO%2$s."

#: src/content-type-visibility/application/content-type-visibility-dismiss-notifications.php:73
msgid "Error: Taxonomy was not removed from new_taxonomies list."
msgstr "Erreur : La taxonomie n’a pas été retirée de la liste new_taxonomies."

#: src/content-type-visibility/application/content-type-visibility-dismiss-notifications.php:73
msgid "Taxonomy is no longer new."
msgstr "La taxonomie n’est plus nouvelle."

#: src/content-type-visibility/application/content-type-visibility-dismiss-notifications.php:66
msgid "Taxonomy is not new."
msgstr "La taxonomie n’est pas nouvelle."

#: src/content-type-visibility/application/content-type-visibility-dismiss-notifications.php:43
msgid "Error: Post type was not removed from new_post_types list."
msgstr "Erreur : Le type de publication n’a pas été retiré de la liste new_post_types."

#: src/content-type-visibility/application/content-type-visibility-dismiss-notifications.php:43
msgid "Post type is no longer new."
msgstr "Le type de publication n’est plus nouveau."

#: src/content-type-visibility/application/content-type-visibility-dismiss-notifications.php:37
msgid "Post type is not new."
msgstr "Le type de publication n’est pas nouveau."

#: src/integrations/support-integration.php:108 js/dist/support.js:24
msgid "Support"
msgstr "Support"

#: src/integrations/admin/crawl-settings-integration.php:163
#: js/dist/new-settings.js:38 js/dist/new-settings.js:181
msgid "Prevent Google AdsBot from crawling"
msgstr "Empêcher l’exploration de Google AdsBot"

#: src/integrations/admin/background-indexing-integration.php:186
msgid "Every fifteen minutes"
msgstr "Toutes les quinze minutes"

#: src/commands/index-command.php:174
msgid "Your WordPress environment is running on a non-production site. Indexables can only be created on production environments. Please check your `WP_ENVIRONMENT_TYPE` settings."
msgstr "Votre environnement WordPress tourne sur un site qui n’est pas en production. Les indexables ne peuvent être créés que sur des environnements de production. Veuillez vérifiez vos réglages `WP_ENVIRONMENT_TYPE`."

#. translators: %s expands to the inclusive language score
#: inc/class-wpseo-rank.php:239 inc/class-wpseo-rank.php:244
#: inc/class-wpseo-rank.php:249
msgid "Inclusive language: %s"
msgstr "Langage inclusif : %s"

#. translators: %1$s expands to Yoast SEO, %2$s to Wincher
#: admin/class-wincher-dashboard-widget.php:58
msgid "%1$s / %2$s: Top Keyphrases"
msgstr "%1$s / %2$s : Expressions clés principales"

#. translators: %s: expands to the post type
#: src/exceptions/indexable/post-type-not-built-exception.php:20
msgid "The post type %s could not be indexed because it does not meet indexing requirements."
msgstr "Le type de publication %s ne peut pas être indexé car il n’atteint pas les prérequis d’indexation."

#: src/integrations/academy-integration.php:111 js/dist/academy.js:2
msgid "Academy"
msgstr "Académie"

#. translators: %1$s expands to a strong tag, %2$s expands to the product name,
#. %3$s expands to a closing strong tag, %4$s expands to an a tag. %5$s expands
#. to MyYoast, %6$s expands to a closing a tag,  %7$s expands to the product
#. name
#: inc/class-addon-manager.php:522
msgid "%1$s %2$s isn't working as expected %3$s and you are not receiving updates or support! Make sure to %4$s activate your product subscription in %5$s%6$s to unlock all the features of %7$s."
msgstr "%1$s %2$s ne fonctionne pas de façon optimale %3$s et vous ne recevez plus les mises à jour ou de support ! Assurez-vous %4$s d’activer votre abonnement dans %5$s%6$s pour débloquer toutes les fonctionnalités de %7$s."

#. translators: %1$s is the plugin name, %2$s and %3$s are a link.
#: inc/class-addon-manager.php:421
msgid "%1$s can't be updated because your product subscription is expired. %2$sRenew your product subscription%3$s to get updates again and use all the features of %1$s."
msgstr "%1$s ne peut pas être mis à jour parce que votre abonnement a expiré. %2$sRenouvelez votre licence%3$s pour obtenir les mises à jour et utiliser toutes les fonctionnalités de %1$s."

#: src/integrations/admin/crawl-settings-integration.php:292
msgid "This feature is disabled when your site is not using pretty permalinks."
msgstr "Cette fonctionnalité est désactivée lorsque votre site n’utilise pas de beaux permaliens."

#. translators: 1: Link start tag to the Permalinks settings page, 2: Link
#. closing tag.
#: src/integrations/admin/crawl-settings-integration.php:286
msgid "This feature is disabled when your site is not using %1$spretty permalinks%2$s."
msgstr "Cette fonctionnalité est désactivée lorsque votre site n’utilise pas de %1$sbeaux permaliens%2$s."

#. translators: %1$s: Yoast SEO
#: src/helpers/crawl-cleanup-helper.php:271
msgid "%1$s: unregistered URL parameter removed. See %2$s"
msgstr "%1$s : paramètre d’URL non-enregistré retiré. Consultez %2$s"

#. translators: %1$s: Yoast SEO
#: src/initializers/crawl-cleanup-permalinks.php:144
msgid "%1$s: redirect utm variables to #"
msgstr "%1$s : rediriger vers les variables utm vers #"

#: src/presenters/admin/indexing-notification-presenter.php:93
msgid "It looks like you've enabled media pages. We recommend that you help us to re-analyze your site by running the SEO data optimization."
msgstr "Il semblerait que vous ayez activé les pages de média. Nous vous recommandons de nous aider à réanalyser votre site en lançant l’optimisation des données SEO."

#: inc/class-wpseo-rank.php:156 inc/class-wpseo-rank.php:245
#: js/dist/block-editor.js:540 js/dist/editor-modules.js:315
#: js/dist/elementor.js:94 js/dist/externals-components.js:188
#: js/dist/externals-components.js:282
#: js/dist/frontend-inspector-resources.js:1 js/dist/post-edit.js:25
#: js/dist/term-edit.js:1
msgid "Potentially non-inclusive"
msgstr "Potentiellement non-inclusif"

#. translators: CTA to finish the first time configuration. %s: Either
#. first-time SEO configuration or SEO configuration.
#: admin/class-admin.php:235
msgid "Finish your %s"
msgstr "Finissez votre %s"

#. translators: %1$s and %2$s expand to a span wrap to avoid linebreaks. %3$s
#. expands to "Yoast SEO Premium".
#: src/presenters/admin/sidebar-presenter.php:63 js/dist/block-editor.js:26
#: js/dist/classic-editor.js:11 js/dist/elementor.js:11
#: js/dist/externals-components.js:198 js/dist/general-page.js:13
#: js/dist/integrations-page.js:47 js/dist/new-settings.js:13
#: js/dist/post-edit.js:11 js/dist/support.js:13 js/dist/term-edit.js:11
msgid "%1$sGet%2$s %3$s"
msgstr "%1$sPassez à%2$s %3$s"

#: inc/class-wpseo-admin-bar-menu.php:572
msgid "WordPress.org support forums"
msgstr "Forums de support WordPress.org"

#: inc/class-wpseo-admin-bar-menu.php:567
msgid "Yoast Premium support"
msgstr "Support de Yoast Premium"

#: inc/class-wpseo-admin-bar-menu.php:562
msgid "Yoast.com help section"
msgstr "Section aide de Yoast.com"

#: inc/class-wpseo-admin-bar-menu.php:547
msgid "Help"
msgstr "Aide"

#: inc/class-wpseo-admin-bar-menu.php:528
msgid "Write better content"
msgstr "Écrivez un meilleur contenu"

#: inc/class-wpseo-admin-bar-menu.php:523
msgid "Improve your blog post"
msgstr "Améliorez votre article"

#: inc/class-wpseo-admin-bar-menu.php:518
#: inc/class-wpseo-admin-bar-menu.php:577
msgid "Learn more SEO"
msgstr "Approfondissez le SEO"

#: inc/class-wpseo-admin-bar-menu.php:473
msgid "SEO Tools"
msgstr "Outils SEO"

#: inc/class-wpseo-admin-bar-menu.php:236
msgid "Focus keyphrase: "
msgstr "Expression clé principale : "

#: inc/class-wpseo-admin-bar-menu.php:230
msgid "not set"
msgstr "non défini"

#: src/presenters/admin/indexing-notification-presenter.php:90
msgid "We need to re-analyze some of your SEO data because of a change in the visibility of your taxonomies. Please help us do that by running the SEO data optimization."
msgstr "Nous devons analyser une partie de vos données SEO à nouveau suite à changement de visibilité sur vos taxonomies. Aidez-nous à le faire en lançant une optimisation des données SEO."

#: src/presenters/admin/indexing-notification-presenter.php:87
msgid "We need to re-analyze some of your SEO data because of a change in the visibility of your post types. Please help us do that by running the SEO data optimization."
msgstr "Nous devons analyser une partie de vos données SEO à nouveau suite à changement de visibilité sur vos types de publications. Aidez-nous à le faire en lançant une optimisation des données SEO."

#. translators: %s: expands to the term id
#: src/exceptions/indexable/term-not-built-exception.php:20
msgid "The term %s could not be built because it's not indexable."
msgstr "Le terme %s ne peut pas être généré car il n’est pas indexable."

#. translators: %s: expands to the post id
#: src/exceptions/indexable/post-not-built-exception.php:32
msgid "The post %s could not be indexed because it's post type is excluded from indexing."
msgstr "La publication %s ne peut pas être indexée car son type de publication est exclu de l’indexation."

#. translators: %s: expands to the post id
#: src/exceptions/indexable/post-not-built-exception.php:20
msgid "The post %s could not be indexed because it does not meet indexing requirements."
msgstr "La publication %s ne peut pas être indexée car elle n’atteint pas les prérequis d’indexation."

#. translators: %1$d is the number of records that were removed. %2$s is the
#. site url.
#: src/commands/cleanup-command.php:183
msgid "Cleaned up %1$d record from %2$s."
msgid_plural "Cleaned up %1$d records from %2$s."
msgstr[0] "%1$d enregistrement a été nettoyé de %2$s."
msgstr[1] "%1$d enregistrements ont été nettoyés de %2$s."

#. translators: %1$s is the site url of the site that is cleaned up. %2$s is
#. the name of the cleanup task that is currently running.
#: src/commands/cleanup-command.php:159
msgid "Cleaning up %1$s [%2$s]"
msgstr "Nettoyage de %1$s [%2$s]"

#. translators: %1$s is the site url of the site that is skipped. %2$s is Yoast
#. SEO.
#: src/commands/cleanup-command.php:146
msgid "Skipping %1$s. %2$s is not active on this site."
msgstr "%1$s ignoré. %2$s n’est pas actif sur ce site."

#. translators: %1$d is the number of records that are removed.
#: src/commands/cleanup-command.php:97
msgid "Cleaned up %1$d record."
msgid_plural "Cleaned up %1$d records."
msgstr[0] "%1$d enregistrement a été nettoyé."
msgstr[1] "%1$d enregistrements ont été nettoyés."

#: src/commands/cleanup-command.php:84
msgid "The value for 'batch-size' must be a positive integer higher than equal to 1."
msgstr "La valeur pour « batch-size » doit être un entier positif égal ou supérieur à 1."

#: src/commands/cleanup-command.php:81
msgid "The value for 'interval' must be a positive integer."
msgstr "La valeur « interval » doit être un entier positif."

#: admin/views/class-yoast-feature-toggles.php:210 js/dist/general-page.js:23
#: js/dist/general-page.js:35 js/dist/integrations-page.js:3
#: js/dist/integrations-page.js:6 js/dist/integrations-page.js:18
#: js/dist/integrations-page.js:57 js/dist/new-settings.js:312
#: js/dist/plans.js:2 js/dist/post-edit.js:28
msgid "Learn more"
msgstr "En savoir plus"

#: src/integrations/admin/crawl-settings-integration.php:157
msgid "Redirect pretty URLs for search pages to raw format"
msgstr "Rediriger les belles URL des pages de recherche vers leur format brut"

#. Translators: %1$s expands to an opening anchor tag for a link leading to the
#. Yoast SEO page of the Permalink Cleanup features, %2$s expands to a closing
#. anchor tag.
#: src/integrations/admin/crawl-settings-integration.php:204
msgid "These are expert features, so make sure you know what you're doing before removing the parameters. %1$sRead more about how your site can be affected%2$s."
msgstr "Ce sont des fonctionnalités pour les experts donc assurez-vous de savoir ce que vous faites avant de retirer ces paramètres. %1$sEn savoir plus sur l’impact potentiel sur votre site%2$s."

#: src/presenters/admin/sidebar-presenter.php:101 js/dist/block-editor.js:27
#: js/dist/classic-editor.js:12 js/dist/elementor.js:12
#: js/dist/externals-components.js:199 js/dist/general-page.js:14
#: js/dist/integrations-page.js:48 js/dist/new-settings.js:14
#: js/dist/post-edit.js:12 js/dist/support.js:14 js/dist/term-edit.js:12
msgid "Read reviews from real users"
msgstr "Lisez des avis de vrais utilisateurs"

#. translators: %1$s expands to Yoast SEO, %2$s expands to the name of the
#. class that could not be found.
#: src/loader.php:258
msgid "%1$s attempted to load the class %2$s but it could not be found."
msgstr "%1$s a essayé de charger la classe %2$s mais elle est introuvable."

#: src/integrations/admin/crawl-settings-integration.php:189
#: js/dist/new-settings.js:181
msgid "Remove unused resources"
msgstr "Retirer les ressources inutilisées"

#: src/integrations/admin/crawl-settings-integration.php:162
msgid "Prevent search engines from crawling /wp-json/"
msgstr "Empêcher les moteurs de recherche d’explorer /wp-json/"

#: src/integrations/admin/crawl-settings-integration.php:156
msgid "Prevent search engines from crawling site search URLs"
msgstr "Empêcher les moteurs de recherche d’explorer les URL de recherche interne"

#: admin/views/user-profile.php:74
#: src/user-meta/framework/custom-meta/inclusive-language-analysis-disable.php:115
msgid "Removes the inclusive language analysis section from the metabox and disables all inclusive language-related suggestions."
msgstr "Retire la section de l’analyse d’écriture inclusive et désactive toutes les suggestions liées."

#: admin/views/user-profile.php:71
#: src/user-meta/framework/custom-meta/inclusive-language-analysis-disable.php:109
msgid "Disable inclusive language analysis"
msgstr "Désactive l’analyse d’écriture inclusive"

#: admin/views/class-yoast-feature-toggles.php:95
msgid "Discover why inclusive language is important for SEO."
msgstr "Découvrez en quoi l’écriture inclusive est importante pour le SEO."

#: admin/views/class-yoast-feature-toggles.php:94
msgid "The inclusive language analysis offers suggestions to write more inclusive copy."
msgstr "L’analyse d’écriture inclusive vous donne des suggestions pour écrire de façon plus inclusive."

#: admin/views/class-yoast-feature-toggles.php:91 js/dist/new-settings.js:38
#: js/dist/new-settings.js:312
msgid "Inclusive language analysis"
msgstr "Analyse d’écriture inclusive"

#: admin/metabox/class-metabox-section-inclusive-language.php:30
#: js/dist/externals-components.js:284
msgid "Inclusive language"
msgstr "Écriture inclusive"

#: inc/class-wpseo-admin-bar-menu.php:269
msgid "Front-end SEO inspector"
msgstr "Inspecteur SEO d’interface publique"

#: admin/class-yoast-form.php:933
msgid "Unlock with Premium!"
msgstr "Débloquer avec la version Premium !"

#. translators: 1: Yoast SEO Premium
#: src/integrations/admin/deactivated-premium-integration.php:99
msgid "Activate %1$s!"
msgstr "Activez %1$s !"

#. translators: 1: Yoast SEO Premium 2: Link start tag to activate premium, 3:
#. Link closing tag.
#: src/integrations/admin/deactivated-premium-integration.php:86
msgid "You've installed %1$s but it's not activated yet. %2$sActivate %1$s now!%3$s"
msgstr "Vous avez installé %1$s mais ce n’est pas encore activé.  %2$sActivez %1$s !%3$s"

#: src/integrations/admin/crawl-settings-integration.php:212
msgid "Permalink cleanup settings"
msgstr "Réglages de nettoyage des permaliens"

#: src/integrations/admin/crawl-settings-integration.php:198
msgid "Search cleanup settings"
msgstr "Réglages de nettoyage de la recherche"

#: src/integrations/admin/crawl-settings-integration.php:155
#: js/dist/new-settings.js:38 js/dist/new-settings.js:182
msgid "Filter searches with common spam patterns"
msgstr "Filtrer les recherches avec des motifs courants de spam"

#: src/integrations/admin/crawl-settings-integration.php:154
#: js/dist/new-settings.js:38 js/dist/new-settings.js:182
msgid "Filter searches with emojis and other special characters"
msgstr "Filtrer les recherches avec des emojis et autres caractères spéciaux"

#: src/integrations/admin/crawl-settings-integration.php:153
#: js/dist/new-settings.js:38 js/dist/new-settings.js:182
msgid "Filter search terms"
msgstr "Filtrer les termes de recherche"

#: src/integrations/admin/crawl-settings-integration.php:149
msgid "Unregistered URL parameters"
msgstr "Paramètres URL non enregistrés"

#: src/integrations/admin/crawl-settings-integration.php:148
msgid "Campaign tracking URL parameters"
msgstr "Paramètres d’URL de suivi de campagne"

#: src/deprecated/src/config/wordproof-translations.php:129
msgid "Contact WordProof support"
msgstr "Contacter le support de WordProof"

#: admin/views/class-yoast-feature-toggles.php:199
msgid "Find out how IndexNow can help your site."
msgstr "Découvrez comment IndexNow peut aider votre site."

#: admin/views/class-yoast-feature-toggles.php:198 js/dist/new-settings.js:316
msgid "Automatically ping search engines like Bing and Yandex whenever you publish, update or delete a post."
msgstr "Notifie automatiquement les moteurs de recherche tels que Bing et Yandex à chaque fois que vous publiez, mettez à jour ou supprimez une publication."

#: admin/views/class-yoast-feature-toggles.php:195 js/dist/new-settings.js:38
#: js/dist/new-settings.js:316
msgid "IndexNow"
msgstr "IndexNow"

#. translators: 1: Link start tag to the first-time configuration, 2: Link
#. closing tag.
#: src/integrations/admin/first-time-configuration-notice-integration.php:119
msgid "We noticed that you haven't fully configured Yoast SEO yet. Optimize your SEO settings even further by using our improved %1$s First-time configuration%2$s."
msgstr "Nous avons remarqué que nous avons pas entièrement configuré Yoast SEO. Optimisez encore plus vos réglages SEO avec notre nouvelle %1$sconfiguration initiale%2$s."

#: src/helpers/first-time-configuration-notice-helper.php:64
msgid "SEO configuration"
msgstr "Configuration SEO"

#: src/integrations/admin/crawl-settings-integration.php:188
msgid "Feed crawl settings"
msgstr "Réglages d’exploration des flux"

#: src/integrations/admin/crawl-settings-integration.php:186
msgid "Basic crawl settings"
msgstr "Réglages basiques d’exploration"

#: src/integrations/admin/crawl-settings-integration.php:144
msgid "Powered by HTTP header"
msgstr "En-tête HTTP « propulsé par »"

#: src/integrations/admin/crawl-settings-integration.php:143
#: js/dist/new-settings.js:38 js/dist/new-settings.js:181
msgid "Pingback HTTP header"
msgstr "En-tête HTTP des pings"

#: src/integrations/admin/crawl-settings-integration.php:161
msgid "Emoji scripts"
msgstr "Script des émojis"

#: src/integrations/admin/crawl-settings-integration.php:142
msgid "Generator tag"
msgstr "Balise du générateur"

#: src/integrations/admin/crawl-settings-integration.php:141
msgid "oEmbed links"
msgstr "Liens oEmbed"

#: src/integrations/admin/crawl-settings-integration.php:140
msgid "RSD / WLW links"
msgstr "Liens RSD/WLW"

#: src/integrations/admin/crawl-settings-integration.php:139
msgid "REST API links"
msgstr "Liens d’API REST"

#: src/integrations/admin/crawl-settings-integration.php:138
msgid "Shortlinks"
msgstr "Liens courts"

#: src/integrations/admin/crawl-settings-integration.php:134
msgid "Atom/RDF feeds"
msgstr "Flux Atom/RDF"

#: src/integrations/admin/crawl-settings-integration.php:133
msgid "Search results feeds"
msgstr "Flux des résultats de recherche"

#: src/integrations/admin/crawl-settings-integration.php:132
msgid "Custom taxonomy feeds"
msgstr "Flux des taxonomies personnalisées"

#: src/integrations/admin/crawl-settings-integration.php:131
msgid "Tag feeds"
msgstr "Flux des étiquettes"

#: src/integrations/admin/crawl-settings-integration.php:130
msgid "Category feeds"
msgstr "Flux des catégories"

#: src/integrations/admin/crawl-settings-integration.php:129
msgid "Post type feeds"
msgstr "Flux des types de publication"

#: src/integrations/admin/crawl-settings-integration.php:128
msgid "Post authors feeds"
msgstr "Flux des auteurs/autrices"

#: src/integrations/admin/crawl-settings-integration.php:126
msgid "Global comment feeds"
msgstr "Flux global des commentaires"

#: src/integrations/admin/crawl-settings-integration.php:125
msgid "Global feed"
msgstr "Flux global"

#: src/integrations/admin/crawl-settings-integration.php:127
msgid "Post comments feeds"
msgstr "Flux des commentaires"

#. translators: %1$s opens the link to the Yoast.com article about Crawl
#. settings, %2$s closes the link,
#: admin/views/tabs/network/crawl-settings.php:31
msgid "%1$sLearn more about crawl settings.%2$s"
msgstr "%1$sEn savoir plus sur les réglages d’exploration.%2$s"

#: admin/views/redirects.php:188
msgid "No items found."
msgstr "Aucun élément trouvé."

#: admin/views/redirects.php:137
msgid "All redirect types"
msgstr "Tous les types de redirection"

#: admin/views/redirects.php:122
msgid "Add Redirect"
msgstr "Ajouter une redirection"

#: admin/views/redirects.php:100 admin/views/redirects.php:165
#: admin/views/redirects.php:212
msgid "Old URL"
msgstr "Ancienne URL"

#. translators: 1: opens a link. 2: closes the link.
#: admin/views/redirects.php:91
msgid "The redirect type is the HTTP response code sent to the browser telling the browser what type of redirect is served. %1$sLearn more about redirect types%2$s."
msgstr "Le type de redirection est le code de réponse HTTP envoyé au navigateur. %1$sEn savoir plus sur les types de redirection%2$s."

#: admin/views/redirects.php:66 admin/views/redirects.php:75
msgid "301 Moved Permanently"
msgstr "301 Déplacé définitivement"

#: admin/views/redirects.php:59 admin/views/redirects.php:155
#: admin/views/redirects.php:203
msgid "Type"
msgstr "Type"

#: admin/views/redirects.php:50
msgid "Plain redirects"
msgstr "Redirections classiques"

#: admin/views/redirects.php:37
msgid "Regex Redirects"
msgstr "Redirections regex"

#: admin/pages/network.php:25 admin/views/tabs/network/crawl-settings.php:19
msgid "Crawl settings"
msgstr "Exploration"

#: src/integrations/admin/first-time-configuration-integration.php:128
#: js/dist/general-page.js:55
msgid "First-time configuration"
msgstr "Configuration initiale"

#. translators: 1: Link start tag to the First time configuration tab in the
#. General page, 2: Link closing tag.
#: admin/views/tabs/tool/import-seo.php:106
msgid "You should finish the %1$sfirst time configuration%2$s to make sure your SEO data has been optimized and you’ve set the essential Yoast SEO settings for your site."
msgstr "Vous devriez terminer la %1$sconfiguration initiale%2$s pour que les données SEO soient optimisées et que les réglages essentiels de Yoast SEO soient définis."

#: admin/views/tabs/tool/import-seo.php:100
msgid "Step 4: Go through the first time configuration"
msgstr "Étape 4 : Allez au bout de la configuration initiale"

#: src/integrations/admin/import-integration.php:220
msgid "If you already have saved AIOSEO 'Search Appearance' settings and the issue persists, please contact our support team so we can take a closer look."
msgstr "Si vous avez déjà enregistré des réglages de AIOSEO dans « Apparence de la recherche » et que le problème persiste, veuillez contacter notre équipe de support afin que nous regardions de plus près."

#: src/integrations/admin/import-integration.php:217
msgid "If you have never saved any AIOSEO 'Search Appearance' settings, please do that first and run the import again."
msgstr "Si vous n’avez jamais enregistré de réglages dans « Apparence de la recherche », veuillez le faire et relancer ensuite l’importation."

#: src/integrations/admin/import-integration.php:214
msgid "The AIOSEO import was cancelled because some AIOSEO data is missing. Please try and take the following steps to fix this:"
msgstr "L’importation AIOSEO a été annulée car certaines données sont manquantes. Veuillez réessayer et suivre les étapes suivantes pour corriger le problème :"

#: src/exceptions/importing/aioseo-validation-exception.php:17
msgid "The validation of the AIOSEO data structure has failed."
msgstr "La validation de la structure de données de AIOSEO a échoué."

#: src/deprecated/src/integrations/third-party/wordproof-integration-toggle.php:122
msgid "The WordProof Timestamp plugin needs to be disabled before you can activate this integration."
msgstr "L’extension « WordProof Timestamp » doit d’abord être désactivée avant d’activer cette intégration."

#. translators: %s expands to WordProof
#: src/deprecated/src/integrations/third-party/wordproof-integration-toggle.php:116
msgid "Currently, the %s integration is not available for multisites."
msgstr "Actuellement, l’intégration à %s n’est pas disponible pour les multisites."

#: src/deprecated/src/config/wordproof-translations.php:115
msgid "Open settings"
msgstr "Ouvrir les réglages"

#: src/deprecated/src/config/wordproof-translations.php:101
msgid "Open authentication"
msgstr "Ouvrir l’authentification"

#. translators: %s expands to WordProof.
#: src/deprecated/src/config/wordproof-translations.php:87
msgid "The timestamp is not created because you need to authenticate with %s first."
msgstr "L’horodatage n’a pas été créé parce que vous devez d’abord vous authentifier à %s."

#. translators: %s expands to WordProof.
#: src/deprecated/src/config/wordproof-translations.php:72
msgid "The timestamp is not retrieved by your site. Please try again or contact %1$s support."
msgstr "L’horodatage n’est pas récupéré par votre site. Veuillez réessayer ou contactez le support de %1$s."

#. translators: %s expands to WordProof.
#: src/deprecated/src/config/wordproof-translations.php:57
msgid "%1$s failed to timestamp this page. Please check if you're correctly authenticated with %1$s and try to save this page again."
msgstr "%1$s n’a pas réussi à horodater cette page. Veuillez vérifier votre connexion à %1$s puis mettez cette page à jour."

#. translators: %s expands to WordProof.
#: src/deprecated/src/config/wordproof-translations.php:42
msgid "%s has successfully timestamped this page."
msgstr "%s a bien horodaté cette page."

#. translators: %s expands to WordProof.
#: src/deprecated/src/config/wordproof-translations.php:27
msgid "You are out of timestamps. Please upgrade your account by opening the %s settings."
msgstr "Vous n’avez plus d’horodatages. Veuillez mettre votre compte à niveau en ouvrant les réglages de %s."

#: src/integrations/admin/import-integration.php:236
msgid "Cleanup failed with the following error:"
msgstr "Le nettoyage a échoué avec l’erreur suivante :"

#: src/integrations/admin/import-integration.php:120
msgid "Note: These settings will overwrite the default settings of Yoast SEO."
msgstr "Remarque : Ces réglages écraseront ceux par défaut de Yoast SEO."

#: src/integrations/admin/import-integration.php:116
#: src/integrations/admin/import-integration.php:126
msgid "Note: This metadata will only be imported if there is no existing Yoast SEO metadata yet."
msgstr "Remarque : Cette métadonnée ne sera importée que s’il n‘existe pas encore de métadonnée correspondante dans Yoast SEO."

#: src/integrations/admin/import-integration.php:115
#: src/integrations/admin/import-integration.php:125
msgid "Post metadata (SEO titles, descriptions, etc.)"
msgstr "Les métadonnées des publications (titres SEO, descriptions, etc…)"

#. translators: %s: expands to the name of the plugin that is selected to be
#. imported
#: src/integrations/admin/import-integration.php:111
msgid "The import from %s includes:"
msgstr "L’importation de %s inclut :"

#: src/integrations/admin/import-integration.php:109
msgid "Once you're certain that your site is working properly with the imported data from another SEO plugin, you can clean up all the original data from that plugin."
msgstr "Lorsque vous aurez la certitude que votre site fonctionne correctement avec le données importées d’une autre extension SEO, vous pouvez nettoyer ses anciennes données."

#: src/integrations/admin/import-integration.php:108
msgid "Please select an SEO plugin below to see what data can be imported."
msgstr "Veuillez sélectionner une extension SEO ci-dessous pour voir les données éligibles à l’importation."

#: admin/views/tool-import-export.php:35
#: src/integrations/admin/import-integration.php:107
msgid "Clean up"
msgstr "Nettoyer"

#: src/integrations/admin/import-integration.php:99
msgid "After you've imported data from another SEO plugin, please make sure to clean up all the original data from that plugin. (step 5)"
msgstr "Après l’importation de données d’une autre extension SEO, assurez-vous de nettoyer ses données d’origine. (étape 5)"

#: src/integrations/admin/import-integration.php:98
msgid "Note: "
msgstr "Remarque : "

#: src/integrations/admin/import-integration.php:97
msgid "The cleanup can take a long time depending on your site's size."
msgstr "Le nettoyage peut prendre un certain temps en fonction de la taille de votre site."

#: src/integrations/admin/import-integration.php:238
msgid "Import failed with the following error:"
msgstr "L’importation a échoué avec l’erreur suivante :"

#: src/integrations/admin/import-integration.php:101
msgid "No data found from other SEO plugins."
msgstr "Aucune donnée trouvée de la part d’autres extensions SEO."

#: src/integrations/admin/import-integration.php:100
msgid "Select SEO plugin"
msgstr "Sélectionnez une extension SEO"

#: src/integrations/admin/import-integration.php:96
msgid "The import can take a long time depending on your site's size."
msgstr "L’importation peut prendre un certain temps en fonction de la taille de votre site."

#: src/integrations/admin/installation-success-integration.php:104
msgid "Installation Successful"
msgstr "Installation réussie"

#: src/config/schema-types.php:131
msgid "Blog Post"
msgstr "Article de blog"

#. translators: %s: expands to 'Yoast SEO Premium'.
#. translators: 1: Yoast WooCommerce SEO
#: src/integrations/admin/workouts-integration.php:315
#: js/dist/integrations-page.js:11
msgid "Activate %s"
msgstr "Activer %s"

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:309
msgid "Update %s"
msgstr "Mettre à jour %s"

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:302
msgid "Renew %s"
msgstr "Renouveler %s"

#: src/integrations/admin/workouts-integration.php:243
msgid "Get help activating your subscription"
msgstr "Obtenez de l’aide dans l’activation de votre abonnement"

#. translators: 1: expands to 'Yoast SEO Premium', 2: Link start tag to the
#. page to update Premium, 3: Link closing tag.
#: src/integrations/admin/workouts-integration.php:237
msgid "It looks like you’re running an outdated and unactivated version of %1$s, please activate your subscription in %2$sMyYoast%3$s and update to the latest version (at least 17.7) to gain access to our updated workouts section."
msgstr "Il semblerait que vous utilisiez une version obsolète et désactivée de %1$s. Veuillez renouveler votre abonnement dans%2$sMyYoast%3$s puis mettez l’extension à jour (au moins en version 17.7) pour débloquer l’accès à notre section Entraînements mise à jour."

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:233
msgid "Activate your subscription of %s"
msgstr "Activez votre abonnement de %s"

#. translators: 1: expands to 'Yoast SEO Premium', 2: Link start tag to the
#. page to update Premium, 3: Link closing tag.
#: src/integrations/admin/workouts-integration.php:224
msgid "It looks like you're running an outdated version of %1$s, please %2$supdate to the latest version (at least 17.7)%3$s to gain access to our updated workouts section."
msgstr "Il semblerait que vous utilisiez une version obsolète de %1$s. Veuillez %2$spasser à la dernière version%3$s pour débloquer la section des entraînements mise à jour."

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:221
msgid "Update to the latest version of %s"
msgstr "Passez à la dernière version de %s"

#: src/integrations/admin/workouts-integration.php:213
msgid "Renew your subscription"
msgstr "Renouvelez votre abonnement"

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:206
msgid "Accessing the latest workouts requires an updated version of %s (at least 17.7), but it looks like your subscription has expired. Please renew your subscription to update and gain access to all the latest features."
msgstr "L’accès aux derniers entraînements nécessite une version mise à jour de %s (au moins 17.7), mais il semblerait que votre abonnement ait expiré. Veuillez le renouveler pour mettre l’extension à jour et débloquer l’accès aux dernières fonctionnalités."

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:203
msgid "Renew your subscription of %s"
msgstr "Renouvelez votre abonnement à %s"

#. translators: 1: Link start tag to the first-time configuration, 2: Yoast
#. SEO, 3: Link closing tag.
#: src/integrations/admin/first-time-configuration-notice-integration.php:110
msgid "Get started quickly with the %1$s%2$s First-time configuration%3$s and configure Yoast SEO with the optimal SEO settings for your site!"
msgstr "Démarrez rapidement avec %1$sla première configuration de %2$s%3$s afin de configurer les réglages optimaux pour votre site !"

#: inc/class-wpseo-replace-vars.php:1499
msgid "Current or first category title"
msgstr "Titre de la catégorie actuelle ou de la première catégorie"

#: inc/class-wpseo-replace-vars.php:1499
msgid "Category Title"
msgstr "Titre de catégorie"

#: inc/class-wpseo-replace-vars.php:1498
msgid "Replaced with the post content"
msgstr "Remplacé par le contenu de la publication"

#: inc/class-wpseo-replace-vars.php:1498
msgid "Post Content"
msgstr "Contenu de la publication"

#: inc/class-wpseo-replace-vars.php:1497
msgid "Replaced with the permalink"
msgstr "Remplacé par le permalien"

#: inc/class-wpseo-replace-vars.php:1496
msgid "Replaced with the last name of the author"
msgstr "Remplacé par le nom de famille de l’auteur/autrice"

#: inc/class-wpseo-replace-vars.php:1496
msgid "Author last name"
msgstr "Nom de famille de l’auteur/autrice"

#: inc/class-wpseo-replace-vars.php:1495
msgid "Replaced with the first name of the author"
msgstr "Remplacé par le prénom de l’auteur/autrice"

#: inc/class-wpseo-replace-vars.php:1495
msgid "Author first name"
msgstr "Prénom de l‘auteur/autrice"

#: inc/class-wpseo-replace-vars.php:1494
msgid "Replaced with the day the post was published"
msgstr "Remplacé par le jour de publication"

#: inc/class-wpseo-replace-vars.php:1494
msgid "Post day"
msgstr "Jour de publication"

#: inc/class-wpseo-replace-vars.php:1493
msgid "Replaced with the month the post was published"
msgstr "Remplacé par le mois de publication"

#: inc/class-wpseo-replace-vars.php:1493
msgid "Post month"
msgstr "Mois de publication"

#: inc/class-wpseo-replace-vars.php:1492
msgid "Replaced with the year the post was published"
msgstr "Remplacé par l’année de publication"

#: inc/class-wpseo-replace-vars.php:1492
msgid "Post year"
msgstr "Année de publication"

#: inc/class-wpseo-replace-vars.php:1491
msgid "Current day"
msgstr "Jour actuel"

#: inc/class-wpseo-replace-vars.php:1490
msgid "Current month"
msgstr "Mois actuel"

#: inc/class-wpseo-replace-vars.php:1488
msgid "Current date"
msgstr "Date actuelle"

#. translators: %1$s expands to an opening strong tag, %2$s expands to the
#. dependency name, %3$s expands to a closing strong tag, %4$s expands to an
#. opening anchor tag, %5$s expands to a closing anchor tag.
#: admin/class-suggested-plugins.php:111
msgid "It looks like you aren't using our %1$s%2$s addon%3$s. %4$sUpgrade today%5$s to unlock more tools and SEO features to make your products stand out in search results."
msgstr "Il semblerait que vous n’utilisez pas notre %1$smodule %2$s%3$s. %4$sAchetez-le%5$s et débloquez plus d’outils et de fonctionnalités SEO pour mettre en avant vos produits dans les résultats de recherche."

#. translators: %1$s expands to an opening anchor tag for a link leading to the
#. Premium installation page, %2$s expands to a closing anchor tag.
#: src/presenters/admin/indexing-error-presenter.php:102
msgid "Below are the technical details for the error. See %1$sthis page%2$s for a more detailed explanation."
msgstr "Vous trouverez ci-dessous les détails techniques de cette erreur. Consultez %1$scette page%2$s pour une explication détaillée."

#: src/integrations/admin/workouts-integration.php:93
msgid "Workouts"
msgstr "Entraînements"

#: admin/views/class-yoast-integration-toggles.php:81
msgid "Improve the quality of your site search! Automatically helps your users find your cornerstone and most important content in your internal search results. It also removes noindexed posts & pages from your site’s search results."
msgstr "Améliorez la qualité de la recherche sur votre site ! Cela aide automatiquement vos visiteurs à trouver vos contenus piliers et les plus importants dans les résultats de recherche interne. Cela retire également des publications désindexées de vos résultats de recherche."

#. translators: %s: Algolia.
#: admin/views/class-yoast-integration-toggles.php:83
msgid "Find out more about our %s integration."
msgstr "En savoir plus sur l’intégration à %s."

#: admin/views/class-yoast-feature-toggles.php:129
msgid "Read more about how internal linking can improve your site structure."
msgstr "En savoir plus sur l’impact du maillage interne sur la structure de votre site."

#: admin/views/class-yoast-feature-toggles.php:128
msgid "Get relevant internal linking suggestions — while you’re writing! The link suggestions metabox shows a list of posts on your blog with similar content that might be interesting to link to. "
msgstr "Obtenez des suggestions de maillage interne pertinentes — pendant que vous écrivez ! L’encart des suggestions de liens affiche une liste de publications de votre blog avec du contenu similaire vers lequel il serait intéressant de faire des liens."

#: admin/views/class-yoast-feature-toggles.php:125 js/dist/new-settings.js:38
#: js/dist/new-settings.js:312
msgid "Link suggestions"
msgstr "Suggestions de liens"

#: admin/views/class-yoast-feature-toggles.php:119
msgid "Find out how Insights can help you improve your content."
msgstr "Découvrez comment les analyses peuvent vous aider à améliorer votre contenu."

#: admin/views/class-yoast-feature-toggles.php:118
msgid "Find relevant data about your content right in the Insights section in the Yoast SEO metabox. You’ll see what words you use most often and if they’re a match with your keywords! "
msgstr "Trouvez des données pertinentes sur votre contenu directement dans la section « Analyses » de la boîte méta Yoast SEO. Vous verrez quels mots vous utilisez le plus souvent et s’ils correspondent à vos mots-clés ! "

#: admin/views/class-yoast-feature-toggles.php:116 js/dist/block-editor.js:244
#: js/dist/block-editor.js:548 js/dist/classic-editor.js:229
#: js/dist/elementor.js:120 js/dist/new-settings.js:38
#: js/dist/new-settings.js:312
msgid "Insights"
msgstr "Analyses"

#. translators: %1$s expands to an opening anchor tag for a link leading to the
#. Premium installation page, %2$s expands to a closing anchor tag.
#. Translators: %1$s expands to an opening anchor tag for a link leading to the
#. Premium installation page, %2$s expands to a closing anchor tag.
#: src/presenters/admin/indexing-error-presenter.php:76
#: src/presenters/admin/indexing-failed-notification-presenter.php:77
msgid "Oops, something has gone wrong and we couldn't complete the optimization of your SEO data. Please make sure to activate your subscription in MyYoast by completing %1$sthese steps%2$s."
msgstr "Oups, il y a eu une erreur et nous n’avons pas pu terminer l’optimisation de vos données SEO. Veuillez vous assurer de bien activer votre abonnement dans MyYoast en suivant %1$sces étapes%2$s."

#: admin/views/redirects.php:22 js/dist/integrations-page.js:4
#: js/dist/integrations-page.js:7 js/dist/integrations-page.js:19
msgid "Unlock with Premium"
msgstr "Débloquer avec la version premium"

#. translators: %1$s expands to Yoast SEO
#: src/integrations/admin/addon-installation/dialog-integration.php:94
msgid "No %1$s plugins have been installed. You don't seem to own any active subscriptions."
msgstr "Aucune extension %1$s n’a été installée. Il semblerait que vous n’ayez aucune licence active."

#. Translators: %s expands to the error message.
#: src/integrations/admin/addon-installation/installation-integration.php:192
msgid "Addon installation failed because of an error: %s."
msgstr "Échec d’installation d’un module à cause d’une erreur : %s."

#: src/integrations/admin/addon-installation/installation-integration.php:188
msgid "You are not allowed to install plugins."
msgstr "Vous n’avez pas les droits suffisants pour installer des extensions."

#. Translators: %s expands to the name of the addon.
#: src/integrations/admin/addon-installation/installation-integration.php:184
msgid "Addon installed."
msgstr "Module installé."

#. Translators:%s expands to the error message.
#: src/integrations/admin/addon-installation/installation-integration.php:160
msgid "Addon activation failed because of an error: %s."
msgstr "Échec d’activation d’un module à cause d’une erreur : %s."

#: src/integrations/admin/addon-installation/installation-integration.php:156
msgid "You are not allowed to activate plugins."
msgstr "Vous n’avez pas les droits nécessaires pour activer des extensions."

#. Translators: %s expands to the name of the addon.
#: src/integrations/admin/addon-installation/installation-integration.php:154
msgid "Addon activated."
msgstr "Module activé."

#. translators: %1$s expands to an anchor tag to the admin premium page, %2$s
#. expands to Yoast SEO Premium, %3$s expands to a closing anchor tag
#: src/integrations/admin/addon-installation/installation-integration.php:129
msgid "%1$s Continue to %2$s%3$s"
msgstr "%1$s Continuer vers %2$s%3$s"

#: src/integrations/admin/addon-installation/installation-integration.php:106
msgid "Installing and activating addons"
msgstr "Installation et activation des modules"

#. translators: %s expands to Yoast SEO Premium.
#: admin/class-admin.php:248
msgid "Required by %s"
msgstr "Nécessaire pour %s"

#. Translators: %1$s resolves to Yoast SEO.
#: src/integrations/watchers/addon-update-watcher.php:95
msgid "Auto-updates are disabled based on this setting for %1$s."
msgstr "Les mises à jour automatiques sont désactivées grâce à ce réglage de %1$s."

#. Translators: %1$s resolves to Yoast SEO.
#: src/integrations/watchers/addon-update-watcher.php:85
msgid "Auto-updates are enabled based on this setting for %1$s."
msgstr "Les mises à jour automatiques sont activées grâce à ce réglage de %1$s."

#: src/presenters/admin/badge-presenter.php:80
#: src/presenters/admin/badge-presenter.php:87
#: js/dist/externals/componentsNew.js:1081 js/dist/integrations-page.js:3
#: js/dist/integrations-page.js:6 js/dist/integrations-page.js:18
#: js/dist/new-settings.js:354
msgid "New"
msgstr "Nouveau"

#: src/exceptions/indexable/post-not-found-exception.php:16
msgid "The post could not be found."
msgstr "La publication est introuvable."

#. translators: %s is the reason given by WordPress.
#: src/exceptions/indexable/invalid-term-exception.php:21
msgid "The term is considered invalid. The following reason was given by WordPress: %s"
msgstr "Le terme est considéré comme non-valide. Voici la raison donnée par WordPress : %s"

#: src/exceptions/indexable/term-not-found-exception.php:16
msgid "The term could not be found."
msgstr "Le terme est introuvable."

#: admin/class-yoast-form.php:1068 js/dist/general-page.js:52
msgid "This feature has been disabled since subsites never send tracking data."
msgstr "Cette fonctionnalité a été désactivée puisque les sous-sites n’envoient jamais de données de suivi."

#. translators: %1$s expands to an opening anchor tag, %2$s expands to an
#. closing anchor tag.
#: src/integrations/third-party/wpml-wpseo-notification.php:110
msgid "We notice that you have installed WPML. To make sure your canonical URLs are set correctly, %1$sinstall and activate the WPML SEO add-on%2$s as well!"
msgstr "Nous remarquons que vous avez installé WPML. Pour s’assurer que vos URL canoniques sont correctement configurées, %1$sinstallez et activez le module Yoast SEO Multilingual%2$s également !"

#: src/presenters/admin/indexing-notification-presenter.php:81
msgid "Because of a change in your category base setting, some of your SEO data needs to be reprocessed."
msgstr "À cause d’un changement dans vos réglages de préfixe de catégorie, une partie de vos données SEO doit être re-traitée."

#: admin/views/class-yoast-feature-toggles.php:190
msgid "Find out how a rich snippet can improve visibility and click-through-rate."
msgstr "Découvrez comment un extrait enrichi peut améliorer votre visibilité et votre taux de clic."

#: admin/views/class-yoast-feature-toggles.php:189 js/dist/new-settings.js:312
msgid "This adds an author byline and reading time estimate to the article’s snippet when shared on Slack."
msgstr "Cela ajoute une ligne à l’extrait de la publication sur Slack avec l’auteur ou l’autrice et la durée de lecture estimée."

#: admin/views/class-yoast-feature-toggles.php:187
msgid "Enhanced Slack sharing"
msgstr "Partage amélioré sur Slack"

#. translators: 1: Expands to Yoast SEO
#: src/presenters/admin/indexing-notification-presenter.php:129
msgid "Wait for a week or so, until %1$s automatically processes most of your content in the background."
msgstr "Attendre une semaine environ, jusqu’à ce que %1$s traite automatiquement votre contenu en arrière-plan."

#. translators: %s expands to the reading time number, in minutes
#: src/presenters/slack/enhanced-data-presenter.php:55
msgid "%s minute"
msgid_plural "%s minutes"
msgstr[0] "%s minute"
msgstr[1] "%s minutes"

#. translators: %s expands to the reading time number, in minutes
#: src/presenters/slack/enhanced-data-presenter.php:55
msgid "Est. reading time"
msgstr "Durée de lecture estimée"

#: src/presenters/slack/enhanced-data-presenter.php:50
msgid "Written by"
msgstr "Écrit par"

#: inc/class-wpseo-admin-bar-menu.php:444
msgid "Google Rich Results Test"
msgstr "Test de résultats enrichis Google"

#: src/presenters/admin/indexing-notification-presenter.php:84
msgid "Because of a change in your tag base setting, some of your SEO data needs to be reprocessed."
msgstr "À cause d’un changement dans vos réglages de préfixe d’étiquettes, une partie de vos données SEO doit être re-traitée."

#. translators: %s: 'Semrush'
#: admin/views/class-yoast-integration-toggles.php:71
msgid "The %s integration offers suggestions and insights for keywords related to the entered focus keyphrase."
msgstr "L’intégration %s offre des suggestions et des analyses pour les mots-clés similaires à l’expression clé principale saisie."

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/network/integrations.php:27
msgid "This tab allows you to selectively disable %1$s integrations with third-party products for all sites in the network. By default all integrations are enabled, which allows site admins to choose for themselves if they want to toggle an integration on or off for their site. When you disable an integration here, site admins will not be able to use that integration at all."
msgstr "Cet onglet vous permet de désactiver de façon sélective des intégrations de %1$s à des solutions tierces pour l’ensemble du réseau. Par défaut, toutes les intégrations sont activées, ce qui permet aux rôles administration de choisir s’ils veulent les utiliser sur leur site. Par contre, si vous désactivez une intégration ici, aucun site du réseau ne pourra l’utiliser."

#: admin/class-admin.php:259
msgid "Activate your subscription"
msgstr "Activer votre abonnement"

#: src/presenters/admin/indexing-error-presenter.php:64
msgid "Oops, something has gone wrong and we couldn't complete the optimization of your SEO data. Please click the button again to re-start the process. "
msgstr "Oups, un problème est survenu et nous n’avons pas pu terminer l’optimisation de vos données SEO. Veuillez relancer le traitement."

#: src/integrations/watchers/indexable-homeurl-watcher.php:97
msgid "All permalinks were successfully reset"
msgstr "Tous les permaliens ont bien été réinitialisés"

#: src/presenters/admin/indexing-notification-presenter.php:96
msgid "You can speed up your site and get insight into your internal linking structure by letting us perform a few optimizations to the way SEO data is stored."
msgstr "Vous pouvez accélérer votre site et obtenir une analyse de la structure de vos liens internes en nous permettant d’effectuer quelques optimisations sur la façon dont les données SEO sont stockées."

#: src/presenters/admin/indexing-notification-presenter.php:59
#: js/dist/general-page.js:48 js/dist/indexation.js:8
msgid "Start SEO data optimization"
msgstr "Démarrer l’optimisation des données SEO"

#: src/presenters/admin/indexing-list-item-presenter.php:42
msgid "Learn more about the benefits of optimized SEO data."
msgstr "En savoir plus sur les avantages des données SEO optimisées."

#: src/presenters/admin/indexing-list-item-presenter.php:40
msgid "You can speed up your site and get insight into your internal linking structure by letting us perform a few optimizations to the way SEO data is stored. If you have a lot of content it might take a while, but trust us, it's worth it."
msgstr "Vous pouvez accélérer votre site et obtenir une analyse de la structure de vos liens internes en nous permettant d’effectuer quelques optimisations sur la façon dont les données SEO sont stockées. Si vous avez beaucoup de contenu, cela pourrait prendre un certain temps, mais croyez-nous, cela en vaut la peine."

#: src/presenters/admin/indexing-list-item-presenter.php:37
msgid "Optimize SEO Data"
msgstr "Optimiser les données SEO"

#: src/presenters/admin/indexing-error-presenter.php:71
#: src/presenters/admin/indexing-failed-notification-presenter.php:71
msgid "If the problem persists, please contact support."
msgstr "Si le problème persiste, veuillez contacter le support."

#. Translators: %1$s expands to an opening anchor tag for a link leading to the
#. Yoast SEO tools page, %2$s expands to a closing anchor tag.
#: src/presenters/admin/indexing-failed-notification-presenter.php:59
msgid "Something has gone wrong and we couldn't complete the optimization of your SEO data. Please %1$sre-start the process%2$s."
msgstr "Un problème est survenu et nous n’avons pas pu terminer l’optimisation de vos données SEO. Veuillez %1$srelancer le traitement%2$s."

#. translators: %s expands to a mailto support link.
#: inc/class-addon-manager.php:877
msgid "If you still need support and have an active subscription for this product, please email %s."
msgstr "Si vous avez besoin de support et disposez d’une licence active pour ce produit, veuillez nous écrire un e-mail à %s."

#. translators: 1: expands to <a> that refers to the help page, 2: </a> closing
#. tag.
#: inc/class-addon-manager.php:874
msgid "You can probably find an answer to your question in our %1$shelp center%2$s."
msgstr "Vous pouvez probablement trouver une réponse à votre question dans notre %1$scentre d’aide%2$s."

#: inc/class-addon-manager.php:871
msgid "Need support?"
msgstr "Besoin d’aide ?"

#. translators: %1$s: expands to an opening anchor tag, %2$s: expands to a
#. closing anchor tag
#: admin/views/class-yoast-feature-toggles.php:241
msgid "Disabling Yoast SEO's XML sitemaps will not disable WordPress' core sitemaps. In some cases, this %1$s may result in SEO errors on your site%2$s. These may be reported in Google Search Console and other tools."
msgstr "Désactiver les plans de site XML de Yoast SEO ne désactivera pas les plans de site du cœur de WordPress. Dans certains cas, cela %1$s peut provoquer des erreurs SEO sur votre site%2$s. Elles pourraient être relevées dans Google Search Console et d’autres outils."

#. translators: 1: Yoast SEO, 2: translated version of "Off"
#: admin/views/class-yoast-feature-toggles.php:158
msgid "The advanced section of the %1$s meta box allows a user to remove posts from the search results or change the canonical. The settings in the schema tab allows a user to change schema meta data for a post. These are things you might not want any author to do. That's why, by default, only editors and administrators can do this. Setting to \"%2$s\" allows all users to change these settings."
msgstr "La section avancée de l’encart de %1$s permet de désindexer des publications ou changer l’URL canonique. Les réglages dans l’onglet « schema » permettent de changer les métadonnées de schema pour une publication. Ce sont des choses que vous ne voulez peut-être pas que tous les auteurs et autrices manipulent. C’est pourquoi, par défaut, seuls les éditeurs et éditrices, administrateurs et administratrices peuvent les modifier. En mettant le réglage à « %2$s », vous autoriseriez tous les utilisateurs et utilisatrices à modifier ces réglages."

#: admin/views/class-yoast-feature-toggles.php:154
msgid "Security: no advanced or schema settings for authors"
msgstr "Sécurité : pas de réglages avancés ou schema pour les auteurs et autrices"

#: src/config/schema-types.php:159
msgid "Report"
msgstr "Rapport"

#: src/config/schema-types.php:155
msgid "Tech Article"
msgstr "Article technique"

#: src/config/schema-types.php:151
msgid "Scholarly Article"
msgstr "Article universitaire"

#: src/config/schema-types.php:147
msgid "Satirical Article"
msgstr "Article satirique"

#: src/config/schema-types.php:143
msgid "Advertiser Content Article"
msgstr "Article publi-rédactionnel"

#: src/config/schema-types.php:139
msgid "News Article"
msgstr "Article d’actualité"

#: src/config/schema-types.php:135
msgid "Social Media Posting"
msgstr "Publication de réseau social"

#: src/config/schema-types.php:127
msgid "Article"
msgstr "Article"

#: src/config/schema-types.php:104
msgid "Search Results Page"
msgstr "Page de résultats de recherche"

#: src/config/schema-types.php:100
msgid "Real Estate Listing"
msgstr "Annonces immobilières"

#: src/config/schema-types.php:96
msgid "Checkout Page"
msgstr "Page de commande"

#: src/config/schema-types.php:92
msgid "Collection Page"
msgstr "Page de navigation"

#: src/config/schema-types.php:88
msgid "Medical Web Page"
msgstr "Page médicale"

#: src/config/schema-types.php:84
msgid "Contact Page"
msgstr "Page de contact"

#: src/config/schema-types.php:80
msgid "Profile Page"
msgstr "Page de profil"

#: src/config/schema-types.php:76
msgid "QA Page"
msgstr "Page de questions-réponses"

#: src/config/schema-types.php:72
msgid "FAQ Page"
msgstr "Page de FAQ"

#: src/config/schema-types.php:68
msgid "About Page"
msgstr "Page « À propos »"

#: src/config/schema-types.php:64 js/dist/block-editor.js:527
#: js/dist/classic-editor.js:512 js/dist/elementor.js:389
msgid "Item Page"
msgstr "Page de l’élément"

#: src/config/schema-types.php:60
msgid "Web Page"
msgstr "Page"

#. translators: 1: Yoast SEO
#: admin/views/class-yoast-feature-toggles.php:170
msgid "Allow us to track some data about your site to improve our plugin."
msgstr "Autorisez-nous à suivre quelques données de votre site pour améliorer notre extension."

#: admin/views/class-yoast-feature-toggles.php:165
#: admin/views/class-yoast-feature-toggles.php:166 js/dist/new-settings.js:38
#: js/dist/new-settings.js:309
msgid "Usage tracking"
msgstr "Suivi d’utilisation"

#: src/presenters/admin/indexing-notification-presenter.php:75
msgid "Because of a change in your permalink structure, some of your SEO data needs to be reprocessed."
msgstr "À cause d’un changement dans la structure de vos permaliens, une partie de vos données SEO doit être re-traitée."

#: src/presenters/admin/indexing-notification-presenter.php:78
msgid "Because of a change in your home URL setting, some of your SEO data needs to be reprocessed."
msgstr "À cause d’un changement dans vos réglages d’URL de page d’accueil, une partie de vos données SEO doit être re-traitée."

#. translators: %1$s expands to Yoast.
#: src/integrations/blocks/block-categories.php:45
msgid "%1$s Internal Linking Blocks"
msgstr "Blocs %1$s de maillage interne"

#. translators: 1: Link to the Yoast help center, 2: Link closing tag.
#: src/services/health-check/links-table-reports.php:100
msgid "%1$sFind out how to solve this problem on our help center%2$s."
msgstr "%1$sDécouvrez comment régler ce problème dans notre centre d’aide%2$s."

#: src/services/health-check/links-table-reports.php:58
msgid "The text link counter feature is not working as expected"
msgstr "Le compteur de liens textuels ne fonctionne pas comme prévu"

#. translators: 1: Link to the Yoast SEO blog, 2: Link closing tag.
#: src/services/health-check/links-table-reports.php:73
msgid "The text link counter helps you improve your site structure. %1$sFind out how the text link counter can enhance your SEO%2$s."
msgstr "Le compteur de liens textuels vous aide à améliorer la structure de votre site. %1$sDécouvrez comment il peut améliorer votre référencement%2$s."

#: src/services/health-check/links-table-reports.php:45
msgid "The text link counter is working as expected"
msgstr "Le compteur de liens textuels fonctionne comme prévu"

#. translators: %1$s: Link to article about text links, %2$s: Anchor closing
#. tag, %3$s: Emphasis open tag, %4$s: Emphasis close tag
#: admin/class-yoast-columns.php:52
msgid "The links columns show the number of articles on this site linking %3$sto%4$s this article and the number of URLs linked %3$sfrom%4$s this article. Learn more about %1$show to use these features to improve your internal linking%2$s, which greatly enhances your SEO."
msgstr "Les colonnes des liens affichent le nombre d’articles sur ce site faisant un lien %3$svers%4$s cet article et le nombre d’URL liées %3$sdepuis%4$s cet article. En savoir plus sur %1$sces fonctionnalités pour améliorer le maillage interne%2$s, ce qui perfectionnera votre référencement."

#. translators: %1$s: Link to article about content analysis, %2$s: Anchor
#. closing
#: admin/class-yoast-columns.php:43
msgid "We've written an article about %1$show to use the SEO score and Readability score%2$s."
msgstr "Nous avons écrit un article sur %1$sl’utilisation des scores SEO et Lisibilité%2$s."

#. translators: %1$s: Yoast SEO
#: admin/class-yoast-columns.php:36
msgid "%1$s adds several columns to this page."
msgstr "%1$s ajoute plusieurs colonnes à cette page."

#: src/presenters/admin/search-engines-discouraged-presenter.php:41
msgid "I don't want this site to show in the search results."
msgstr "Je ne veux pas que ce site apparaisse dans les résultats de recherche."

#. translators: 1: Link start tag to the WordPress Reading Settings page, 2:
#. Link closing tag.
#: src/presenters/admin/search-engines-discouraged-presenter.php:36
msgid "If you want search engines to show this site in their results, you must %1$sgo to your Reading Settings%2$s and uncheck the box for Search Engine Visibility."
msgstr "Si vous souhaitez que les moteurs de recherche affichent ce site dans leurs résultats, vous devez %1$svous rendre dans vos réglages de lecture%2$s et décocher la case « Visibilité pour les moteurs de recherche »."

#: src/presenters/admin/migration-error-presenter.php:64
msgid "Show debug information"
msgstr "Afficher les informations de débogage"

#. translators: %s: Yoast SEO.
#: src/presenters/admin/migration-error-presenter.php:58
msgid "Your site will continue to work normally, but won't take full advantage of %s."
msgstr "Votre site continuera de fonctionner normalement, mais ne profitera pas pleinement de %s."

#. translators: %s: Yoast SEO.
#: src/presenters/admin/migration-error-presenter.php:47
msgid "%s had problems creating the database tables needed to speed up your site."
msgstr "%s a rencontré des problème pour créer les tables de base de données nécessaires pour optimiser votre site."

#: src/presenters/admin/indexing-notification-presenter.php:117
msgid " We estimate this will take less than a minute."
msgstr "Nous estimons que cela prendra moins d’une minute."

#: src/presenters/admin/indexing-notification-presenter.php:121
msgid " We estimate this will take a couple of minutes."
msgstr "Nous estimons que cela prendra quelques minutes."

#. translators: 1: Link to article about indexation command, 2: Anchor closing
#. tag, 3: Link to WP CLI.
#: src/presenters/admin/indexing-notification-presenter.php:136
msgid "%1$sRun the indexation process on your server%2$s using %3$sWP CLI%2$s."
msgstr "%1$sLancer le processus d’indexation sur votre serveur%2$s en utilisant %3$sWP CLI%2$s."

#: src/presenters/admin/indexing-notification-presenter.php:124
msgid " We estimate this could take a long time, due to the size of your site. As an alternative to waiting, you could:"
msgstr "Nous estimons que cela pourrait prendre beaucoup de temps en raison de la taille de votre site. Au lieu d’attendre, vous pourriez :"

#. translators: %1$s: link to help article about solving table issue. %2$s: is
#. anchor closing.
#: src/presenters/admin/migration-error-presenter.php:52
msgid "Please read %1$sthis help article%2$s to find out how to resolve this problem."
msgstr "Veuillez lire %1$sce guide%2$s pour voir comment résoudre ce problème."

#: inc/class-wpseo-replace-vars.php:1486
msgid "Replaced with the term ancestors hierarchy"
msgstr "Remplacé par la hiérarchie des termes supérieurs"

#: inc/class-wpseo-replace-vars.php:1486
msgid "Term hierarchy"
msgstr "Hiérarchie des termes"

#. translators: 1: Yoast SEO
#: admin/views/class-yoast-feature-toggles.php:181
msgid "This %1$s REST API endpoint gives you all the metadata you need for a specific URL. This will make it very easy for headless WordPress sites to use %1$s for all their SEO meta output."
msgstr "Le point de terminaison REST API de %1$s vous donne toutes les métadonnées d’une URL spécifique. Ainsi, les sites WordPress headless pourront continuer d’utiliser %1$s pour la gestion des métadonnées SEO."

#: admin/views/class-yoast-feature-toggles.php:177
msgid "REST API: Head endpoint"
msgstr "REST API : Point de terminaison Head"

#. translators: 1: link open tag; 2: link close tag.
#: src/services/health-check/default-tagline-reports.php:63
msgid "%1$sYou can change the tagline in the customizer%2$s."
msgstr "%1$sVous pouvez changer le slogan dans l’outil de personnalisation%2$s."

#: src/services/health-check/default-tagline-reports.php:45
msgid "You still have the default WordPress tagline. Even an empty one is probably better."
msgstr "Vous avez encore le slogan WordPress par défaut. Même un slogan vide serait probablement mieux."

#: src/services/health-check/default-tagline-reports.php:43
msgid "You should change the default WordPress tagline"
msgstr "Vous devriez changer le slogan WordPress par défaut"

#: src/services/health-check/default-tagline-reports.php:32
msgid "You are using a custom tagline or an empty one."
msgstr "Vous utilisez un slogan personnalisé ou vide."

#: src/services/health-check/default-tagline-reports.php:30
msgid "You changed the default WordPress tagline"
msgstr "Vous avez changé le slogan WordPress par défaut"

#: src/services/health-check/page-comments-reports.php:32
msgid "Comments on your posts are displayed on a single page. This is just like we'd suggest it. You're doing well!"
msgstr "Les commentaires de vos publications sont affichés sur une seule page. Cela correspond à notre recommandation, tout se passe donc très bien !"

#. translators: %s expands to '/%postname%/'
#: src/services/health-check/postname-permalink-reports.php:58
msgid "It's highly recommended to have your postname in the URL of your posts and pages. Consider setting your permalink structure to %s."
msgstr "Il est fortement recommandé d’avoir le nom de la publication dans l’URL de vos articles et pages. Envisagez de passer votre structure de permaliens à %s."

#: src/services/health-check/postname-permalink-reports.php:43
msgid "You do not have your postname in the URL of your posts and pages"
msgstr "Vous n’avez pas le nom de la publication dans l’URL et de vos articles et pages."

#: src/services/health-check/postname-permalink-reports.php:32
msgid "You do have your postname in the URL of your posts and pages."
msgstr "Vous avez bien le nom de la publication dans l’URL et de vos articles et pages."

#: src/services/health-check/postname-permalink-reports.php:30
msgid "Your permalink structure includes the post name"
msgstr "Votre structure de permaliens inclut le nom de la publication"

#: src/services/health-check/page-comments-reports.php:45
msgid "Comments on your posts break into multiple pages. As this is not needed in 999 out of 1000 cases, we recommend you disable it. To fix this, uncheck \"Break comments into pages...\" on the Discussion Settings page."
msgstr "Les commentaires sur vos publications sont divisés sur plusieurs pages. Ce qui n’est pas nécessaire dans 999 cas sur 1000. Pour corriger ce problème, décochez « Diviser les commentaires en pages […] » dans les réglages de Discussion."

#: src/services/health-check/page-comments-reports.php:43
msgid "Comments break into multiple pages"
msgstr "Commentaires divisés en plusieurs pages"

#: src/services/health-check/page-comments-reports.php:30
msgid "Comments are displayed on a single page"
msgstr "Les commentaires sont affichés sur une seule page"

#: src/helpers/post-helper.php:112
msgid "No title"
msgstr "Aucun titre"

#. translators: 1: Start of a paragraph beginning with the Yoast icon, 2:
#. Expands to 'Yoast SEO', 3: Paragraph closing tag.
#: src/services/health-check/report-builder.php:201
msgid "%1$sThis was reported by the %2$s plugin%3$s"
msgstr "%1$sCela a été remonté par l’extension %2$s%3$s"

#. translators: 1: Opening tag of the link to the discussion settings page, 2:
#. Link closing tag.
#: src/services/health-check/page-comments-reports.php:58
msgid "%1$sGo to the Discussion Settings page%2$s"
msgstr "%1$sAllez dans les réglages de discussion%2$s"

#: admin/metabox/class-metabox.php:194
msgid "If you want to apply advanced <code>meta</code> robots settings for this page, please define them in the following field."
msgstr "Si vous souhaitez appliquer des réglages de <code>métadonnées</code> robots pour cette page, veuillez les définir dans le champ suivant."

#. translators: 1: Link start tag to the Firefox website, 2: Link start tag to
#. the Chrome website, 3: Link start tag to the Edge website, 4: Link closing
#. tag.
#: admin/metabox/class-metabox.php:150 admin/taxonomy/class-taxonomy.php:113
msgid "The browser you are currently using is unfortunately rather dated. Since we strive to give you the best experience possible, we no longer support this browser. Instead, please use %1$sFirefox%4$s, %2$sChrome%4$s or %3$sMicrosoft Edge%4$s."
msgstr "Le navigateur que vous utilisez actuellement est malheureusement obsolète. Puisque nous tenons à vous proposer la meilleure expérience possible, nous ne le prenons plus en charge. À la place, veuillez utiliser %1$sFirefox%4$s, %2$sChrome%4$s ou %3$sMicrosoft Edge%4$s."

#. translators: %1$s expands to Yoast SEO academy
#. translators: %1$s expands to "Yoast SEO academy".
#: src/presenters/admin/sidebar-presenter.php:137 js/dist/general-page.js:3
#: js/dist/new-settings.js:3 js/dist/support.js:3
msgid "Check out %1$s"
msgstr "Jetez un œil à %1$s"

#: src/presenters/admin/sidebar-presenter.php:130 js/dist/general-page.js:2
#: js/dist/new-settings.js:2 js/dist/support.js:2
msgid "We have both free and premium online courses to learn everything you need to know about SEO."
msgstr "Nous avons des formations gratuites et payantes pour vous apprendre le référencement."

#. translators: %1$s expands to Yoast SEO academy, which is a clickable link.
#. translators: %1$s expands to "Yoast SEO" academy, which is a clickable link.
#: src/presenters/admin/sidebar-presenter.php:128 js/dist/general-page.js:2
#: js/dist/new-settings.js:2 js/dist/support.js:2
msgid "Want to learn SEO from Team Yoast? Check out our %1$s!"
msgstr "Vous souhaitez apprendre le référencement avec l’équipe de Yoast ? Jetez un œil à notre %1$s !"

#: src/presenters/admin/sidebar-presenter.php:120 js/dist/general-page.js:2
#: js/dist/new-settings.js:2 js/dist/support.js:2
msgid "Learn SEO"
msgstr "Apprenez le SEO"

#. translators: %s expands to Yoast SEO
#: admin/views/tabs/tool/wpseo-import.php:39
msgid "%s settings to import:"
msgstr "Réglages %s à importer :"

#. translators: 1: expands to Yoast SEO, 2: expands to Import settings.
#: admin/views/tabs/tool/wpseo-import.php:23
msgid "Import settings from another %1$s installation by pasting them here and clicking \"%2$s\"."
msgstr "Importez des réglages d’une autre installation %1$s en les collant ici et en cliquant sur « %2$s »."

#. translators: %1$s expands to Yoast SEO
#: admin/class-export.php:72
msgid "Your %1$s settings:"
msgstr "Vos réglages %1$s :"

#: admin/metabox/class-metabox.php:427 js/dist/block-editor.js:573
#: js/dist/elementor.js:540 js/dist/new-settings.js:33
#: js/dist/new-settings.js:38 js/dist/new-settings.js:42
#: js/dist/new-settings.js:71 js/dist/new-settings.js:254
msgid "Schema"
msgstr "Schema"

#: admin/admin-settings-changed-listener.php:85
msgid "Settings saved."
msgstr "Réglages enregistrés."

#. translators: Hidden accessibility text.
#: admin/views/partial-notifications-template.php:47
msgid "Show this item."
msgstr "Afficher cet élément."

#. translators: Hidden accessibility text.
#: admin/views/partial-notifications-template.php:39
msgid "Hide this item."
msgstr "Masquer cet élément."

#. translators: %d expands the amount of hidden notifications.
#: admin/views/partial-notifications-errors.php:25
#: admin/views/partial-notifications-warnings.php:25
msgid "You have %d hidden notification:"
msgid_plural "You have %d hidden notifications:"
msgstr[0] "Vous avez %d notification masquée :"
msgstr[1] "Vous avez %d notifications masquées :"

#: src/helpers/score-icon-helper.php:84
msgid "Focus keyphrase not set"
msgstr "Expression clé principale non définie"

#. translators: %1$s: amount of errors, %2$s: the admin page title
#: admin/class-yoast-input-validation.php:65
msgid "The form contains %1$s error. %2$s"
msgid_plural "The form contains %1$s errors. %2$s"
msgstr[0] "Le formulaire contient %1$s erreur. %2$s"
msgstr[1] "Le formulaire contient %1$s erreurs. %2$s"

#. translators: %s expands to the score
#: admin/statistics/class-statistics-service.php:216
#: admin/statistics/class-statistics-service.php:221
#: admin/statistics/class-statistics-service.php:226
msgid "Posts with the SEO score: %s"
msgstr "Publications avec le score SEO : %s"

#. translators: %s: expends to Yoast SEO
#: admin/class-admin.php:354
msgid "%s video tutorial"
msgstr "Tutoriel vidéo de %s"

#: inc/class-wpseo-rank.php:191
msgid "Post Noindexed"
msgstr "Publication désindexée"

#: inc/class-wpseo-rank.php:171
msgid "No Focus Keyphrase"
msgstr "Aucune expression clé principale"

#. translators: %s expands to the SEO score
#: inc/class-wpseo-rank.php:170 inc/class-wpseo-rank.php:175
#: inc/class-wpseo-rank.php:180 inc/class-wpseo-rank.php:185
#: inc/class-wpseo-rank.php:190
msgid "SEO: %s"
msgstr "SEO : %s"

#. Translators: %1$s: expands to opening anchor tag, %2$s expands to closing
#. anchor tag.
#: admin/google_search_console/views/gsc-display.php:39
msgid "To view your current crawl errors, %1$splease visit Google Search Console%2$s."
msgstr "Pour voir vos erreurs d’exploration actuelles, %1$sveuillez vous rendre sur Google Search Console%2$s."

#. Translators: %1$s: expands to opening anchor tag, %2$s expands to closing
#. anchor tag.
#: admin/google_search_console/views/gsc-display.php:32
msgid "Google has discontinued its Crawl Errors API. Therefore, any possible crawl errors you might have cannot be displayed here anymore. %1$sRead our statement on this for further information%2$s."
msgstr "Google a mis fin à son API d’erreurs d’exploration. Par conséquent, les éventuelles erreurs que vous pourriez avoir ne peuvent plus être affichées ici. %1$sLisez notre déclaration à ce sujet pour plus d’informations%2$s."

#: src/integrations/admin/first-time-configuration-integration.php:480
#: src/integrations/admin/first-time-configuration-integration.php:493
#: js/dist/new-settings.js:324
msgid "Organization"
msgstr "Entité"

#. translators: %1$s is a link start tag to the Search Appearance settings,
#. %2$s is the link closing tag.
#: admin/class-schema-person-upgrade-notification.php:66
msgid "You have previously set your site to represent a person. We’ve improved our functionality around Schema and the Knowledge Graph, so you should go in and %1$scomplete those settings%2$s."
msgstr "Vous avez précédemment réglé votre site en tant que représentant d’une personne. Nous avons amélioré les fonctionnalités de données structurées (Schema et Knowledge Graph), donc nous vous invitons à %1$s compléter ces réglages%2$s."

#: admin/class-admin.php:317
#: src/user-meta/framework/additional-contactmethods/wikipedia.php:28
msgid "(if one exists)"
msgstr "(si une existe)"

#: admin/class-admin.php:317
#: src/user-meta/framework/additional-contactmethods/wikipedia.php:28
msgid "Wikipedia page about you"
msgstr "Page Wikipédia à propos de vous"

#: admin/class-admin.php:316
#: src/user-meta/framework/additional-contactmethods/youtube.php:28
msgid "YouTube profile URL"
msgstr "URL de profil YouTube"

#: admin/class-admin.php:314
#: src/user-meta/framework/additional-contactmethods/tumblr.php:28
msgid "Tumblr profile URL"
msgstr "URL de profil Tumblr"

#: admin/class-admin.php:313
#: src/user-meta/framework/additional-contactmethods/soundcloud.php:28
msgid "SoundCloud profile URL"
msgstr "URL de profil Soundcloud"

#: admin/class-admin.php:311
#: src/user-meta/framework/additional-contactmethods/myspace.php:28
msgid "MySpace profile URL"
msgstr "URL de profil Myspace"

#: src/generators/schema/article.php:141
msgid "Uncategorized"
msgstr "Non classé"

#: admin/class-admin.php:312
#: src/user-meta/framework/additional-contactmethods/pinterest.php:28
msgid "Pinterest profile URL"
msgstr "URL de profil Pinterest"

#: admin/class-admin.php:310
#: src/user-meta/framework/additional-contactmethods/linkedin.php:28
msgid "LinkedIn profile URL"
msgstr "URL de profil LinkedIn"

#: admin/class-admin.php:309
#: src/user-meta/framework/additional-contactmethods/instagram.php:28
msgid "Instagram profile URL"
msgstr "URL de profil Instagram"

#: inc/class-my-yoast-api-request.php:140
msgid "No JSON object was returned."
msgstr "Aucun objet JSON n’a été renvoyé."

#. translators: Hidden accessibility text.
#: src/integrations/admin/link-count-columns-integration.php:149
msgid "Received internal links"
msgstr "Liens internes reçus"

#. translators: Hidden accessibility text.
#: src/integrations/admin/link-count-columns-integration.php:141
msgid "Outgoing internal links"
msgstr "Liens internes émis"

#: admin/class-meta-columns.php:132 js/dist/block-editor.js:170
#: js/dist/classic-editor.js:155 js/dist/editor-modules.js:291
#: js/dist/elementor.js:510 js/dist/wincher-dashboard-widget.js:117
msgid "Keyphrase"
msgstr "Expression clé"

#. translators: 1: Yoast SEO.
#: src/services/health-check/links-table-reports.php:87
msgid "For this feature to work, %1$s needs to create a table in your database. We were unable to create this table automatically."
msgstr "Pour que cette fonctionnalité fonctionne, %1$s a besoin de créer une table dans votre base de données. Nous n’avons pas réussi à le faire automatiquement."

#. translators: %1$s expands to the requested url
#: admin/exceptions/class-file-size-exception.php:40
msgid "Cannot get the size of %1$s because of unknown reasons."
msgstr "Impossible de récupérer la taille de %1$s pour des raisons inconnues."

#. translators: %1$s expands to the requested url
#: admin/exceptions/class-file-size-exception.php:23
msgid "Cannot get the size of %1$s because it is hosted externally."
msgstr "Impossible de récupérer la taille de %1$s car elle est externe au site."

#. translators: %s expands to the current page number
#: src/generators/breadcrumbs-generator.php:424
msgid "Page %s"
msgstr "Page %s"

#. translators: %1$s expands to the method name. %2$s expands to the class name
#: src/exceptions/missing-method.php:24
msgid "Method %1$s() does not exist in class %2$s"
msgstr "La méthode %1$s() n’existe pas dans la classe %2$s"

#. translators: %s expands to Yoast SEO Premium
#: admin/watchers/class-slug-change-watcher.php:226
msgid "With %s, you can easily create such redirects."
msgstr "Avec %s, vous pouvez facilement créer de telles redirections."

#: admin/views/tabs/tool/wpseo-import.php:15
msgid "Import of settings is only supported on servers that run PHP 5.3 or higher."
msgstr "L’import de réglages ne fonctionne que sur les serveurs qui fonctionnent sous PHP 5.3 ou supérieur."

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/tool/wpseo-export.php:26
msgid "Export your %1$s settings here, to copy them on another site."
msgstr "Exportez vos réglages de %1$s ici, afin de les copier vers un autre site."

#: admin/import/class-import-settings.php:85
msgid "No settings found."
msgstr "Aucun réglage trouvé."

#. translators: %1$s expands to Yoast SEO, %2$s expands to Yoast.com
#: admin/class-export.php:97
msgid "These are settings for the %1$s plugin by %2$s"
msgstr "Ce sont des réglages pour l’extension %1$s par %2$s"

#. translators: %1$s expands to Import settings
#: admin/class-export.php:61
msgid "Copy all these settings to another site's %1$s tab and click \"%1$s\" there."
msgstr "Copiez tous ces réglages dans l’onglet %1$s d’un autre site et cliquez ensuite sur « %1$s »."

#: admin/class-export.php:54
msgid "You do not have the required rights to export settings."
msgstr "Vous n’avez pas les droits suffisant pour exporter les réglages."

#. translators: %s expands to Yoast SEO Premium
#. translators: %s expands to "Yoast SEO" Premium
#: admin/class-premium-upsell-admin-block.php:125 js/dist/block-editor.js:34
#: js/dist/classic-editor.js:19 js/dist/elementor.js:19
#: js/dist/externals-components.js:200 js/dist/general-page.js:15
#: js/dist/integrations-page.js:49 js/dist/new-settings.js:15
#: js/dist/post-edit.js:13 js/dist/support.js:15 js/dist/term-edit.js:13
msgid "Upgrade to %s"
msgstr "Passez à %s"

#: admin/class-admin-init.php:354
msgid "Learn about why permalinks are important for SEO."
msgstr "Découvrez en quoi les permaliens sont importants pour votre référencement."

#. translators: %1$s and %2$s expand to <em> items to emphasize the word in the
#. middle.
#: admin/class-admin-init.php:348
msgid "Changing your permalinks settings can seriously impact your search engine visibility. It should almost %1$s never %2$s be done on a live website."
msgstr "Changer les réglages de permaliens peut sérieusement impacter votre visibilité sur les moteurs de recherche. On ne devrait %1$s jamais %2$s les changer sur un site en production."

#: admin/class-admin-init.php:345
msgid "WARNING:"
msgstr "AVERTISSEMENT :"

#: admin/views/tabs/network/features.php:95
#: admin/views/tabs/network/integrations.php:82
#: src/integrations/admin/crawl-settings-integration.php:251
msgid "Disable"
msgstr "Désactiver"

#: admin/views/tabs/network/features.php:94
#: admin/views/tabs/network/integrations.php:81
#: src/integrations/admin/crawl-settings-integration.php:249
msgid "Allow Control"
msgstr "Autoriser le contrôle"

#. translators: %s expands to Yoast SEO
#: admin/views/tabs/network/crawl-settings.php:24
#: admin/views/tabs/network/features.php:27
msgid "This tab allows you to selectively disable %s features for all sites in the network. By default all features are enabled, which allows site admins to choose for themselves if they want to toggle a feature on or off for their site. When you disable a feature here, site admins will not be able to use that feature at all."
msgstr "Cet onglet vous permet de désactiver certaines fonctionnalités de %s pour tous les sites du réseau. Par défaut, toutes les fonctionnalités sont activées, ce qui permet aux administrateurs et administratrices de choisir lesquelles ils veulent activer/désactiver pour leur site. Lorsque vous désactivez une fonctionnalité ici, les administrateurs et administratrices des sites ne pourront plus l’utiliser du tout."

#. translators: %s: argument name
#: admin/views/class-yoast-feature-toggle.php:161
msgid "%s is a required feature toggle argument."
msgstr "%s est un argument d’activation de fonctionnalité obligatoire."

#: admin/class-yoast-form.php:1064 js/dist/general-page.js:52
msgid "This feature has been disabled by the network admin."
msgstr "Cette fonctionnalité a été désactivée par l’administrateur ou l’administratrice du réseau."

#. translators: Hidden accessibility text.
#: admin/class-meta-columns.php:197
msgid "Focus keyphrase not set."
msgstr "Expression clé principale non définie."

#. translators: %s expands to Yoast SEO Premium
#: admin/class-premium-popup.php:81
#: admin/watchers/class-slug-change-watcher.php:230
msgid "Get %s"
msgstr "%s"

#: inc/class-wpseo-admin-bar-menu.php:887
msgid "There is a new notification."
msgid_plural "There are new notifications."
msgstr[0] "Il y a une nouvelle notification."
msgstr[1] "Il y a des nouvelles notifications."

#: inc/options/class-wpseo-option-titles.php:949
msgid "Colon"
msgstr "Deux-points"

#. translators: %1$s expands to Yoast SEO, %2$s: 'SEO' plugin name of possibly
#. conflicting plugin with regard to the creation of duplicate SEO meta.
#: admin/class-plugin-conflict.php:90
msgid "Both %1$s and %2$s manage the SEO of your site. Running two SEO plugins at the same time is detrimental."
msgstr "%1$s et %2$s gèrent tous les deux le référencement de votre site. En ayant deux solutions concurrentes, vous pourriez créer des conflits au détriment de votre visibilité."

#. translators: %d expands to the number of minute/minutes.
#. translators: %d expands to the number of minutes.
#: src/integrations/blocks/structured-data-blocks.php:146
#: js/dist/how-to-block.js:8 js/dist/how-to-block.js:14
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d minute"
msgstr[1] "%d minutes"

#. translators: %d expands to the number of hour/hours.
#. translators: %d expands to the number of hours.
#: src/integrations/blocks/structured-data-blocks.php:139
#: js/dist/how-to-block.js:7 js/dist/how-to-block.js:13
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d heure"
msgstr[1] "%d heures"

#. translators: %d expands to the number of day/days.
#. translators: %d expands to the number of days.
#: src/integrations/blocks/structured-data-blocks.php:132
#: js/dist/block-editor.js:149 js/dist/classic-editor.js:134
#: js/dist/editor-modules.js:270 js/dist/elementor.js:489
#: js/dist/how-to-block.js:6 js/dist/how-to-block.js:12
#: js/dist/wincher-dashboard-widget.js:43
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d jour"
msgstr[1] "%d jours"

#: blocks/structured-data-blocks/how-to/block.json
msgctxt "block description"
msgid "Create a How-to guide in an SEO-friendly way. You can only use one How-to block per post."
msgstr "Créez un tutoriel optimisé pour le référencement. Vous ne pouvez utiliser qu’un seul bloc Tutoriel par publication."

#. translators: %1$s expands to Yoast.
#: src/integrations/blocks/block-categories.php:37
msgid "%1$s Structured Data Blocks"
msgstr "Blocs de données structurées de %1$s"

#: src/integrations/blocks/structured-data-blocks.php:197
#: js/dist/how-to-block.js:5 js/dist/how-to-block.js:16
msgid "Time needed:"
msgstr "Temps nécessaire :"

#: inc/class-wpseo-admin-bar-menu.php:439
msgid "Check links to this URL"
msgstr "Vérifier les liens vers cette URL"

#: inc/class-wpseo-admin-bar-menu.php:510
msgid "How to"
msgstr "Tutoriel"

#: admin/pages/network.php:31
msgid "Restore Site"
msgstr "Restaurer le site"

#: admin/menu/class-network-admin-menu.php:34
msgid "Network Settings"
msgstr "Réglages du réseau"

#: admin/class-yoast-network-admin.php:276
msgid "You are not allowed to perform this action."
msgstr "Vous n’avez pas les droits suffisants pour faire cette action."

#. translators: %s: error message
#: admin/class-yoast-network-admin.php:208
msgid "Error: %s"
msgstr "Erreur : %s"

#. translators: %s: success message
#: admin/class-yoast-network-admin.php:206
msgid "Success: %s"
msgstr "Succès : %s"

#. translators: %s expands to the ID of a site within a multisite network.
#: admin/class-yoast-network-admin.php:168
msgid "Site with ID %d not found."
msgstr "Le site avec l’ID %d est introuvable."

#: admin/class-yoast-network-admin.php:159
msgid "No site has been selected to restore."
msgstr "Aucun site n’a été sélectionné pour la restauration."

#: admin/class-yoast-network-admin.php:120
msgid "You are not allowed to modify unregistered network settings."
msgstr "Vous n’avez pas les droits suffisants pour modifier les réglages d’un réseau non-enregistré."

#: admin/class-yoast-network-admin.php:81
msgid "deleted"
msgstr "supprimé"

#: inc/class-wpseo-replace-vars.php:1475
msgid "The site's tagline"
msgstr "Le slogan du site"

#. translators: %1$s expands to the missing field name.
#: admin/menu/class-replacevar-editor.php:152
msgid "Not all required fields are given. Missing field %1$s"
msgstr "Tous les champs nécessaires ne sont pas renseignés. Champ manquant %1$s"

#: inc/class-wpseo-replace-vars.php:1489 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "Current year"
msgstr "Année en cours"

#: inc/class-wpseo-replace-vars.php:1475 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1 js/dist/new-settings.js:38
#: js/dist/new-settings.js:306
msgid "Tagline"
msgstr "Slogan"

#: inc/class-wpseo-replace-vars.php:1520 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "Page"
msgstr "Page"

#: inc/class-wpseo-replace-vars.php:1528
msgid "description (custom taxonomy)"
msgstr "description (taxonomie personnalisée)"

#: inc/class-wpseo-replace-vars.php:1527
msgid "(custom taxonomy)"
msgstr "(taxonomie personnalisée)"

#: inc/class-wpseo-replace-vars.php:1526
msgid "(custom field)"
msgstr "(champ personnalisé)"

#: inc/class-wpseo-replace-vars.php:1525
msgid "Term404"
msgstr "Phrase404"

#: inc/class-wpseo-replace-vars.php:1523
msgid "Caption"
msgstr "Légende"

#: inc/class-wpseo-replace-vars.php:1522
msgid "Pagenumber"
msgstr "Numéropage"

#: inc/class-wpseo-replace-vars.php:1521
msgid "Pagetotal"
msgstr "Nombrepages"

#: inc/class-wpseo-replace-vars.php:1519
msgid "User description"
msgstr "Description du compte"

#: inc/class-wpseo-replace-vars.php:1517 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "ID"
msgstr "ID"

#: inc/class-wpseo-replace-vars.php:1516
msgid "Modified"
msgstr "Modifié"

#: inc/class-wpseo-replace-vars.php:1515
msgid "Post type (plural)"
msgstr "Types de publication (au pluriel)"

#: inc/class-wpseo-replace-vars.php:1514
msgid "Post type (singular)"
msgstr "Type de publication (au singulier)"

#: inc/class-wpseo-replace-vars.php:1487 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1 js/dist/new-settings.js:38
msgid "Separator"
msgstr "Séparateur"

#: inc/class-wpseo-replace-vars.php:1485 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "Search phrase"
msgstr "Phrase de recherche"

#: inc/class-wpseo-replace-vars.php:1484
msgid "Term title"
msgstr "Titre du terme"

#: inc/class-wpseo-replace-vars.php:1483 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "Term description"
msgstr "Description de l’élément"

#: inc/class-wpseo-replace-vars.php:1482 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "Tag description"
msgstr "Étiquette du contenu"

#: inc/class-wpseo-replace-vars.php:1481 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "Category description"
msgstr "Description de la catégorie"

#: inc/class-wpseo-replace-vars.php:1480 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "Primary category"
msgstr "Catégorie principale"

#: inc/class-wpseo-replace-vars.php:1479 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "Category"
msgstr "Catégorie"

#: inc/class-wpseo-replace-vars.php:1478
msgid "Tag"
msgstr "Étiquette"

#: inc/class-wpseo-replace-vars.php:1477 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "Excerpt only"
msgstr "Extrait seulement"

#: inc/class-wpseo-replace-vars.php:1476 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "Excerpt"
msgstr "Extrait"

#: inc/class-wpseo-replace-vars.php:1474 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "Site title"
msgstr "Titre du site"

#: inc/class-wpseo-replace-vars.php:1473
msgid "Archive title"
msgstr "Titre de l’archive"

#: inc/class-wpseo-replace-vars.php:1472 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "Parent title"
msgstr "Titre parent"

#: inc/class-wpseo-replace-vars.php:1470 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "Date"
msgstr "Date"

#: inc/class-wpseo-admin-bar-menu.php:608
msgid "Get Yoast SEO Premium"
msgstr "Obtenez Yoast SEO Premium"

#: admin/watchers/class-slug-change-watcher.php:224
msgid "You should create a redirect to ensure your visitors do not get a 404 error when they click on the no longer working URL."
msgstr "Vous devriez créer une redirection pour vous assurer que les visiteurs n’obtiennent pas une erreur 404 lorsqu’ils cliquent sur des URL cassées."

#: admin/watchers/class-slug-change-watcher.php:90
#: admin/watchers/class-slug-change-watcher.php:113
msgid "Search engines and other websites can still send traffic to your deleted content."
msgstr "Les moteurs de recherche et d’autres sites peuvent continuer à envoyer du trafic à votre contenu supprimé."

#: admin/watchers/class-slug-change-watcher.php:220
msgid "Make sure you don't miss out on traffic!"
msgstr "Assurez-vous de ne pas perdre de trafic !"

#. translators: %1$s expands to the translated name of the post type.
#. translators: %1$s expands to the translated name of the term.
#: admin/watchers/class-slug-change-watcher.php:89
#: admin/watchers/class-slug-change-watcher.php:112
msgid "You just deleted a %1$s."
msgstr "Vous venez de supprimer un·e %1$s."

#. translators: %1$s expands to the translated name of the post type.
#: admin/watchers/class-slug-change-watcher.php:67
msgid "You just trashed a %1$s."
msgstr "Vous venez de mettre un·e %1$s à la corbeille."

#. translators: %s is replaced with Yoast SEO.
#: admin/import/plugins/class-abstract-plugin-importer.php:259
msgid "The %s importer functionality uses temporary database tables. It seems your WordPress install does not have the capability to do this, please consult your hosting provider."
msgstr "L’importateur %s utilise des tables temporaires de base de données. Il semblerait que votre installation WordPress ne le permette pas. Veuillez solliciter votre hébergeur."

#. translators: %s is replaced with the plugin's name.
#: admin/import/plugins/class-abstract-plugin-importer.php:132
msgid "Cleanup of %s data failed."
msgstr "Le nettoyage des données de %s a échoué."

#: admin/class-bulk-editor-list-table.php:1037
msgid "Content Type"
msgstr "Type de contenu"

#. translators: Hidden accessibility text.
#: admin/class-bulk-editor-list-table.php:429
msgid "Filter by content type"
msgstr "Filtrer par type de contenu"

#: admin/class-bulk-editor-list-table.php:411
msgid "Show All Content Types"
msgstr "Afficher tous les types de publication"

#: inc/class-wpseo-replace-vars.php:1473
msgid "Replaced with the normal title for an archive generated by WordPress"
msgstr "Remplacé avec un titre d’archive normal généré par WordPress"

#: admin/views/tabs/tool/import-seo.php:126
msgid "Clean"
msgstr "Propre"

#: admin/views/tabs/tool/import-seo.php:117
msgid "Once you're certain your site is OK, you can clean up. This will remove all the original data."
msgstr "Une fois que vous avez la certitude que votre site est OK, vous pouvez le nettoyer. Cela retirera toutes les données antérieures."

#: admin/views/tabs/tool/import-seo.php:115
msgid "Step 5: Clean up"
msgstr "Étape 5 : Nettoyez"

#: admin/views/tabs/tool/import-seo.php:95
msgid "Please check your posts and pages and see if the metadata was successfully imported."
msgstr "Veuillez vérifier que les métadonnées de vos articles et pages soient bien importés."

#: admin/views/tabs/tool/import-seo.php:93
msgid "Step 3: Check your data"
msgstr "Étape 3 : Vérifiez vos données"

#. translators: 1: expands to Yoast SEO
#: admin/views/tabs/tool/import-seo.php:67
msgid "This will import the post metadata like SEO titles and descriptions into your %1$s metadata. It will only do this when there is no existing %1$s metadata yet. The original data will remain in place."
msgstr "Cela importera les métadonnées de vos publications telles que les titres et descriptions SEO dans %1$s. Cela ne fonctionnera que s’il n’existe pas encore de métadonnées appartenant à %1$s. Les données antérieures ne seront pas effacées."

#: admin/views/tabs/tool/import-seo.php:62
msgid "Step 2: Import"
msgstr "Étape 2 : Importez"

#: admin/views/tabs/tool/import-seo.php:57
msgid "Please make a backup of your database before starting this process."
msgstr "Veuillez faire une sauvegarde de votre base de données avant de lancer ce processus."

#: admin/views/tabs/tool/import-seo.php:55
msgid "Step 1: Create a backup"
msgstr "Étape 1 : Sauvegardez"

#: admin/views/tabs/tool/import-seo.php:51
msgid "We've detected data from one or more SEO plugins on your site. Please follow the following steps to import that data:"
msgstr "Nous avons détecté que vous utilisez une ou plusieurs extensions SEO sur votre site. Veuillez suivre les étapes suivantes pour en importer les données :"

#: admin/views/tabs/tool/import-seo.php:39
msgid "Plugin: "
msgstr "Extension : "

#. translators: %s expands to Yoast SEO
#: admin/views/tabs/tool/import-seo.php:22
msgid "%s did not detect any plugin data from plugins it can import from."
msgstr "%s n’a pas détecté d’extension dont il peut importer les données."

#: admin/statistics/class-statistics-service.php:229
msgid "Posts that should not show up in search results"
msgstr "Articles qui ne devraient pas apparaître dans les résultats de recherche"

#. translators: %s is replaced with the name of the plugin we've found data
#. from.
#: admin/import/class-import-status.php:128
msgid "%s data found."
msgstr "%s données trouvées."

#. translators: %s is replaced with the name of the plugin we're removing data
#. from.
#: admin/import/class-import-status.php:124
msgid "%s data successfully removed."
msgstr "Données de %s retirées."

#. translators: %s is replaced with the name of the plugin we're importing data
#. from.
#: admin/import/class-import-status.php:121
msgid "%s data successfully imported."
msgstr "Données de %s importées."

#. translators: %s is replaced with the name of the plugin we're trying to find
#. data from.
#: admin/import/class-import-status.php:61
msgid "%s data not found."
msgstr "%s données introuvables."

#: admin/views/user-profile.php:17
msgid "this author's archives"
msgstr "archives de cet auteur ou cette autrice"

#. translators: %s expands to "this author's archives".
#: admin/views/user-profile.php:16
msgid "Do not allow search engines to show %s in search results."
msgstr "Empêcher les moteurs de recherche d’afficher les %s dans les résultats de recherche."

#: admin/class-yoast-form.php:960 admin/class-yoast-form.php:1000
#: js/dist/externals/componentsNew.js:766
msgid "On"
msgstr "Activé"

#. translators: Hidden accessibility text; %s expands to a feature's name.
#. translators: Hidden accessibility text; %s expands to an integration's name.
#: admin/views/tabs/network/features.php:62
#: admin/views/tabs/network/integrations.php:50
msgid "Help on: %s"
msgstr "Aide sur : %s"

#: admin/class-yoast-form.php:961 admin/class-yoast-form.php:1001
#: admin/views/class-yoast-feature-toggles.php:160
#: js/dist/externals/componentsNew.js:766
msgid "Off"
msgstr "Désactivé"

#: admin/views/class-yoast-feature-toggles.php:140
msgid "Read why XML Sitemaps are important for your site."
msgstr "Découvrez pourquoi les plans de site XML sont importants pour votre site."

#. translators: %s: Yoast SEO
#: admin/views/class-yoast-feature-toggles.php:139
msgid "Enable the XML sitemaps that %s generates."
msgstr "Activer les plans de site générés par %s."

#: admin/views/class-yoast-feature-toggles.php:70
msgid "See the XML sitemap."
msgstr "Voir le plan de site XML."

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/dashboard/dashboard.php:19
msgid "See who contributed to %1$s."
msgstr "Voir qui a contribué à %1$s."

#. translators: %1$s expands to the post type name.
#: admin/metabox/class-metabox.php:189
msgid "Should search engines follow links on this %1$s?"
msgstr "Les moteurs de recherche doivent-ils suivre les liens sur le contenu %1$s ?"

#. translators: %1$s expands to Yes or No,  %2$s expands to the post type name.
#: admin/metabox/class-metabox.php:184
msgid "Default for %2$s, currently: %1$s"
msgstr "Réglages par défaut pour %2$s, actuellement : %1$s"

#. translators: %s expands to the post type name.
#: admin/metabox/class-metabox.php:179
msgid "Allow search engines to show this %s in search results?"
msgstr "Autoriser les moteurs de recherche à afficher le contenu %s dans les résultats de recherche ?"

#. translators: %s expands to an indexable object's name, like a post type or
#. taxonomy
#: admin/class-yoast-form.php:971
msgid "Show %s in search results?"
msgstr "Afficher les %s dans les résultats de recherche ?"

#. translators: %1$s expands to Yoast SEO.
#: admin/class-plugin-conflict.php:82
msgid "Toggle %1$s's XML Sitemap"
msgstr "Activer le plan de site XML de %1$s"

#. translators: %s: 'Semrush'
#. translators: %s: Algolia.
#: admin/views/class-yoast-integration-toggles.php:67
#: admin/views/class-yoast-integration-toggles.php:78
msgid "%s integration"
msgstr "Intégration de %s"

#: admin/views/class-yoast-feature-toggles.php:111
msgid "Find out how the text link counter can enhance your SEO."
msgstr "Découvrez comment le compteur de liens peut améliorer votre SEO."

#: admin/views/class-yoast-feature-toggles.php:110
msgid "The text link counter helps you improve your site structure."
msgstr "Le compteur de liens vous aide à améliorer la structure de votre site."

#: admin/views/class-yoast-feature-toggles.php:103
msgid "Find out how cornerstone content can help you improve your site structure."
msgstr "Découvrez comment les contenus piliers peuvent améliorer votre structure de site."

#: admin/views/class-yoast-feature-toggles.php:102
msgid "The cornerstone content feature lets you to mark and filter cornerstone content on your website."
msgstr "La fonctionnalité des contenus piliers vous permets de distinguer et filtrer les contenus les plus importants de votre site."

#: admin/views/class-yoast-feature-toggles.php:86
msgid "Discover why readability is important for SEO."
msgstr "Découvrez pourquoi la lisibilité est importante pour le SEO."

#: admin/views/class-yoast-feature-toggles.php:85 js/dist/new-settings.js:312
msgid "The readability analysis offers suggestions to improve the structure and style of your text."
msgstr "L’analyse de lisibilité propose des suggestions pour améliorer la structure et le style de votre texte."

#: admin/views/class-yoast-feature-toggles.php:78
msgid "Learn how the SEO analysis can help you rank."
msgstr "Découvrez comment l’analyse SEO peut améliorer votre référencement."

#: admin/views/class-yoast-feature-toggles.php:77
msgid "The SEO analysis offers suggestions to improve the SEO of your text."
msgstr "L’analyse SEO propose des suggestions pour améliorer le référencement de votre texte."

#: admin/views/class-yoast-feature-toggles.php:75
#: js/dist/externals-components.js:264 js/dist/new-settings.js:38
#: js/dist/new-settings.js:312
msgid "SEO analysis"
msgstr "Analyse SEO"

#. Author URI of the plugin
#: wp-seo.php
msgid "https://yoa.st/1uk"
msgstr "https://yoa.st/1uk"

#. Plugin URI of the plugin
#: wp-seo.php
msgid "https://yoa.st/1uj"
msgstr "https://yoa.st/1uj"

#. translators: %1$s resolves to Yoast.com
#: admin/class-yoast-dashboard-widget.php:130
msgid "Latest blog posts on %1$s"
msgstr "Derniers articles sur %1$s"

#: src/helpers/first-time-configuration-notice-helper.php:64
msgid "First-time SEO configuration"
msgstr "Première configuration SEO"

#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:186
msgid "%s file"
msgstr "fichier %s"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:174 admin/views/tool-file-editor.php:224
msgid "Save changes to %s"
msgstr "Enregistrer les changements de %s"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:165 admin/views/tool-file-editor.php:215
msgid "Edit the content of your %s:"
msgstr "Modifiez le contenu de votre %s :"

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:125
msgid "Create %s file"
msgstr "Créer le fichier %s"

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:65
msgid "Updated %s"
msgstr "%s mis à jour"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:49 admin/views/tool-file-editor.php:76
msgid "You cannot edit the %s file."
msgstr "Vous ne pouvez pas modifier le fichier %s."

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:28
msgid "You cannot create a %s file."
msgstr "Vous ne pouvez pas créer de fichier %s."

#. translators: Hidden accessibility text; %1$s expands to the dependency name
#: admin/class-suggested-plugins.php:137
msgid "More information about %1$s"
msgstr "Plus d’informations au sujet de %1$s"

#: src/integrations/admin/old-configuration-integration.php:38
msgid "Old Configuration Wizard"
msgstr "Ancien assistant de configuration"

#. translators: %1$s expands to the posttype label, %2$s expands anchor to blog
#. post about cornerstone content, %3$s expands to </a>
#: admin/filters/class-cornerstone-filter.php:104
msgid "Mark the most important %1$s as 'cornerstone content' to improve your site structure. %2$sLearn more about cornerstone content%3$s."
msgstr "Marquez les contenus les plus importants des %1$s comme « contenus piliers » pour améliorer la structure de votre site. %2$sEn savoir plus sur les contenus piliers (en anglais)%3$s."

#. translators: Hidden accessibility text.
#: admin/class-admin-utils.php:79 admin/class-premium-popup.php:83
#: admin/class-premium-upsell-admin-block.php:102
#: admin/class-yoast-form.php:935
#: admin/watchers/class-slug-change-watcher.php:232
#: src/integrations/admin/workouts-integration.php:215
#: src/integrations/admin/workouts-integration.php:245
#: src/presenters/admin/help-link-presenter.php:76 js/dist/academy.js:9
#: js/dist/block-editor.js:19 js/dist/block-editor.js:23
#: js/dist/block-editor.js:25 js/dist/block-editor.js:541
#: js/dist/block-editor.js:542 js/dist/classic-editor.js:4
#: js/dist/classic-editor.js:8 js/dist/classic-editor.js:10
#: js/dist/elementor.js:4 js/dist/elementor.js:8 js/dist/elementor.js:10
#: js/dist/externals-components.js:21 js/dist/externals-components.js:191
#: js/dist/externals-components.js:195 js/dist/externals-components.js:197
#: js/dist/externals/componentsNew.js:1044 js/dist/externals/helpers.js:6
#: js/dist/externals/relatedKeyphraseSuggestions.js:3
#: js/dist/externals/relatedKeyphraseSuggestions.js:7 js/dist/general-page.js:6
#: js/dist/general-page.js:10 js/dist/general-page.js:12
#: js/dist/general-page.js:36 js/dist/integrations-page.js:3
#: js/dist/integrations-page.js:4 js/dist/integrations-page.js:5
#: js/dist/integrations-page.js:6 js/dist/integrations-page.js:7
#: js/dist/integrations-page.js:8 js/dist/integrations-page.js:13
#: js/dist/integrations-page.js:18 js/dist/integrations-page.js:19
#: js/dist/integrations-page.js:20 js/dist/integrations-page.js:40
#: js/dist/integrations-page.js:44 js/dist/integrations-page.js:46
#: js/dist/introductions.js:3 js/dist/introductions.js:4
#: js/dist/new-settings.js:6 js/dist/new-settings.js:10
#: js/dist/new-settings.js:12 js/dist/plans.js:2 js/dist/post-edit.js:4
#: js/dist/post-edit.js:8 js/dist/post-edit.js:10 js/dist/support.js:6
#: js/dist/support.js:10 js/dist/support.js:12 js/dist/support.js:24
#: js/dist/term-edit.js:4 js/dist/term-edit.js:8 js/dist/term-edit.js:10
msgid "(Opens in a new browser tab)"
msgstr "(S’ouvre dans un nouvel onglet)"

#. translators: %1$s expands to an opening strong tag, %2$s expands to a
#. closing strong tag
#: admin/statistics/class-statistics-service.php:210
msgid "Posts %1$swithout%2$s a focus keyphrase"
msgstr "Publications %1$s sans %2$s une expression clé principale"

#: admin/statistics/class-statistics-service.php:77
msgid "Hey, your SEO is doing pretty well! Check out the stats:"
msgstr "Hey, votre SEO se porte plutôt bien ! Regardez les statistiques :"

#: admin/statistics/class-statistics-service.php:73
msgid "You don't have any published posts, your SEO scores will appear here once you make your first post!"
msgstr "Vous n’avez pas encore publié d’articles, vos scores SEO apparaîtront une fois que vous l’aurez fait !"

#: admin/class-yoast-dashboard-widget.php:133
msgid "Read more like this on our SEO blog"
msgstr "Lisez plus d’informations comme celle-ci sur notre blog SEO"

#. translators: %s expands to the readability score
#: inc/class-wpseo-rank.php:207 inc/class-wpseo-rank.php:212
#: inc/class-wpseo-rank.php:217 inc/class-wpseo-rank.php:222
msgid "Readability: %s"
msgstr "Lisibilité : %s"

#. translators: %1$s expands to Yoast
#: src/presenters/admin/sidebar-presenter.php:31
msgid "%1$s recommendations for you"
msgstr "Recommandations %1$s pour vous"

#: admin/class-meta-columns.php:302
msgid "All Readability Scores"
msgstr "Tous les scores de lisibilité"

#. translators: Hidden accessibility text.
#: admin/class-meta-columns.php:298
msgid "Filter by Readability Score"
msgstr "Filtrer par score de lisibilité"

#. translators: %1$s expands to the request method
#: admin/class-remote-request.php:97
msgid "Request method %1$s is not valid."
msgstr "La méthode de requête %1$s n’est pas valide."

#: admin/views/class-yoast-feature-toggles.php:108 js/dist/new-settings.js:38
#: js/dist/new-settings.js:312
msgid "Text link counter"
msgstr "Compteur de lien textuel"

#. translators: %s expands to Yoast
#: admin/class-yoast-columns.php:64
msgid "%s Columns"
msgstr "Colonnes %s"

#: admin/class-meta-columns.php:122 admin/class-meta-columns.php:124
#: admin/taxonomy/class-taxonomy-columns.php:92
#: admin/taxonomy/class-taxonomy-columns.php:93
msgid "Readability score"
msgstr "Score de lisibilité"

#: admin/filters/class-cornerstone-filter.php:87
#: admin/views/class-yoast-feature-toggles.php:100
#: js/dist/externals-components.js:20 js/dist/new-settings.js:38
#: js/dist/new-settings.js:312
msgid "Cornerstone content"
msgstr "Contenu pilier"

#: admin/class-yoast-form.php:149 admin/class-yoast-form.php:154
#: js/dist/general-page.js:48 js/dist/new-settings.js:23
msgid "Save changes"
msgstr "Enregistrer les modifications"

#: admin/class-premium-popup.php:89 js/dist/block-editor.js:285
#: js/dist/block-editor.js:535 js/dist/classic-editor.js:270
#: js/dist/classic-editor.js:520 js/dist/elementor.js:91
#: js/dist/elementor.js:540 js/dist/externals-components.js:243
#: js/dist/externals-components.js:245
msgid "1 year free support and updates included!"
msgstr "1 an de mises à jour et de support offert !"

#. translators: %2$s expands to 'RS Head Cleaner' plugin name of possibly
#. conflicting plugin with regard to differentiating output between search
#. engines and normal users.
#: admin/class-plugin-conflict.php:87
msgid "The plugin %2$s changes your site's output and in doing that differentiates between search engines and normal users, a process that's called cloaking. We highly recommend that you disable it."
msgstr "L’extension %2$s modifie l’apparence de votre site et, ce faisant, elle diffère entre les moteurs de recherche et les utilisateurs normaux, c’est un processus qui se nomme cloaking. Nous vous recommandons vivement de la désactiver."

#: admin/class-premium-upsell-admin-block.php:92
msgid "No ads!"
msgstr "Sans publicités !"

#. translators: %s expands to Yoast SEO Premium
#: src/presenters/admin/sidebar-presenter.php:85
msgid "Get %1$s"
msgstr "Obtenir %1$s"

#: admin/class-admin.php:355
msgid "Scroll to see the table content."
msgstr "Faire défiler pour voir le contenu du tableau."

#: admin/views/partial-notifications-warnings.php:22 js/dist/general-page.js:36
msgid "No new notifications."
msgstr "Aucune nouvelle notification."

#: admin/class-bulk-editor-list-table.php:922
msgid "Save all"
msgstr "Tout enregistrer"

#: admin/class-bulk-editor-list-table.php:921
msgid "Save"
msgstr "Enregistrer"

#. translators: 1: Author name; 2: Site name.
#: inc/options/class-wpseo-option-titles.php:272
msgid "%1$s, Author at %2$s"
msgstr "%1$s, auteur/autrice sur %2$s"

#: inc/class-wpseo-replace-vars.php:1518 js/dist/general-page.js:48
msgid "Name"
msgstr "Nom"

#: admin/views/tool-import-export.php:89
msgid "Export settings"
msgstr "Exporter les réglages"

#: admin/class-product-upsell-notice.php:181
msgid "Please don't show me this notification anymore"
msgstr "Merci de ne plus afficher cette notification"

#. translators: %1$s is a link start tag to the bugreport guidelines on the
#. Yoast help center, %2$s is the link closing tag.
#: admin/class-product-upsell-notice.php:174
msgid "If you are experiencing issues, %1$splease file a bug report%2$s and we'll do our best to help you out."
msgstr "Si vous rencontrez des problèmes, %1$sveuillez remplir un rapport de bug%2$s et nous ferons notre possible pour vous aider."

#. translators: %1$s expands to Yoast SEO, %2$s is a link start tag to the
#. plugin page on WordPress.org, %3$s is the link closing tag.
#: admin/class-product-upsell-notice.php:166
msgid "We've noticed you've been using %1$s for some time now; we hope you love it! We'd be thrilled if you could %2$sgive us a 5 stars rating on WordPress.org%3$s!"
msgstr "Nous avons remarqué que vous utilisez %1$s depuis un certain temps ; nous espérons que vous l’adorez ! Nous serions très heureux si vous pouviez %2$snous attribuer 5 étoiles sur WordPress.org%3$s !"

#. translators: %1$s: '%%term_title%%' variable used in titles and meta's
#. template that's not compatible with the given template, %2$s: expands to
#. 'HelpScout beacon'
#: admin/class-admin.php:349
msgid "Warning: the variable %1$s cannot be used in this template. See the %2$s for more info."
msgstr "Avertissement : la variable %1$s ne peut pas être utilisée dans ce modèle. Consultez le centre d’aide %2$s pour plus d’informations."

#. translators: %1$s expands anchor to premium plugin page, %2$s expands to
#. </a>
#: admin/class-product-upsell-notice.php:149
msgid "By the way, did you know we also have a %1$sPremium plugin%2$s? It offers advanced features, like a redirect manager and support for multiple keyphrases. It also comes with 24/7 personal support."
msgstr "Au fait, saviez-vous que nous avons aussi une %1$sextension Premium%2$s ? Elle offre des fonctionnalités avancées, comme un gestionnaire de redirections et la prise en charge de plusieurs expressions clés. Elle inclut également un support personnel 24/7."

#: admin/class-bulk-editor-list-table.php:829
msgid "(no title)"
msgstr "(sans titre)"

#. translators: 1: Yoast SEO
#: admin/views/class-yoast-feature-toggles.php:150
msgid "The %1$s admin bar menu contains useful links to third-party tools for analyzing pages and makes it easy to see if you have new notifications."
msgstr "La barre de menu d’administration de %1$s contient des liens utiles vers des outils tiers pour l’analyse de vos pages et facilite l’affichage de nouvelles notifications."

#: admin/views/class-yoast-feature-toggles.php:147 js/dist/new-settings.js:38
#: js/dist/new-settings.js:312 js/dist/new-settings.js:314
msgid "Admin bar menu"
msgstr "Menu de la barre d’administration"

#: admin/pages/network.php:19 admin/views/tabs/network/features.php:22
msgid "Features"
msgstr "Fonctionnalités"

#: admin/metabox/class-metabox.php:175 js/dist/externals/analysis.js:107
#: js/dist/externals/analysis.js:207
#: js/dist/externals/replacementVariableEditor.js:27
#: js/dist/externals/searchMetadataPreviews.js:62 js/dist/new-settings.js:31
#: js/dist/new-settings.js:35 js/dist/new-settings.js:38
#: js/dist/new-settings.js:42 js/dist/new-settings.js:68
#: js/dist/new-settings.js:82 js/dist/new-settings.js:112
#: js/dist/new-settings.js:141 js/dist/new-settings.js:206
#: js/dist/new-settings.js:223 js/dist/new-settings.js:232
#: js/dist/new-settings.js:254
msgid "SEO title"
msgstr "Titre SEO"

#: inc/options/class-wpseo-option-titles.php:989
msgid "Greater than sign"
msgstr "Signe supérieur à"

#: inc/options/class-wpseo-option-titles.php:985
msgid "Less than sign"
msgstr "Signe inférieur à"

#: inc/options/class-wpseo-option-titles.php:981
msgid "Right angle quotation mark"
msgstr "Guillemet droit à angle droit"

#: inc/options/class-wpseo-option-titles.php:977
msgid "Left angle quotation mark"
msgstr "Guillemet gauche à angle droit"

#: inc/options/class-wpseo-option-titles.php:973
msgid "Small tilde"
msgstr "Petit tilde"

#: inc/options/class-wpseo-option-titles.php:969
msgid "Vertical bar"
msgstr "Barre verticale"

#: inc/options/class-wpseo-option-titles.php:965
msgid "Low asterisk"
msgstr "Astérisque bas"

#: inc/options/class-wpseo-option-titles.php:961
msgid "Asterisk"
msgstr "Astérisque"

#: inc/options/class-wpseo-option-titles.php:957
msgid "Bullet"
msgstr "Puces"

#: inc/options/class-wpseo-option-titles.php:953
msgid "Middle dot"
msgstr "Point au milieu"

#: inc/options/class-wpseo-option-titles.php:945
msgid "Em dash"
msgstr "Tiret Em"

#: inc/options/class-wpseo-option-titles.php:941
msgid "En dash"
msgstr "Tiret En"

#: inc/options/class-wpseo-option-titles.php:937
msgid "Dash"
msgstr "Tiret"

#: admin/metabox/class-metabox.php:186 admin/metabox/class-metabox.php:191
#: js/dist/block-editor.js:285 js/dist/classic-editor.js:270
#: js/dist/elementor.js:393
msgid "No"
msgstr "Non"

#: admin/metabox/class-metabox.php:185 admin/metabox/class-metabox.php:190
#: js/dist/block-editor.js:285 js/dist/classic-editor.js:270
#: js/dist/elementor.js:393
msgid "Yes"
msgstr "Oui"

#: admin/views/tool-bulk-editor.php:63
msgid "Posts list"
msgstr "Liste des publications"

#: admin/views/tool-bulk-editor.php:62
msgid "Posts list navigation"
msgstr "Navigation dans la liste des publications"

#: admin/views/tool-bulk-editor.php:61
msgid "Filter posts list"
msgstr "Filtrer la liste des publications"

#. translators: Hidden accessibility text; %s: post title.
#: admin/class-bulk-editor-list-table.php:843
msgid "Edit &#8220;%s&#8221;"
msgstr "Modifier &#8220;%s&#8221;"

#: src/integrations/admin/menu-badge-integration.php:35 js/dist/academy.js:8
msgid "Premium"
msgstr "Premium"

#: admin/class-admin.php:266
msgid "Get Premium"
msgstr "Passer à la version premium"

#: admin/views/partial-notifications-warnings.php:20
#: inc/class-wpseo-admin-bar-menu.php:400 js/dist/general-page.js:36
msgid "Notifications"
msgstr "Notifications"

#: admin/views/user-profile.php:50
#: src/user-meta/framework/custom-meta/keyword-analysis-disable.php:115
msgid "Removes the focus keyphrase section from the metabox and disables all SEO-related suggestions."
msgstr "Retire la section de l’expression clé principale de la boîte méta et désactive toutes les suggestions liées au SEO."

#: admin/views/user-profile.php:47
#: src/user-meta/framework/custom-meta/keyword-analysis-disable.php:109
msgid "Disable SEO analysis"
msgstr "Désactiver l’analyse SEO"

#: admin/views/js-templates-primary-term.php:28
msgid "Make primary"
msgstr "Rendre principal"

#. translators: Hidden accessibility text; %s: number of notifications.
#: admin/menu/class-admin-menu.php:118 inc/class-wpseo-admin-bar-menu.php:862
#: js/dist/general-page.js:56
msgid "%s notification"
msgid_plural "%s notifications"
msgstr[0] "%s notification"
msgstr[1] "%s notifications"

#: admin/views/user-profile.php:59
#: src/user-meta/framework/custom-meta/content-analysis-disable.php:109
msgid "Disable readability analysis"
msgstr "Désactiver l’analyse de lisibilité"

#: admin/views/user-profile.php:62
#: src/user-meta/framework/custom-meta/content-analysis-disable.php:115
msgid "Removes the readability analysis section from the metabox and disables all readability-related suggestions."
msgstr "Retire l’onglet de l’analyse de lisibilité du gestionnaire de métadonnées et désactive toutes les suggestions liées."

#: admin/views/class-yoast-feature-toggles.php:83
#: js/dist/externals-components.js:219 js/dist/new-settings.js:38
#: js/dist/new-settings.js:312
msgid "Readability analysis"
msgstr "Analyse de lisibilité"

#: admin/statistics/class-statistics-service.php:217
#: inc/class-wpseo-rank.php:140 inc/class-wpseo-rank.php:176
#: inc/class-wpseo-rank.php:208 inc/class-wpseo-rank.php:240
#: js/dist/block-editor.js:540 js/dist/editor-modules.js:315
#: js/dist/elementor.js:94 js/dist/externals-components.js:188
#: js/dist/externals/analysis.js:15 js/dist/externals/dashboardFrontend.js:4
#: js/dist/frontend-inspector-resources.js:1 js/dist/post-edit.js:25
#: js/dist/term-edit.js:1
msgid "Needs improvement"
msgstr "À améliorer"

#: admin/metabox/class-metabox-section-readability.php:30
#: inc/class-wpseo-admin-bar-menu.php:256
msgid "Readability"
msgstr "Lisibilité"

#: admin/views/partial-notifications-errors.php:22 js/dist/general-page.js:36
msgid "Good job! We could detect no serious SEO problems."
msgstr "Bon travail ! Nous n’avons détecté aucun problème sérieux de référencement."

#: admin/views/partial-notifications-errors.php:21 js/dist/general-page.js:36
msgid "We have detected the following issues that affect the SEO of your site."
msgstr "Nous avons détecté les problèmes suivants qui affectent le référencement de votre site."

#: admin/views/partial-notifications-errors.php:20
#: js/dist/editor-modules.js:181 js/dist/externals-components.js:188
#: js/dist/externals/analysisReport.js:39 js/dist/general-page.js:36
msgid "Problems"
msgstr "Problèmes"

#: inc/class-wpseo-rank.php:138 js/dist/block-editor.js:540
#: js/dist/editor-modules.js:315 js/dist/elementor.js:94
#: js/dist/externals-components.js:188
#: js/dist/frontend-inspector-resources.js:1 js/dist/post-edit.js:25
#: js/dist/term-edit.js:1
msgid "Not available"
msgstr "Non disponible"

#. translators: Hidden accessibility text.
#: admin/class-meta-columns.php:269
msgid "Filter by SEO Score"
msgstr "Filtrer par score SEO"

#. translators: Hidden accessibility text.
#: admin/class-meta-columns.php:181
msgid "Meta description not set."
msgstr "La méta description n’est pas définie."

#: admin/menu/class-admin-menu.php:56 js/dist/general-page.js:55
msgid "Dashboard"
msgstr "Tableau de bord"

#. translators: %1$s is a link start tag to the permalink settings page, %2$s
#. is the link closing tag.
#: src/services/health-check/postname-permalink-reports.php:71
msgid "You can fix this on the %1$sPermalink settings page%2$s."
msgstr "Vous pouvez corriger cela sur la %1$spage des réglages des permaliens%2$s."

#: inc/class-wpseo-replace-vars.php:1480
msgid "Replaced with the primary category of the post/page"
msgstr "Remplacé par la catégorie principale de l’article/page"

#. translators: %1$s expands to Yoast SEO
#: admin/class-bulk-title-editor-list-table.php:50
msgid "New %1$s Title"
msgstr "Nouveau titre %1$s"

#. translators: %1$s expands to Yoast SEO
#: admin/class-bulk-title-editor-list-table.php:48
msgid "Existing %1$s Title"
msgstr "Titre %1$s actuel"

#: inc/sitemaps/class-sitemaps-cache-validator.php:301
msgid "Expected an integer as input."
msgstr "L’entrée doit être un entier."

#: inc/sitemaps/class-sitemaps-cache-validator.php:111
msgid "Trying to build the sitemap cache key, but the postfix and prefix combination leaves too little room to do this. You are probably requesting a page that is way out of the expected range."
msgstr "Tentative de construction d’une clé de cache sécurisée pour le plan de site, mais la combinaison du suffixe et du préfixe laisse trop peu de place pour le faire. Vous demandez probablement une page qui est bien loin de la plage attendue."

#: admin/views/redirects.php:32
#: src/integrations/admin/redirects-page-integration.php:48
msgid "Redirects"
msgstr "Redirections"

#: src/integrations/admin/crawl-settings-integration.php:242
#: js/dist/externals/relatedKeyphraseSuggestions.js:1
msgid "Remove"
msgstr "Supprimer"

#: src/integrations/admin/crawl-settings-integration.php:241
msgid "Keep"
msgstr "Conserver"

#. translators: %s is the taxonomy title. This will be shown to screenreaders
#: admin/views/js-templates-primary-term.php:38
msgid "Primary %s"
msgstr "%s principal"

#: admin/views/js-templates-primary-term.php:32
msgid "Primary"
msgstr "Principal"

#. translators: Hidden accessibility text; %1$s expands to the term title, %2$s
#. to the taxonomy title.
#: admin/views/js-templates-primary-term.php:18
msgid "Make %1$s primary %2$s"
msgstr "Faire de %1$s le %2$s principal"

#: admin/pages/network.php:20 admin/views/tabs/network/integrations.php:22
#: src/integrations/admin/integrations-page.php:131
#: js/dist/integrations-page.js:63
msgid "Integrations"
msgstr "Intégrations"

#: admin/taxonomy/class-taxonomy-columns.php:170
msgid "Term is set to noindex."
msgstr "Le terme est réglé sur noindex."

#: src/integrations/admin/crawl-settings-integration.php:195
#: src/presenters/admin/light-switch-presenter.php:120
msgid "Enabled"
msgstr "Activé"

#: src/integrations/admin/crawl-settings-integration.php:194
#: src/presenters/admin/light-switch-presenter.php:120
#: js/dist/externals/dashboardFrontend.js:4
msgid "Disabled"
msgstr "Désactivé"

#. translators: %s: wp_title() function.
#: inc/class-wpseo-replace-vars.php:1462
msgid "The separator defined in your theme's %s tag."
msgstr "Le séparateur défini dans la balise %s de votre thème."

#: inc/class-wpseo-rank.php:139
msgid "No index"
msgstr "No index"

#: admin/class-meta-columns.php:114 admin/class-meta-columns.php:116
#: admin/taxonomy/class-taxonomy-columns.php:87
#: admin/taxonomy/class-taxonomy-columns.php:88
#: inc/class-wpseo-admin-bar-menu.php:244
#: js/dist/externals/dashboardFrontend.js:4
msgid "SEO score"
msgstr "Score SEO"

#. Author of the plugin
#: wp-seo.php
msgid "Team Yoast"
msgstr "L’équipe Yoast"

#. Description of the plugin
#: wp-seo.php
msgid "The first true all-in-one SEO solution for WordPress, including on-page content analysis, XML sitemaps and much more."
msgstr "La première solution SEO tout-en-un pour WordPress, y compris l’analyse des pages de contenu, les plans de site XML et bien plus encore."

#. Plugin Name of the plugin
#: wp-seo.php admin/capabilities/class-capability-manager-integration.php:74
#: src/presenters/meta-description-presenter.php:36 js/dist/block-editor.js:609
msgid "Yoast SEO"
msgstr "Yoast SEO"

#. translators: %1$s expands to Yoast SEO, %2$s / %3$s: links to the
#. installation manual in the Readme for the Yoast SEO code repository on
#. GitHub
#: wp-seo-main.php:524
msgid "The %1$s plugin installation is incomplete. Please refer to %2$sinstallation instructions%3$s."
msgstr "L’installation de l’extension %1$s est incomplète. Veuillez vous référer %2$saux informations d’installation%3$s."

#: wp-seo-main.php:500
msgid "The Standard PHP Library (SPL) extension seem to be unavailable. Please ask your web host to enable it."
msgstr "Les extensions de la Librairie Standard PHP (SPL) semblent ne pas être disponibles.Demandez à votre hébergeur de les activer."

#: inc/class-wpseo-admin-bar-menu.php:636
#: inc/class-wpseo-admin-bar-menu.php:684
msgid "SEO Settings"
msgstr "Réglages SEO"

#: inc/class-wpseo-admin-bar-menu.php:454
msgid "Google Page Speed Test"
msgstr "Test de vitesse de page Google"

#: inc/class-wpseo-admin-bar-menu.php:449
msgid "Facebook Debugger"
msgstr "Facebook Debugger"

#: inc/class-wpseo-admin-bar-menu.php:430
msgid "Analyze this page"
msgstr "Analyser cette page"

#. translators: %s expands to the name of a post type (plural).
#: inc/class-upgrade.php:1555 inc/options/class-wpseo-option-titles.php:309
msgid "%s Archive"
msgstr "%s Archive"

#. translators: %s expands to the search phrase.
#: inc/options/class-wpseo-option-titles.php:274
msgid "You searched for %s"
msgstr "Vous avez cherché %s"

#. translators: 1: link to post; 2: link to blog.
#: inc/options/class-wpseo-option-titles.php:277
msgid "The post %1$s appeared first on %2$s."
msgstr "L’article %1$s est apparu en premier sur %2$s."

#: inc/options/class-wpseo-option-ms.php:243
msgid "No numeric value was received."
msgstr "Les chiffres ne sont pas acceptés"

#. translators: %s is the ID number of a blog.
#: inc/options/class-wpseo-option-ms.php:231
msgid "This must be an existing blog. Blog %s does not exist or has been marked as deleted."
msgstr "Blog inexistant. Le blog %s n’existe pas ou a été marqué comme étant supprimé."

#: inc/options/class-wpseo-option-ms.php:227
#: inc/options/class-wpseo-option-ms.php:243
msgid "The default blog setting must be the numeric blog id of the blog you want to use as default."
msgstr "Le réglage par défaut du blog doit être l’identifiant numérique du blog que vous souhaitez utiliser par défaut."

#. translators: %1$s expands to the option name and %2$sexpands to Yoast SEO
#: inc/options/class-wpseo-option-ms.php:208
msgid "%1$s is not a valid choice for who should be allowed access to the %2$s settings. Value reset to the default."
msgstr "%1$s n’est pas un choix valide pour ceux qui devraient être autorisés d’accéder aux réglages de %2$s. La valeur par défaut a été remise."

#. translators: %s expands to a taxonomy slug.
#: inc/options/class-wpseo-option-titles.php:583
msgid "Please select a valid post type for taxonomy \"%s\""
msgstr "Veuillez choisir un type de publication valide pour la taxonomie « %s »"

#. translators: %s expands to a post type.
#: inc/options/class-wpseo-option-titles.php:545
msgid "Please select a valid taxonomy for post type \"%s\""
msgstr "Veuillez choisir une taxonomie valide pour le type de publication « %s »"

#: inc/options/class-wpseo-option-titles.php:282
msgid "You searched for"
msgstr "Vous avez cherché"

#: inc/options/class-wpseo-option-titles.php:281
msgid "Home"
msgstr "Accueil"

#: inc/options/class-wpseo-option-titles.php:280
msgid "Archives for"
msgstr "Archives pour"

#: inc/options/class-wpseo-option-titles.php:279
msgid "Error 404: Page not found"
msgstr "Erreur 404 : Page introuvable"

#: admin/statistics/class-statistics-service.php:227
#: inc/class-wpseo-rank.php:142 inc/class-wpseo-rank.php:186
#: inc/class-wpseo-rank.php:218 inc/class-wpseo-rank.php:250
#: js/dist/block-editor.js:540 js/dist/editor-modules.js:315
#: js/dist/elementor.js:94 js/dist/externals-components.js:188
#: js/dist/externals/analysis.js:15 js/dist/externals/dashboardFrontend.js:4
#: js/dist/frontend-inspector-resources.js:1 js/dist/post-edit.js:25
#: js/dist/term-edit.js:1
msgid "Good"
msgstr "Bon"

#: inc/class-wpseo-replace-vars.php:1528
msgid "Replaced with a custom taxonomies description"
msgstr "Remplacé par une description des taxonomies personnalisées."

#: inc/class-wpseo-replace-vars.php:1527
msgid "Replaced with a posts custom taxonomies, comma separated."
msgstr "Remplacé par les catégories de la publication, séparées par des virgules."

#: inc/class-wpseo-replace-vars.php:1526
msgid "Replaced with a posts custom field value"
msgstr "Remplacé par une valeur de champ personnalisé"

#: inc/class-wpseo-replace-vars.php:1525
msgid "Replaced with the slug which caused the 404"
msgstr "Remplacé par le slug qui a causé l’erreur 404"

#: inc/class-wpseo-replace-vars.php:1524
msgid "Replaced with the posts focus keyphrase"
msgstr "Remplacé par l’expression clé principale de la publication."

#: inc/class-wpseo-replace-vars.php:1523
msgid "Attachment caption"
msgstr "Légende de la pièce-jointe"

#: inc/class-wpseo-replace-vars.php:1522
msgid "Replaced with the current page number"
msgstr "Remplacé par le numéro de la page en cours"

#: inc/class-wpseo-replace-vars.php:1521
msgid "Replaced with the current page total"
msgstr "Remplacé par le nombre total de pages"

#: inc/class-wpseo-replace-vars.php:1520
msgid "Replaced with the current page number with context (i.e. page 2 of 4)"
msgstr "Remplacé par le numéro de page en cours avec le contexte (P. ex. : page 2 sur 4)"

#: inc/class-wpseo-replace-vars.php:1489
msgid "Replaced with the current year"
msgstr "Remplacé par l’année en cours"

#: inc/class-wpseo-replace-vars.php:1490
msgid "Replaced with the current month"
msgstr "Remplacé par le mois en cours"

#: inc/class-wpseo-replace-vars.php:1491
msgid "Replaced with the current day"
msgstr "Remplacé par la date actuelle"

#: inc/class-wpseo-replace-vars.php:1488
msgid "Replaced with the current date"
msgstr "Remplacé par la date actuelle"

#: inc/class-wpseo-replace-vars.php:1519
msgid "Replaced with the post/page author's 'Biographical Info'"
msgstr "Remplacé par « les informations biographiques » de l’auteur ou de l’autrice de la publication"

#: inc/class-wpseo-replace-vars.php:1518
msgid "Replaced with the post/page author's 'nicename'"
msgstr "Remplacé par l’identifiant normalisé de l’auteur ou de l’autrice de la publication"

#: inc/class-wpseo-replace-vars.php:1517
msgid "Replaced with the post/page ID"
msgstr "Remplacé par l’ID de la publication"

#: inc/class-wpseo-replace-vars.php:1516
msgid "Replaced with the post/page modified time"
msgstr "Remplacé par l’heure de modification de la publication"

#: inc/class-wpseo-replace-vars.php:1515
msgid "Replaced with the content type plural label"
msgstr "Remplacé par l’intitulé au pluriel du type de contenu"

#: inc/class-wpseo-replace-vars.php:1514
msgid "Replaced with the content type single label"
msgstr "Remplacé par l’intitulé au singulier du type de contenu"

#: inc/class-wpseo-replace-vars.php:1485
msgid "Replaced with the current search phrase"
msgstr "Remplacé par la phrase recherchée"

#: inc/class-wpseo-replace-vars.php:1484
msgid "Replaced with the term name"
msgstr "Remplacé par le nom du terme"

#: inc/class-wpseo-replace-vars.php:1483
msgid "Replaced with the term description"
msgstr "Remplacé par la description du terme"

#: inc/class-wpseo-replace-vars.php:1482
msgid "Replaced with the tag description"
msgstr "Remplacé par la description du mot-clé"

#: inc/class-wpseo-replace-vars.php:1481
msgid "Replaced with the category description"
msgstr "Remplacé par la description de la catégorie"

#: inc/class-wpseo-replace-vars.php:1479
msgid "Replaced with the post categories (comma separated)"
msgstr "Remplacé par les catégories de l’article (séparées par des virgules)"

#: inc/class-wpseo-replace-vars.php:1478
msgid "Replaced with the current tag/tags"
msgstr "Remplacé par le(s) étiquette(s)"

#: inc/class-wpseo-replace-vars.php:1477
msgid "Replaced with the post/page excerpt (without auto-generation)"
msgstr "Remplacé par l’extrait de la publication (sans auto-génération)"

#: inc/class-wpseo-replace-vars.php:1476
msgid "Replaced with the post/page excerpt (or auto-generated if it does not exist)"
msgstr "Remplacé par l’extrait de la publication (ou auto-généré, si l’extrait n’existe pas)"

#: inc/class-wpseo-replace-vars.php:1474
msgid "The site's name"
msgstr "Le nom du site"

#: inc/class-wpseo-replace-vars.php:1472
msgid "Replaced with the title of the parent page of the current page"
msgstr "Remplacé par le titre de la page parent de la présente page"

#: inc/class-wpseo-replace-vars.php:1471
msgid "Replaced with the title of the post/page"
msgstr "Remplacé par le titre de la publication"

#: inc/class-wpseo-replace-vars.php:1470
msgid "Replaced with the date of the post/page"
msgstr "Remplacé par la date de la publication"

#. translators: 1: current page number, 2: total number of pages.
#: inc/class-wpseo-replace-vars.php:1029
msgid "Page %1$d of %2$d"
msgstr "Page %1$d sur %2$d"

#: inc/class-wpseo-replace-vars.php:124
msgid "You cannot overrule a WPSEO standard variable replacement by registering a variable with the same name. Use the \"wpseo_replacements\" filter instead to adjust the replacement value."
msgstr "Vous ne pouvez pas annuler une variable de remplacement standard de WPSEO en enregistrant une variable avec le même nom. Utilisez le filtre « wpseo_replacements » au lieu d’ajuster la valeur de remplacement."

#: inc/class-wpseo-replace-vars.php:120
msgid "A replacement variable with the same name has already been registered. Try making your variable name unique."
msgstr "Une variable de remplacement portant le même nom est déjà enregistrée. Essayez d’utiliser un nom de variable unique."

#: inc/class-wpseo-replace-vars.php:110
msgid "A replacement variable can not start with \"%%cf_\" or \"%%ct_\" as these are reserved for the WPSEO standard variable variables for custom fields and custom taxonomies. Try making your variable name unique."
msgstr "Une variable de remplacement ne peut commencer par \"%%cf_\" ou \"%%ct_\" car ces préfixes sont réservés aux variables standards de WPSEO pour les champs et les taxonomies personnalisés. Essayez d’utiliser un nom de variable unique."

#: inc/class-wpseo-replace-vars.php:107
msgid "A replacement variable can only contain alphanumeric characters, an underscore or a dash. Try renaming your variable."
msgstr "Une variable de remplacement ne peut contenir que des caractères alpha-numériques, un souligné ou un tiret. Veuillez renommer votre variable"

#. translators: %1$s resolves to Yoast SEO, %2$s resolves to the Settings
#. submenu item.
#: src/presenters/meta-description-presenter.php:35
msgid "Admin only notice: this page does not show a meta description because it does not have one, either write it for this page specifically or go into the [%1$s - %2$s] menu and set up a template."
msgstr "Avis aux administrateurs et administratrices : cette page n’affiche pas de méta description car elle n’en a pas. Vous pouvez donc soit l’ajouter spécifiquement pour cette page soit vous rendre dans vos réglages (%1$s - %2$s) pour configurer un modèle."

#: inc/options/class-wpseo-option-titles.php:275
msgid "Page not found"
msgstr "Page non trouvée"

#. translators: %s expands to the variable used for term title.
#: inc/class-upgrade.php:1558 inc/options/class-wpseo-option-titles.php:345
#: src/editors/framework/seo/terms/title-data-provider.php:27
msgid "%s Archives"
msgstr "Archives des %s"

#: admin/views/user-profile.php:30
#: src/user-meta/framework/custom-meta/author-metadesc.php:97
msgid "Meta description to use for Author page"
msgstr "Méta description à utiliser pour la page Auteur/autrice"

#: admin/views/user-profile.php:26
#: src/user-meta/framework/custom-meta/author-title.php:97
msgid "Title to use for Author page"
msgstr "Titre SEO à utiliser pour la page auteur/autrice"

#. translators: %1$s expands to Yoast SEO
#: admin/views/user-profile.php:13
#: src/user-meta/user-interface/custom-meta-integration.php:100
msgid "%1$s settings"
msgstr "Réglages de %1$s"

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/tool/wpseo-export.php:15
msgid "Export your %1$s settings"
msgstr "Exporter vos réglages de %1$s"

#: admin/class-export.php:65 admin/views/tabs/tool/wpseo-import.php:25
#: admin/views/tabs/tool/wpseo-import.php:45
#: admin/views/tool-import-export.php:86
msgid "Import settings"
msgstr "Importer les réglages"

#: admin/views/tabs/tool/import-seo.php:18
#: admin/views/tabs/tool/import-seo.php:49
#: admin/views/tool-import-export.php:92
msgid "Import from other SEO plugins"
msgstr "Importer à partir d’autres extensions de SEO"

#: admin/views/tabs/tool/import-seo.php:88
#: admin/views/tool-import-export.php:24
msgid "Import"
msgstr "Importer"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:135 admin/views/tool-file-editor.php:235
msgid "If you had a %s file and it was editable, you could edit it from here."
msgstr "Si vous aviez un %s et qu’il était modifiable, vous pourriez le modifier à partir d’ici."

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:153 admin/views/tool-file-editor.php:203
msgid "If your %s were writable, you could edit it from here."
msgstr "Si votre %s était accessible en écriture, vous pourriez le modifier à partir d’ici."

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:116
msgid "You don't have a %s file, create one here:"
msgstr "Vous n’avez pas de fichier %s, créez-en un ici :"

#: admin/statistics/class-statistics-service.php:80
msgid "Below are your published posts' SEO scores. Now is as good a time as any to start improving some of your posts!"
msgstr "Retrouvez ci-dessous les scores SEO de vos articles publiés. C’est le moment de commencer à améliorer certains d’entre eux !"

#: admin/views/tabs/dashboard/dashboard.php:40
msgid "Credits"
msgstr "Crédits"

#: admin/pages/tools.php:77
msgid "&laquo; Back to Tools page"
msgstr "&laquo; Retour à la page Outils"

#. translators: %1$s expands to Yoast SEO
#: admin/pages/tools.php:49
msgid "%1$s comes with some very powerful built-in tools:"
msgstr "%1$s propose des outils intégrés très puissants :"

#: admin/pages/tools.php:37
msgid "This tool allows you to quickly change important files for your SEO, like your robots.txt and, if you have one, your .htaccess file."
msgstr "Cet outil vous permet de modifier rapidement des fichiers importants pour votre référencement, comme le fichier robots.txt ou le fichier .htaccess si vous en avez un."

#: admin/pages/tools.php:36
msgid "File editor"
msgstr "Éditeur de fichiers"

#: admin/pages/tools.php:31
msgid "Import settings from other SEO plugins and export your settings for re-use on (another) site."
msgstr "Importez les réglages d’autres extensions de référencement et exportez les vôtres pour les réutiliser sur un autre site."

#: admin/pages/tools.php:30
msgid "Import and Export"
msgstr "Importation et exportation"

#: admin/pages/tools.php:43
msgid "This tool allows you to quickly change titles and descriptions of your posts and pages without having to go into the editor for each page."
msgstr "Cet outil vous permet de modifier rapidement les titres et les descriptions de vos articles et pages, sans avoir à aller dans l’éditeur de chaque publication."

#: admin/pages/tools.php:42
msgid "Bulk editor"
msgstr "Éditeur par lot"

#: src/integrations/admin/import-integration.php:119
msgid "Default settings"
msgstr "Réglages par défaut"

#: admin/views/tool-bulk-editor.php:113 js/dist/new-settings.js:256
msgid "Description"
msgstr "Description"

#: admin/views/tabs/network/restore-site.php:32
msgid "Restore site to defaults"
msgstr "Restaurer les réglages du site aux valeurs par défaut"

#: admin/views/tabs/network/restore-site.php:23
#: admin/views/tabs/network/restore-site.php:28
msgid "Site ID"
msgstr "ID du site"

#: admin/views/tabs/network/restore-site.php:16
msgid "Using this form you can reset a site to the default SEO settings."
msgstr "Grâce à ce formulaire, vous pouvez réinitialiser les réglages SEO de votre site aux réglages par défaut."

#: admin/views/tabs/network/general.php:54
msgid "Privacy sensitive (FB admins and such), theme specific (title rewrite) and a few very site specific settings will not be imported to new sites."
msgstr "Les réglages confidentiels (administrateurs Facebook etc.), spécifiques aux thèmes (réécriture des titres) et quelques réglages très spécifiques au site actuel ne seront pas importés vers les nouveaux sites."

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/network/general.php:47
msgid "Enter the %1$sSite ID%2$s for the site whose settings you want to use as default for all sites that are added to your network. Leave empty for none (i.e. the normal plugin defaults will be used)."
msgstr "Saisissez l’%1$sID du site%2$s que vous utiliserez comme base pour les réglages de tous les nouveaux sites ajoutés à votre réseau. Laissez vide pour n’en choisir aucun. (Les réglages par défaut de l’extension seront alors utilisés)."

#: admin/views/tabs/network/general.php:40
msgid "Choose the site whose settings you want to use as default for all sites that are added to your network. If you choose 'None', the normal plugin defaults will be used."
msgstr "Choisissez le site dont les réglages serviront de base à tous les nouveaux sites de votre réseau. Si vous choisissez « Aucun », les réglages par défaut seront utilisés."

#: admin/views/tabs/network/general.php:37
#: admin/views/tabs/network/general.php:43
msgid "New sites in the network inherit their SEO settings from this site"
msgstr "Les nouveaux sites du réseau héritent des réglages SEO de ce site"

#: admin/views/tabs/network/general.php:28
msgid "Super Admins only"
msgstr "Super-admins uniquement"

#: admin/views/tabs/network/general.php:27
msgid "Site Admins (default)"
msgstr "Administrateurs et administratrices du site (par défaut)"

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/network/general.php:25
msgid "Who should have access to the %1$s settings"
msgstr "Qui devrait avoir accès aux réglages de %1$s"

#: admin/class-yoast-network-admin.php:80
msgid "spam"
msgstr "indésirable"

#: admin/class-yoast-network-admin.php:79
msgid "mature"
msgstr "adulte"

#: admin/class-yoast-network-admin.php:78
msgid "archived"
msgstr "archivé"

#: admin/class-yoast-network-admin.php:77
msgid "public"
msgstr "public"

#. translators: %s expands to the name of a site within a multisite network.
#: admin/class-yoast-network-admin.php:174
msgid "%s restored to default SEO settings."
msgstr "%s a été rétabli aux réglages SEO par défaut."

#: admin/class-yoast-network-admin.php:140
msgid "Settings Updated."
msgstr "Les réglages ont été mis à jour."

#: admin/views/tool-bulk-editor.php:111 inc/class-wpseo-replace-vars.php:1471
#: js/dist/elementor.js:540 js/dist/externals-redux.js:1
msgid "Title"
msgstr "Titre"

#: admin/views/tabs/network/general.php:54
msgid "Take note:"
msgstr "À savoir :"

#. translators: %1$s expands to Yoast SEO
#: admin/class-plugin-availability.php:89
msgid "Seamlessly integrate WooCommerce with %1$s and get extra features!"
msgstr "Intégrez WooCommerce et %1$s et obtenez des fonctionnalités supplémentaires !"

#: admin/class-plugin-availability.php:77
msgid "Rank better locally and in Google Maps, without breaking a sweat!"
msgstr "Améliorez votre classement localement dans Google Maps, sans verser une goutte de sueur !"

#: admin/class-plugin-availability.php:67
msgid "Are you in Google News? Increase your traffic from Google News by optimizing for it!"
msgstr "Êtes-vous sur Google Actualités ? Augmentez votre trafic de Google News grâce à son optimisation !"

#: admin/class-plugin-availability.php:57
msgid "Optimize your videos to show them off in search results and get more clicks!"
msgstr "Optimisez vos vidéos pour qu’elles apparaissent dans les résultats des recherches et obtenir plus de clics !"

#. translators: %1$s expands to Yoast SEO
#: admin/class-plugin-availability.php:45
msgid "The premium version of %1$s with more features & support."
msgstr "La version Premium de %1$s avec plus de fonctionnalités & de support."

#: src/integrations/admin/first-time-configuration-integration.php:485
#: js/dist/new-settings.js:324
msgid "Person"
msgstr "Personne"

#: inc/class-wpseo-replace-vars.php:1497
msgid "Permalink"
msgstr "Permalien"

#. Translators: %1$s: expands to 'Yoast SEO Premium', %2$s: links to Yoast SEO
#. Premium plugin page.
#: admin/google_search_console/views/gsc-redirect-nopremium.php:22
msgid "To be able to create a redirect and fix this issue, you need %1$s. You can buy the plugin, including one year of support and updates, on %2$s."
msgstr "Pour pouvoir créer une redirection et régler ce problème, vous avez besoin de %1$s. Vous pouvez acheter l&rsquo;extension, incluant un an de support et de mises à jour, sur %2$s."

#. Translators: %s: expands to Yoast SEO Premium
#: admin/google_search_console/views/gsc-redirect-nopremium.php:15
msgid "Creating redirects is a %s feature"
msgstr "La création de redirection est une fonctionnalité de %s."

#: admin/views/redirects.php:175 admin/views/redirects.php:222
msgid "New URL"
msgstr "Nouvelle URL"

#: admin/views/redirects.php:112
msgid "URL"
msgstr "URL"

#. translators: %s: 'Facebook' plugin name of possibly conflicting plugin
#: admin/class-yoast-plugin-conflict.php:186
msgid "Deactivate %s"
msgstr "Désactiver %s"

#. translators: %1$s: 'Facebook & Open Graph' plugin name(s) of possibly
#. conflicting plugin(s), %2$s to Yoast SEO
#: admin/class-yoast-plugin-conflict.php:182
msgid "The %1$s plugin might cause issues when used in conjunction with %2$s."
msgstr "L’extension %1$s pourrait provoquer des problèmes si utiliser en conjonction avec %2$s."

#. translators: %s is the plugin name
#: admin/class-yoast-dashboard-widget.php:79
msgid "%s Posts Overview"
msgstr "Vue d’ensemble des publications de %s"

#: admin/google_search_console/views/gsc-redirect-nopremium.php:27
#: js/dist/block-editor.js:19 js/dist/block-editor.js:23
#: js/dist/classic-editor.js:4 js/dist/classic-editor.js:8
#: js/dist/elementor.js:4 js/dist/elementor.js:8
#: js/dist/externals-components.js:191 js/dist/externals-components.js:195
#: js/dist/externals-components.js:256 js/dist/externals/componentsNew.js:790
#: js/dist/general-page.js:6 js/dist/general-page.js:10
#: js/dist/general-page.js:22 js/dist/general-page.js:23
#: js/dist/general-page.js:36 js/dist/integrations-page.js:40
#: js/dist/integrations-page.js:44 js/dist/integrations-page.js:56
#: js/dist/integrations-page.js:57 js/dist/introductions.js:4
#: js/dist/new-settings.js:6 js/dist/new-settings.js:10
#: js/dist/new-settings.js:22 js/dist/new-settings.js:45 js/dist/post-edit.js:4
#: js/dist/post-edit.js:8 js/dist/support.js:6 js/dist/support.js:10
#: js/dist/term-edit.js:4 js/dist/term-edit.js:8
msgid "Close"
msgstr "Fermer"

#. translators: %1$s expands to Yoast SEO, %2$s: 'Google XML Sitemaps' plugin
#. name of possibly conflicting plugin with regard to the creation of sitemaps.
#: admin/class-plugin-conflict.php:78
msgid "Both %1$s and %2$s can create XML sitemaps. Having two XML sitemaps is not beneficial for search engines and might slow down your site."
msgstr "%1$s et %2$s peuvent tous les deux créer des plans de site XML. En avoir deux n’est pas recommandé pour les moteurs de recherche et pourrait même ralentir votre site."

#. translators: %1$s expands to Yoast SEO.
#: admin/class-plugin-conflict.php:71
msgid "Configure %1$s's Open Graph settings"
msgstr "Configurer les réglages de l’OpenGraph de %1$s"

#. translators: %1$s expands to Yoast SEO, %2$s: 'Facebook' plugin name of
#. possibly conflicting plugin with regard to creating OpenGraph output.
#: admin/class-plugin-conflict.php:67
msgid "Both %1$s and %2$s create Open Graph output, which might make Facebook, X, LinkedIn and other social networks use the wrong texts and images when your pages are being shared."
msgstr "%1$s et %2$s créent des métadonnées OpenGraph, ce qui pourrait faire que Facebook, X, LinkedIn et d’autres réseaux sociaux utilisent de mauvais textes et de mauvaises images lorsque vos pages sont partagées."

#: admin/statistics/class-statistics-service.php:222
#: inc/class-wpseo-rank.php:141 inc/class-wpseo-rank.php:181
#: inc/class-wpseo-rank.php:213 js/dist/block-editor.js:540
#: js/dist/editor-modules.js:315 js/dist/elementor.js:94
#: js/dist/externals-components.js:188 js/dist/externals/analysis.js:15
#: js/dist/externals/dashboardFrontend.js:4
#: js/dist/frontend-inspector-resources.js:1 js/dist/post-edit.js:25
#: js/dist/term-edit.js:1
msgid "OK"
msgstr "OK"

#: admin/class-meta-columns.php:129
msgid "Meta Desc."
msgstr "Méta Desc."

#: admin/class-meta-columns.php:273
msgid "All SEO Scores"
msgstr "Tous les scores SEO"

#: admin/class-meta-columns.php:828
msgid "Post is set to noindex."
msgstr "La publication est réglée sur noindex."

#: admin/metabox/class-metabox.php:212
msgid "The URL that this page should redirect to."
msgstr "L’URL vers laquelle cette page devrait rediriger."

#: admin/metabox/class-metabox.php:211
msgid "301 Redirect"
msgstr "Redirection 301"

#. translators: 1: link open tag; 2: link close tag.
#: admin/metabox/class-metabox.php:206
msgid "The canonical URL that this page should point to. Leave empty to default to permalink. %1$sCross domain canonical%2$s supported too."
msgstr "L’URL canonique vers laquelle cette page devrait pointer. Laissez ce champ vide pour utiliser le slug par défaut. Les %1$sURL canoniques vers d’autres domaines%2$s sont aussi prises en charge."

#: admin/metabox/class-metabox.php:202 js/dist/block-editor.js:291
#: js/dist/classic-editor.js:276 js/dist/elementor.js:399
msgid "Canonical URL"
msgstr "URL canonique"

#: admin/metabox/class-metabox.php:200
msgid "Title to use for this page in breadcrumb paths"
msgstr "Titre à utiliser pour cette page dans le fil d’Ariane"

#: admin/metabox/class-metabox.php:199 js/dist/block-editor.js:290
#: js/dist/classic-editor.js:275 js/dist/elementor.js:398
msgid "Breadcrumbs Title"
msgstr "Titre pour le fil d’Ariane"

#: admin/metabox/class-metabox.php:197 js/dist/block-editor.js:289
#: js/dist/classic-editor.js:274 js/dist/elementor.js:397
msgid "No Snippet"
msgstr "Pas de métadonnées"

#: admin/metabox/class-metabox.php:196 js/dist/block-editor.js:289
#: js/dist/classic-editor.js:274 js/dist/elementor.js:397
msgid "No Archive"
msgstr "Aucune archive"

#: admin/metabox/class-metabox.php:195 js/dist/block-editor.js:289
#: js/dist/classic-editor.js:274 js/dist/elementor.js:397
msgid "No Image Index"
msgstr "Pas d’index pour l’image"

#: admin/class-yoast-network-admin.php:43 src/config/schema-types.php:163
#: src/integrations/settings-integration.php:581 js/dist/new-settings.js:23
#: js/dist/new-settings.js:356
msgid "None"
msgstr "Aucun"

#: admin/metabox/class-metabox.php:193 js/dist/block-editor.js:289
#: js/dist/classic-editor.js:274 js/dist/elementor.js:397
msgid "Meta robots advanced"
msgstr "Méta robots avancés"

#: admin/metabox/class-metabox.php:181
msgid "Warning: even though you can set the meta robots setting here, the entire site is set to noindex in the sitewide privacy settings, so these settings won't have an effect."
msgstr "Avertissement : même si vous pouvez modifier les méta « robots » ici, tout votre site est réglé en mode « noindex » dans les réglages de vie privée, toute modification effectuée ici n’aura donc aucun effet."

#: admin/metabox/class-metabox.php:176
#: js/dist/externals/replacementVariableEditor.js:27
#: js/dist/externals/searchMetadataPreviews.js:62 js/dist/new-settings.js:31
#: js/dist/new-settings.js:35 js/dist/new-settings.js:38
#: js/dist/new-settings.js:42 js/dist/new-settings.js:68
#: js/dist/new-settings.js:82 js/dist/new-settings.js:112
#: js/dist/new-settings.js:141 js/dist/new-settings.js:206
#: js/dist/new-settings.js:223 js/dist/new-settings.js:232
#: js/dist/new-settings.js:254
msgid "Meta description"
msgstr "Méta description"

#: admin/class-meta-columns.php:128
msgid "SEO Title"
msgstr "Titre SEO"

#: inc/class-wpseo-replace-vars.php:1524
msgid "Focus keyword"
msgstr "Mot-clé principal"

#: admin/import/class-import-settings.php:121
msgid "Settings successfully imported."
msgstr "Les réglages ont été importés."

#: admin/import/class-import-settings.php:85
msgid "Settings could not be imported:"
msgstr "Les réglagles n’ont pas été importés :"

#: admin/class-bulk-editor-list-table.php:1045
msgid "Action"
msgstr "Action"

#: admin/class-bulk-editor-list-table.php:1040
msgid "Page URL/Slug"
msgstr "Slug de la page"

#: admin/class-bulk-editor-list-table.php:1039
msgid "Publication date"
msgstr "Date de Publication"

#: admin/class-bulk-editor-list-table.php:1038
msgid "Post Status"
msgstr "État de la publication"

#: admin/class-bulk-editor-list-table.php:1036
msgid "WP Page Title"
msgstr "Titre de la page"

#: admin/class-bulk-editor-list-table.php:866 js/dist/block-editor.js:149
#: js/dist/classic-editor.js:134 js/dist/editor-modules.js:270
#: js/dist/elementor.js:489 js/dist/externals/dashboardFrontend.js:5
#: js/dist/wincher-dashboard-widget.js:43
#: js/dist/wincher-dashboard-widget.js:112
msgid "View"
msgstr "Voir"

#. translators: Hidden accessibility text; %s: post title.
#: admin/class-bulk-editor-list-table.php:865
msgid "View &#8220;%s&#8221;"
msgstr "Voir &#8220;%s&#8221;"

#: admin/class-bulk-editor-list-table.php:856
msgid "Preview"
msgstr "Prévisualisation"

#. translators: Hidden accessibility text; %s: post title.
#: admin/class-bulk-editor-list-table.php:855
msgid "Preview &#8220;%s&#8221;"
msgstr "Prévisualiser &#8220;%s&#8221;"

#: admin/class-bulk-editor-list-table.php:844
#: js/dist/externals/dashboardFrontend.js:4 js/dist/general-page.js:48
#: js/dist/general-page.js:55
msgid "Edit"
msgstr "Modifier"

#: admin/class-bulk-editor-list-table.php:438 admin/views/redirects.php:141
msgid "Filter"
msgstr "Filtrer"

#. translators: %s expands to the number of trashed posts in localized format.
#: admin/class-bulk-editor-list-table.php:357
msgctxt "posts"
msgid "Trash <span class=\"count\">(%s)</span>"
msgid_plural "Trash <span class=\"count\">(%s)</span>"
msgstr[0] "Corbeille <span class=\"count\">(%s)</span>"
msgstr[1] "Corbeille <span class=\"count\">(%s)</span>"

#. translators: %s expands to the number of posts in localized format.
#: admin/class-bulk-editor-list-table.php:308
msgctxt "posts"
msgid "All <span class=\"count\">(%s)</span>"
msgid_plural "All <span class=\"count\">(%s)</span>"
msgstr[0] "Tout <span class=\"count\">(%s)</span>"
msgstr[1] "Tous <span class=\"count\">(%s)</span>"

#: admin/class-bulk-description-editor-list-table.php:47
msgid "New Yoast Meta Description"
msgstr "Nouvelle méta description Yoast"

#: admin/class-bulk-description-editor-list-table.php:46
msgid "Existing Yoast Meta Description"
msgstr "Méta description Yoast actuelle"

#: admin/class-admin.php:308
#: src/user-meta/framework/additional-contactmethods/facebook.php:28
msgid "Facebook profile URL"
msgstr "URL du profil Facebook"

#: admin/class-admin.php:229
msgid "FAQ"
msgstr "FAQ"

#: admin/class-admin.php:224 admin/views/redirects.php:42
#: src/integrations/settings-integration.php:325
#: src/presenters/meta-description-presenter.php:37 js/dist/how-to-block.js:11
msgid "Settings"
msgstr "Réglages"

#: src/presenters/admin/search-engines-discouraged-presenter.php:33
msgid "Huge SEO Issue: You're blocking access to robots."
msgstr "Problème SEO majeur : Vous bloquez l’accès aux robots."

#: admin/class-admin.php:182
msgid "Posts"
msgstr "Articles"

#: admin/menu/class-network-admin-menu.php:63
msgid "Edit Files"
msgstr "Modifier les fichiers"

#: admin/menu/class-network-admin-menu.php:56 admin/pages/network.php:18
#: src/general/user-interface/general-page-integration.php:158
#: js/dist/new-settings.js:354
msgid "General"
msgstr "Général"

#: admin/menu/class-admin-menu.php:92
msgid "Search Console"
msgstr "Search Console"

#: admin/menu/class-admin-menu.php:96 js/dist/new-settings.js:312
msgid "Tools"
msgstr "Outils"

#: admin/views/class-yoast-feature-toggles.php:136 js/dist/new-settings.js:38
#: js/dist/new-settings.js:314 js/dist/new-settings.js:316
msgid "XML sitemaps"
msgstr "Plans de site XML"

#: admin/metabox/class-metabox.php:435
#: admin/taxonomy/class-taxonomy-metabox.php:160 js/dist/new-settings.js:42
msgid "Social"
msgstr "Réseaux sociaux"

#: admin/metabox/class-metabox.php:410
#: admin/taxonomy/class-taxonomy-metabox.php:142
#: inc/class-wpseo-admin-bar-menu.php:715
#: src/services/health-check/report-builder.php:168
msgid "SEO"
msgstr "SEO"

#. translators: %1$s expands to Yoast SEO, %2$s expands to the installed
#. version, %3$s expands to Gutenberg
#: admin/class-admin-gutenberg-compatibility-notification.php:88
msgid "%1$s detected you are using version %2$s of %3$s, please update to the latest version to prevent compatibility issues."
msgstr "%1$s a détecté que vous utilisez la version de %2$s de %3$s. Veuillez la mettre à jour vers la dernière version pour éviter les problèmes de compatibilité."

#: src/services/health-check/default-tagline-runner.php:32
msgid "Just another WordPress site"
msgstr "Un site utilisant WordPress"

#: admin/ajax.php:206
msgid "You have used HTML in your value which is not allowed."
msgstr "Votre valeur inclut du code HTML non autorisé."

#. translators: %s expands to the name of a post type (plural).
#: admin/ajax.php:197
msgid "You can't edit %s that aren't yours."
msgstr "Vous ne pouvez pas modifier les %s qui ne sont pas à/de vous."

#. translators: %s expands to post type name.
#: admin/ajax.php:185
msgid "You can't edit %s."
msgstr "Vous ne pouvez pas modifier %s."

#. translators: %s expands to post type.
#: admin/ajax.php:173
msgid "Post has an invalid Content Type: %s."
msgstr "L’article a un type de publication non valide : %s."

#: admin/ajax.php:162
msgid "Post doesn't exist."
msgstr "L’article n’existe pas."