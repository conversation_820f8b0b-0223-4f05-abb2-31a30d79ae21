{"translation-revision-date": "2024-10-02 16:14:23+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "Align the last block to the bottom.": ["Align the last block to the bottom."], "Align the last block to the bottom": ["Align the last block to the bottom"], "An error has prevented the block from being updated.": ["An error has prevented the block from being updated."], "Stock status \"%s\" hidden.": ["Stock status \"%s\" hidden."], "Stock status \"%s\" visible.": ["Stock status \"%s\" visible."], "Edit selected attribute": ["Edit selected attribute"], "%d term": ["%d term", "%d terms"], "%1$s, has %2$d term": ["%1$s, has %2$d term", "%1$s, has %2$d terms"], "%1$s, has %2$d product": ["%1$s, has %2$d product", "%1$s, has %2$d products"], "Loading…": ["Loading…"], "The last inner block will follow other content.": ["The last inner block will follow other content."], "The following error was returned": ["The following error was returned"], "The following error was returned from the API": ["The following error was returned from the API"], "Search results updated.": ["Search results updated."], "%d item selected": ["%d item selected", "%d items selected"], "Search for items": ["Search for items"], "No results for %s": ["No results for %s"], "No items found.": ["No items found."], "Clear all selected items": ["Clear all selected items"], "Clear all": ["Clear all"], "Remove %s": ["Remove %s"], "%d attribute selected": ["%d attribute selected", "%d attributes selected"], "All selected attributes": ["All selected attributes"], "Any selected attributes": ["Any selected attributes"], "Clear all product attributes": ["Clear all product attributes"], "Columns": ["Columns"], "Display a grid of products from your selected attributes.": ["Display a grid of products from your selected attributes."], "Done": ["Done"], "Layout": ["Layout"], "Pick at least two attributes to use this setting.": ["Pick at least two attributes to use this setting."], "Product attribute search results updated.": ["Product attribute search results updated."], "Rows": ["Rows"], "Search for product attributes": ["Search for product attributes"], "Your store doesn't have any product attributes.": ["Your store doesn't have any product attributes."], "Products by Attribute": ["Products by Attribute"], "Showing Products by Attribute block preview.": ["Showing Products by Attribute block preview."], "Order By": ["Order By"], "Filter by Product Attribute": ["Filter by Product Attribute"], "Add to Cart button": ["Add to Basket button"], "Display products matching": ["Display products matching"], "Newness - newest first": ["Newness - newest first"], "Order products by": ["Order products by"], "Price - high to low": ["Price - high to low"], "Price - low to high": ["Price - low to high"], "Product price": ["Product price"], "Product rating": ["Product rating"], "Product title": ["Product title"], "Rating - highest first": ["Rating - highest first"], "Sales - most first": ["Sales - most first"], "Title - alphabetical": ["Title - alphabetical"], "Menu Order": ["Menu Order"], "Filter by stock status": ["Filter by stock status"], "Product image": ["Product image"], "%d product": ["%d product", "%d products"], "Content": ["Content"]}}, "comment": {"reference": "assets/client/blocks/products-by-attribute.js"}}