<?php
/**
 * Database Cleanup and Optimization Script
 *
 * WARNING: This script should only be run by administrators
 * Always backup your database before running this script
 *
 * Usage: Access via WordPress admin or run via WP-CLI
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Only allow administrators to run this
if ( ! current_user_can( 'manage_options' ) ) {
    wp_die( 'You do not have permission to access this page.' );
}

class TCL_Database_Optimizer {

    private $results = array();

    public function __construct() {
        $this->results = array(
            'spam_comments' => 0,
            'trash_posts' => 0,
            'revisions' => 0,
            'auto_drafts' => 0,
            'transients' => 0,
            'orphaned_meta' => 0
        );
    }

    /**
     * Run all optimization tasks
     */
    public function optimize_all() {
        $this->clean_spam_comments();
        $this->clean_trash_posts();
        $this->clean_post_revisions();
        $this->clean_auto_drafts();
        $this->clean_expired_transients();
        $this->clean_orphaned_meta();
        $this->optimize_tables();

        return $this->results;
    }

    /**
     * Clean spam comments
     */
    private function clean_spam_comments() {
        global $wpdb;

        $count = $wpdb->get_var( "SELECT COUNT(*) FROM {$wpdb->comments} WHERE comment_approved = 'spam'" );

        if ( $count > 0 ) {
            $wpdb->query( "DELETE FROM {$wpdb->comments} WHERE comment_approved = 'spam'" );
            $wpdb->query( "DELETE FROM {$wpdb->commentmeta} WHERE comment_id NOT IN (SELECT comment_ID FROM {$wpdb->comments})" );
            $this->results['spam_comments'] = $count;
        }
    }

    /**
     * Clean trash posts
     */
    private function clean_trash_posts() {
        global $wpdb;

        $count = $wpdb->get_var( "SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_status = 'trash'" );

        if ( $count > 0 ) {
            $wpdb->query( "DELETE FROM {$wpdb->posts} WHERE post_status = 'trash'" );
            $this->results['trash_posts'] = $count;
        }
    }

    /**
     * Clean old post revisions (keep only 3 most recent)
     */
    private function clean_post_revisions() {
        global $wpdb;

        $revisions = $wpdb->get_results( "
            SELECT post_parent, COUNT(*) as revision_count
            FROM {$wpdb->posts}
            WHERE post_type = 'revision'
            GROUP BY post_parent
            HAVING revision_count > 3
        " );

        $total_deleted = 0;

        foreach ( $revisions as $revision ) {
            $old_revisions = $wpdb->get_results( $wpdb->prepare( "
                SELECT ID
                FROM {$wpdb->posts}
                WHERE post_parent = %d
                AND post_type = 'revision'
                ORDER BY post_date DESC
                LIMIT 999 OFFSET 3
            ", $revision->post_parent ) );

            foreach ( $old_revisions as $old_revision ) {
                wp_delete_post( $old_revision->ID, true );
                $total_deleted++;
            }
        }

        $this->results['revisions'] = $total_deleted;
    }

    /**
     * Clean auto-drafts older than 7 days
     */
    private function clean_auto_drafts() {
        global $wpdb;

        $count = $wpdb->get_var( "
            SELECT COUNT(*)
            FROM {$wpdb->posts}
            WHERE post_status = 'auto-draft'
            AND post_date < DATE_SUB(NOW(), INTERVAL 7 DAY)
        " );

        if ( $count > 0 ) {
            $wpdb->query( "
                DELETE FROM {$wpdb->posts}
                WHERE post_status = 'auto-draft'
                AND post_date < DATE_SUB(NOW(), INTERVAL 7 DAY)
            " );
            $this->results['auto_drafts'] = $count;
        }
    }

    /**
     * Clean expired transients
     */
    private function clean_expired_transients() {
        global $wpdb;

        $count = $wpdb->get_var( "
            SELECT COUNT(*)
            FROM {$wpdb->options}
            WHERE option_name LIKE '_transient_timeout_%'
            AND option_value < UNIX_TIMESTAMP()
        " );

        if ( $count > 0 ) {
            $wpdb->query( "
                DELETE a, b FROM {$wpdb->options} a, {$wpdb->options} b
                WHERE a.option_name LIKE '_transient_timeout_%'
                AND a.option_value < UNIX_TIMESTAMP()
                AND b.option_name = CONCAT('_transient_', SUBSTRING(a.option_name, 20))
            " );
            $this->results['transients'] = $count;
        }
    }

    /**
     * Clean orphaned metadata
     */
    private function clean_orphaned_meta() {
        global $wpdb;

        // Clean orphaned post meta
        $post_meta_count = $wpdb->get_var( "
            SELECT COUNT(*)
            FROM {$wpdb->postmeta}
            WHERE post_id NOT IN (SELECT ID FROM {$wpdb->posts})
        " );

        if ( $post_meta_count > 0 ) {
            $wpdb->query( "
                DELETE FROM {$wpdb->postmeta}
                WHERE post_id NOT IN (SELECT ID FROM {$wpdb->posts})
            " );
        }

        // Clean orphaned comment meta
        $comment_meta_count = $wpdb->get_var( "
            SELECT COUNT(*)
            FROM {$wpdb->commentmeta}
            WHERE comment_id NOT IN (SELECT comment_ID FROM {$wpdb->comments})
        " );

        if ( $comment_meta_count > 0 ) {
            $wpdb->query( "
                DELETE FROM {$wpdb->commentmeta}
                WHERE comment_id NOT IN (SELECT comment_ID FROM {$wpdb->comments})
            " );
        }

        $this->results['orphaned_meta'] = $post_meta_count + $comment_meta_count;
    }

    /**
     * Optimize database tables
     */
    private function optimize_tables() {
        global $wpdb;

        $tables = $wpdb->get_results( "SHOW TABLES", ARRAY_N );

        foreach ( $tables as $table ) {
            $wpdb->query( "OPTIMIZE TABLE {$table[0]}" );
        }
    }

    /**
     * Get optimization results
     */
    public function get_results() {
        return $this->results;
    }
}

// Usage example (uncomment to run):
/*
if ( isset( $_GET['run_optimization'] ) && $_GET['run_optimization'] === 'true' ) {
    $optimizer = new TCL_Database_Optimizer();
    $results = $optimizer->optimize_all();

    echo '<div class="notice notice-success"><p>Database optimization completed:</p>';
    echo '<ul>';
    echo '<li>Spam comments deleted: ' . $results['spam_comments'] . '</li>';
    echo '<li>Trash posts deleted: ' . $results['trash_posts'] . '</li>';
    echo '<li>Post revisions deleted: ' . $results['revisions'] . '</li>';
    echo '<li>Auto-drafts deleted: ' . $results['auto_drafts'] . '</li>';
    echo '<li>Expired transients deleted: ' . $results['transients'] . '</li>';
    echo '<li>Orphaned metadata deleted: ' . $results['orphaned_meta'] . '</li>';
    echo '</ul></div>';
}
*/