<?php
return ['x-generator'=>'GlotPress/4.0.0-rc.1','translation-revision-date'=>'2020-03-02 09:08:16+0000','plural-forms'=>'nplurals=2; plural=n != 1;','project-id-version'=>'Plugins - WP Consent API - Stable (latest release)','language'=>'en_GB','messages'=>['Consent Level API to read and register the current consent level for cookie management and improving compliance with privacy laws.'=>'Consent Level API to read and register the current consent level for cookie management and improving compliance with privacy laws.','Not all plugins have declared to follow Consent API guidelines. Please contact the developer.'=>'Not all plugins have declared to follow Consent API guidelines. Please contact the developer.','One or more plugins are not conforming to the Consent API.'=>'One or more plugins are not conforming to the Consent API.','All plugins have declared in their code that they are following the guidelines from the WP Consent API. When used in combination with a Cookie Management plugin, this will improve compliancy for your site.'=>'All plugins have declared in their code that they are following the guidelines from the WP Consent API. When used in combination with a Cookie Management plugin, this will improve compliancy for your site.','Compliance'=>'Compliance','All plugins have declared to use the Consent API'=>'All plugins have declared to use the Consent API','WP Consent API test'=>'WP Consent API test','This plugin requires WordPress 5.0 or higher'=>'This plugin requires WordPress 5.0 or higher','This plugin requires PHP 5.6 or higher'=>'This plugin requires PHP 5.6 or higher','%s is a singleton class and you cannot create a second instance.'=>'%s is a singleton class and you cannot create a second instance.','https://github.com/rlankhorst/wp-consent-level-api'=>'https://github.com/rlankhorst/wp-consent-level-api','RogierLankhorst'=>'RogierLankhorst','WP Consent API'=>'WP Consent API','https://wordpress.org/plugins/wp-consent-api'=>'https://en-gb.wordpress.org/plugins/wp-consent-api']];