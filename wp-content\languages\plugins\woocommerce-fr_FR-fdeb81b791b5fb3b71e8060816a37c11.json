{"translation-revision-date": "2025-07-27 07:54:28+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "fr"}, "%1$s was successfully activated.": ["%1$s a été activé avec succès."], "A plugin was successfully installed and activated.": ["Une extension a été installée et activée avec succès."], "%1$s (%2$s) was successfully installed and activated.": ["%1$s (%2$s) a été installée et activée avec succès."], "A plugin was successfully activated.": ["Une extension a été activée avec succès."], "Google for WooCommerce": ["Google pour WooCommerce"], "WooPayments": ["WooPayments"], "Omnichannel for WooCommerce": ["Omnichannel pour WooCommerce"], "TikTok for WooCommerce": ["TikTok pour WooCommerce"], "Pinterest for WooCommerce": ["Pinterest pour WooCommerce"], "Mercado Pago payments for WooCommerce": ["Plugin de paiement Mercado Pago pour WooCommerce"], "MailPoet": ["MailPoet"], "Could not %(actionType)s %(pluginName)s plugin, %(error)s": ["Impossible de %(actionType)s %(pluginName)s l’extension, %(error)s", "Impossible de %(actionType)s les extensions suivantes : %(pluginName)s avec les erreurs suivantes : %(error)s"], "Razorpay": ["Razorpay"], "Creative Mail for WooCommerce": ["Creative Mail pour WooCommerce"], "WooCommerce Shipping & Tax": ["WooCommerce Shipping & Tax"], "WooCommerce Stripe": ["WooCommerce Stripe"], "WooCommerce PayPal": ["WooCommerce PayPal"], "WooCommerce Payfast": ["WooCommerce Payfast"], "WooCommerce ShipStation Gateway": ["WooCommerce ShipStation Gateway"], "There was a problem updating your settings.": ["Un problème est survenu lors de la mise à jour de vos réglages."], "Plugins were successfully installed and activated.": ["Les extensions ont été correctement installées et activées."], "MM/DD/YYYY": ["JJ/MM/AAAA"], "Facebook for WooCommerce": ["Facebook for WooCommerce"], "Mailchimp for WooCommerce": ["MailChimp for WooCommerce"], "Klarna Payments for WooCommerce": ["Klarna Payments for WooCommerce"], "Klarna Checkout for WooCommerce": ["<PERSON><PERSON><PERSON> Checkout for WooCommerce"], "Jetpack": ["Jetpack"]}}, "comment": {"reference": "assets/client/admin/data/index.js"}}