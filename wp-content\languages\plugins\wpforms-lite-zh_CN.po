# Translation of Plugins - Contact Form by WPForms &#8211; <PERSON>ag &amp; Drop Form Builder for WordPress - Stable (latest release) in Chinese (China)
# This file is distributed under the same license as the Plugins - Contact Form by WPForms &#8211; Drag &amp; Drop Form Builder for WordPress - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2021-06-19 02:03:03+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: GlotPress/3.0.0-alpha.2\n"
"Language: zh_CN\n"
"Project-Id-Version: Plugins - Contact Form by WPForms &#8211; Drag &amp; Drop Form Builder for WordPress - Stable (latest release)\n"

#: lite/templates/admin/addons.php:27
msgid "Unknown Addon"
msgstr "未知扩展"

#: lite/templates/admin/addons.php:45
msgid "Learn more"
msgstr "了解更多"

#. translators: %s - unavailable field name.
#: includes/admin/builder/panels/class-fields.php:415
msgid "Unfortunately, the %s field is not available and will be ignored on the front end."
msgstr "抱歉，%s 字段不可用，且在前端会被忽略。"

#: src/WPForms.php:83
msgid "Property smart_tags was deprecated, use wpforms()->get( 'smart_tags' ) instead of wpforms()->smart_tags"
msgstr "属性 smart_tags 已被弃用，请使用 wpforms()->get('smart_tags') 而不是 wpforms()->smart_tags"

#: src/SmartTags/SmartTags.php:110
msgid "Field Value"
msgstr "字段值"

#: src/SmartTags/SmartTags.php:109
msgid "Field HTML ID"
msgstr "字段 HTML ID"

#: src/SmartTags/SmartTags.php:108
msgid "Field ID"
msgstr "字段 ID"

#. translators: %s - site domain.
#: src/Emails/Summaries.php:242
msgid "Your Weekly WPForms Summary for %s"
msgstr "%s 的 WPForms 每周总结"

#: src/Admin/Education/Core.php:175
msgid "Please specify a section."
msgstr "请指定一个部分。"

#: src/Admin/Education/Builder/Captcha.php:78
msgid "Something wrong. Please try again later."
msgstr "出了点问题。请稍后再试。"

#: src/Admin/Pages/SMTP.php:174 src/Admin/Pages/SMTP.php:412
msgid "Open Setup Wizard"
msgstr "打开配置向导"

#. translators: %s - Forms list page URL.
#: src/Admin/Tools/Views/Import.php:120
msgid "You can go and <a href=\"%s\">check your forms</a>."
msgstr "您可以立即<a href=\"%s\">检查您的表单</a>。"

#: src/Admin/Tools/Views/Import.php:115
msgid "Import was successfully finished."
msgstr "导入已成功完成。"

#: src/Admin/Tools/Views/Importer.php:270
msgid "Congrats, the import process has finished! We have successfully imported <span class=\"forms-completed\"></span> forms. You can review the results below."
msgstr "恭喜，导入过程已经完成! 我们已经成功导入了<span class=\"forms-completed\"></span>个表单。您可以查看以下结果。"

#. translators: %s - Provider name.
#: src/Admin/Tools/Views/Importer.php:255
msgid "Importing <span class=\"form-current\">1</span> of <span class=\"form-total\">0</span> forms from %s."
msgstr "从 %s 导入<span class=\"form-current\">1个</span>中的<span class=\"form-total\">0个</span>表单。"

#. translators: %s - Provider name.
#: src/Admin/Tools/Views/Importer.php:211
msgid "Analyzing <span class=\"form-current\">1</span> of <span class=\"form-total\">0</span> forms from %s."
msgstr "正在分析来自 %s 的<span class=\"form-current\">1个</span>中的<span class=\"form-total\">0个</span>表单。"

#. translators: %s - field type.
#: src/Admin/Tools/Importers/NinjaForms.php:497
msgid "%s Field"
msgstr "%s字段"

#: includes/admin/admin.php:155
msgid "Are you sure you want to delete this form and all its information?"
msgstr "您确定要删除此表单极其所有信息吗？"

#: includes/admin/admin.php:142
msgid "Are you sure you want to delete ALL entries and all their information (files, notes, logs, etc.)?"
msgstr "您确定要删除所有条目极其所有信息（文件、注释、日志等）吗？"

#: includes/admin/admin.php:141
msgid "Are you sure you want to delete this entry and all its information (files, notes, logs, etc.)?"
msgstr "您确定要删除此条目极其所有信息（文件、注释、日志等）吗？"

#: includes/admin/class-about.php:1030
msgid "Boost your sales and conversions by up to 15% with real-time social proof notifications. TrustPulse helps you show live user activity and purchases to help convince other users to purchase."
msgstr "通过实时社交证明通知，将您的销售和转换率提升最高 15%。TrustPulse 帮助您展示实时的用户动向和购买情况来说服其他用户购买。"

#: includes/admin/class-about.php:1029
msgid "TrustPulse"
msgstr "TrustPulse"

#: includes/admin/class-about.php:1020
msgid "Smash Balloon Twitter Feeds Pro"
msgstr "Smash Balloon Twitter Feeds Pro"

#: includes/admin/class-about.php:1014 includes/admin/class-about.php:1021
msgid "Easily display Twitter content in WordPress without writing any code. Comes with multiple layouts, ability to combine multiple Twitter feeds, Twitter card support, tweet moderation, and more."
msgstr "简单的在 WordPress 中显示 Twitter 内容，无需编码。有多种布局可选，能够合并多个推文，支持 Twitter 卡片，推文预览等。"

#: includes/admin/class-about.php:1013
msgid "Smash Balloon Twitter Feeds"
msgstr "Smash Balloon Twitter Feeds"

#: includes/admin/class-about.php:1004
msgid "Smash Balloon YouTube Feeds Pro"
msgstr "Smash Balloon YouTube Feeds Pro"

#: includes/admin/class-about.php:998 includes/admin/class-about.php:1005
msgid "Easily display YouTube videos on your WordPress site without writing any code. Comes with multiple layouts, ability to embed live streams, video filtering, ability to combine multiple channel videos, and more."
msgstr "轻松在您的 WordPress 站点上嵌入 YouTube 视频，无需编码。配有多种布局，能够嵌入直播流，视频过滤，合并多个频道的视频等。"

#: includes/admin/class-about.php:997
msgid "Smash Balloon YouTube Feeds"
msgstr "Smash Balloon YouTube Feeds"

#: includes/admin/class-about.php:988
msgid "Smash Balloon Facebook Feeds Pro"
msgstr "Smash Balloon Facebook Feeds Pro"

#: includes/admin/class-about.php:982 includes/admin/class-about.php:989
msgid "Easily display Facebook content on your WordPress site without writing any code. Comes with multiple templates, ability to embed albums, group content, reviews, live videos, comments, and reactions."
msgstr "轻松在您的 WordPress 站点上嵌入 Facebook 内容，无需编码。配有多种模板，能够嵌入相册、小组内容、评价、实时视频、评论和回复。"

#: includes/admin/class-about.php:981
msgid "Smash Balloon Facebook Feeds"
msgstr "Smash Balloon Facebook Feeds"

#: includes/admin/class-about.php:972
msgid "Smash Balloon Instagram Feeds Pro"
msgstr "Smash Balloon Instagram Feeds Pro"

#: includes/admin/class-about.php:966 includes/admin/class-about.php:973
msgid "Easily display Instagram content on your WordPress site without writing any code. Comes with multiple templates, ability to show content from multiple accounts, hashtags, and more. Trusted by 1 million websites."
msgstr "轻松在您的 WordPress 站点上嵌入 Instagram 内容，无需编码。配有多个模板，能够显示来自多个账户的内容、标签等。受到 100万个网站的信赖。"

#: includes/admin/class-about.php:965
msgid "Smash Balloon Instagram Feeds"
msgstr "Smash Balloon Instagram Feeds"

#: includes/admin/class-about.php:958
msgid "Connect with your visitors after they leave your website with the leading web push notification software. Over 10,000+ businesses worldwide use PushEngage to send 9 billion notifications each month."
msgstr "领先的 web 推送通知软件，让您在访客离开您的网站后与他们继续保持联系。全球有超过 10,000家企业使用 PushEngage，每月发送 90亿条通知。"

#: includes/admin/class-about.php:957
msgid "PushEngage"
msgstr "PushEngage"

#: includes/admin/class-about.php:942 includes/admin/class-about.php:949
msgid "Turn your website visitors into brand ambassadors! Easily grow your email list, website traffic, and social media followers with the most powerful giveaways & contests plugin for WordPress."
msgstr "将您的网站访客转变为品牌大使! 使用 强大的 WordPress 赠品和竞赛插件，轻松增加您的电子邮件列表、网站流量和社交媒体粉丝。"

#: includes/admin/class-about.php:932
msgid "SeedProd Pro"
msgstr "SeedProd Pro"

#: includes/admin/class-about.php:926 includes/admin/class-about.php:933
msgid "The fastest drag & drop landing page builder for WordPress. Create custom landing pages without writing code, connect them with your CRM, collect subscribers, and grow your audience. Trusted by 1 million sites."
msgstr "为 WordPress 打造的超快速拖拽式着陆页面构建器。无需编写代码即可创建自定义着陆页，将 SeedProd 与您的客户关系管理系统（CRM）相连接，收纳订阅者并增加您的受众。受到 100万个网站的信赖。"

#: includes/admin/class-about.php:925
msgid "SeedProd"
msgstr "SeedProd"

#: includes/admin/class-about.php:916
msgid "AIOSEO Pro"
msgstr "AIOSEO Pro"

#: includes/admin/class-about.php:910 includes/admin/class-about.php:917
msgid "The original WordPress SEO plugin and toolkit that improves your website's search rankings. Comes with all the SEO features like Local SEO, WooCommerce SEO, sitemaps, SEO optimizer, schema, and more."
msgstr "原创的WordPress SEO插件和工具包，提高您网站的搜索排名。附带了大量的SEO功能，如本地SEO，WooCommerce SEO，站点地图，SEO优化程序等。"

#: includes/admin/class-about.php:909
msgid "AIOSEO"
msgstr "AIOSEO"

#: includes/admin/class-about.php:894 includes/admin/class-about.php:901
msgid "Improve your WordPress email deliverability and make sure that your website emails reach user's inbox with the #1 SMTP plugin for WordPress. Over 2 million websites use it to fix WordPress email issues."
msgstr "使用WordPress的第一款SMTP插件，提高您的WordPress电子邮件可达性以确保您网站的电子邮件能到达用户的收件箱。超过200万个网站使用它来解决WordPress的电子邮件发送问题。"

#: includes/admin/class-about.php:878 includes/admin/class-about.php:885
msgid "The leading WordPress analytics plugin that shows you how people find and use your website, so you can make data driven decisions to grow your business. Properly set up Google Analytics without writing code."
msgstr "领先的WordPress分析插件，可向您显示人们如何查找和使用您的网站，以便您制定以数据为依据的决策来发展您的业务。无需编写代码即可正确设置Google Analytics（分析）。"

#: includes/admin/class-about.php:870
msgid "Instantly get more subscribers, leads, and sales with the #1 conversion optimization toolkit. Create high converting popups, announcement bars, spin a wheel, and more with smart targeting and personalization."
msgstr "使用排名第一的转化优化工具包，立即得到更多的订阅者、潜在客户和销售额。通过智能定位和个性化功能，创建高转化率的弹出窗口、公告栏、幻灯片等。"

#: includes/admin/class-settings.php:502
msgid "Check this option to hide the WPForms admin bar menu."
msgstr "勾选此选项可隐藏WPForms管理栏菜单。"

#: includes/admin/class-settings.php:496
msgid "Check this option to hide plugin announcements and update details."
msgstr "勾选此选项以隐藏插件公告和更新详情。"

#: templates/emails/summary-body.php:31
msgid "Below is the total number of submissions for each form, <strong>however actual entries are not stored by WPForms Lite</strong>."
msgstr "以下是每个表单的提交总数，<strong>虽然WPForms Lite不会存储实际条目</strong>。"

#: templates/education/admin/settings/geolocation/screenshots.php:27
msgid "Smart Address Field"
msgstr "智能地址字段"

#: templates/education/admin/settings/geolocation/screenshots.php:22
msgid "Address Autocomplete Field"
msgstr "地址自动填充字段"

#: templates/education/admin/settings/geolocation/screenshots.php:17
msgid "Location Info in Entries"
msgstr "条目中的位置信息"

#: includes/admin/class-settings.php:425
msgid "Number Positive"
msgstr "正数"

#: includes/admin/builder/panels/class-payments.php:111
msgid "It seems you don't have any payment addons activated. Click one of the available addons and start accepting payments today!"
msgstr "您似乎没有启用任何支付扩展。单击其中一个可用的扩展并立即开始接受付款！"

#: includes/admin/admin.php:211
msgid "To edit the License Key, please first click the Deactivate Key button. Please note that deactivating this key will remove access to updates, addons, and support."
msgstr "要编辑许可证密钥，请先点击停用密钥按钮。请注意，停用此密钥将取消对更新、扩展和支持的访问。"

#: includes/functions.php:865
msgid "Bonaire, Saint Eustatius and Saba"
msgstr "北京、上海和深圳。"

#. translators: %s - URL WP Mail SMTP settings.
#: src/Integrations/WPMailSMTP/Notifications.php:105
msgid "This setting is disabled because you have the \"Force From Email\" setting enabled in the <a href=\"%s\" target=\"_blank\">WP Mail SMTP</a> plugin."
msgstr "此设置被禁用，因为您在<a href=\"%s\" target=\"_blank\">WP MAIL SMTP</a>插件的设置中启用了\"强制发件人地址\"。"

#. translators: %s - URL WP Mail SMTP settings.
#: src/Integrations/WPMailSMTP/Notifications.php:76
msgid "This setting is disabled because you have the \"Force From Name\" setting enabled in the <a href=\"%s\" target=\"_blank\">WP Mail SMTP</a> plugin."
msgstr "此设置被禁用，因为您在<a href=\"%s\" target=\"_blank\">WP MAIL SMTP</a>插件的设置中启用了\"强制发件人名称\"。"

#: src/Providers/Provider/Settings/PageIntegrations.php:111
msgid "N/A"
msgstr "N/A"

#: src/Providers/Provider/Settings/PageIntegrations.php:105
msgid "No Label"
msgstr "无标签"

#. translators: %d - Form ID
#: src/Admin/AdminBarMenu.php:336
msgid "Form ID: %d"
msgstr "表单ID:%d"

#: src/Admin/AdminBarMenu.php:324
msgid "Survey Results"
msgstr "调查结果"

#: templates/education/admin/settings/geolocation/submit.php:32
msgid "Install & Activate"
msgstr "安装并启用"

#: templates/education/admin/settings/geolocation/caps.php:30
msgid "Powerful location-based insights and features…"
msgstr "强大基于位置的检测和功能......"

#: templates/education/admin/settings/geolocation/caps.php:25
msgid "Algolia Places API"
msgstr "Algolia Places API"

#: templates/education/admin/settings/geolocation/caps.php:24
msgid "Google Places API"
msgstr "Google Places API"

#: templates/education/admin/settings/geolocation/caps.php:21
msgid "Embedded Map in Forms"
msgstr "表单中嵌入的地图"

#: templates/education/admin/settings/geolocation/caps.php:19
msgid "Latitude/Longitude"
msgstr "纬度/经度"

#: templates/education/admin/settings/geolocation/caps.php:16
msgid "Postal/Zip Code"
msgstr "邮地/邮编"

#: templates/education/admin/settings/geolocation/caps.php:15
msgid "Country"
msgstr "国家/地区"

#: templates/education/admin/settings/geolocation/caps.php:14
msgid "City"
msgstr "市"

#: src/Lite/Admin/Connect.php:149
msgid "There was an error while installing an upgrade. Please download the plugin from wpforms.com and install it manually."
msgstr "安装升级时出错。请从wpforms.com下载插件并手动安装。"

#: src/Lite/Admin/Connect.php:99
msgid "WPForms Pro is installed but not activated."
msgstr "WPForms Pro已安装但未启用。"

#: src/Lite/Admin/Connect.php:72
msgid "You are not allowed to install plugins."
msgstr "您無權安裝插件。"

#: src/Admin/Education/Builder/Geolocation.php:153
#: src/Admin/Education/Builder/Geolocation.php:164
msgid "Enable Address Autocomplete"
msgstr "启用地址自动填充"

#: src/Admin/Education/Builder/Geolocation.php:130
msgid "We're sorry, Address Autocomplete is part of the Geolocation Addon and not available on your plan. Please upgrade to the PRO plan to unlock all these awesome features."
msgstr "很抱歉，地址自动填充功能是地理位置扩展的一部分，您当前的套餐不支持此功能。请升级到pro套餐以解锁所有强大功能。"

#: src/Admin/Education/Builder/Geolocation.php:128
#: templates/education/admin/settings/geolocation/caps.php:20
msgid "Address Autocomplete"
msgstr "地址自动填充"

#: includes/class-frontend.php:1900
msgid "This message is only displayed to site administrators."
msgstr "此消息只会显示给站点管理员。"

#. translators: %s - URL to the troubleshooting guide.
#: includes/class-frontend.php:1887
msgid "Heads up! WPForms has detected an issue with JavaScript on this page. JavaScript is required for this form to work properly, so this form may not work as expected. See our <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">troubleshooting guide</a> to learn more or contact support."
msgstr "注意! WPForms检测到此页面的JavaScript存在问题。此表单需要JavaScript才能正常运作，此表单可能无法按预期运作。请参阅我们的<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">故障排除指南</a>以了解更多信息或联系支持。"

#: src/Forms/Token.php:261
msgid "The form was unable to submit. Please contact the site administrator."
msgstr "表单无法提交。请联系站点管理员。"

#: src/Admin/Settings/Captcha.php:283
msgid "A preview of your CAPTCHA is displayed below. Please view to verify the CAPTCHA settings are correct."
msgstr "下面将显示验证码的预览。请查看以确认验证码设置是否正确。"

#: src/Admin/Settings/Captcha.php:248
msgid "This CAPTCHA is generated using your site and secret keys. If an error is displayed, please double-check your keys."
msgstr "此验证码是由您的站点和密匙生成的。如果显示错误，请仔细检查您的密钥。"

#: src/Admin/Settings/Captcha.php:234
msgid "Please save settings to generate a preview of your CAPTCHA here."
msgstr "请保存设置以在此生成验证码预览。"

#: src/Admin/Settings/Captcha.php:210
msgid "hCaptcha verification failed, please try again later."
msgstr "hCaptcha验证失败，请稍后再试。"

#. translators: %s - WPForms.com CAPTCHA comparison page URL.
#: src/Admin/Settings/Captcha.php:142
msgid "Not sure which service is right for you? <a href=\"https://wpforms.com/docs/setup-captcha-wpforms/\" target=\"_blank\" rel=\"noopener noreferrer\">Check out our comparison</a> for more details."
msgstr "不确定哪种服务适合您？<a href=\"https://wpforms.com/docs/setup-captcha-wpforms/\" target=\"_blank\" rel=\"noopener noreferrer\">参考我们的比较文章</a>以了解更多信息。"

#: src/Admin/Settings/Captcha.php:126
msgid "A CAPTCHA is an anti-spam technique which helps to protect your website from spam and abuse while letting real people pass through with ease. WPForms supports two popular services."
msgstr "CAPTCHA是一种反垃圾内容技术，有助于保护您的网站免受垃圾信息和滥用的侵害，同时让真实的用户能够轻松通过。目前，WPForms支持两种常见的服务。"

#. translators: %s - CAPTCHA name.
#: src/Admin/Education/Builder/Captcha.php:181
msgid "Are you sure you want to disable %s for this form?"
msgstr "您确定要禁用此表单的%s验证码吗？"

#: src/Admin/Education/Fields.php:141
msgid "Custom Captcha"
msgstr "自定义验证码"

#: src/Admin/Education/Builder/Captcha.php:119
#: src/Admin/Education/Fields.php:273 src/Admin/Settings/Captcha.php:103
#: src/Admin/Settings/Captcha.php:126
msgid "CAPTCHA"
msgstr "验证码"

#: src/Providers/Provider/Settings/PageIntegrations.php:228
msgid "Connection missing."
msgstr "连接丢失。"

#: src/Providers/Provider/Settings/PageIntegrations.php:212
msgid "Missing data."
msgstr "数据缺失。"

#: src/Providers/Provider/Settings/PageIntegrations.php:195
#: src/Providers/Provider/Settings/PageIntegrations.php:245
msgid "Your session expired. Please reload the page."
msgstr "您的会话已过期。请重新加载页面。"

#: templates/admin/form-embed-wizard/popup.php:65
msgid "Copy embed code to clipboard"
msgstr "将嵌入代码复制到剪贴板"

#. translators: %s - WPForms.com Setup hCaptcha URL.
#: templates/admin/settings/hcaptcha-description.php:6
msgid "For more details on how hCaptcha works, as well as a step by step setup guide, please check out our <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">documentation</a>."
msgstr "有关hCaptcha的运作方式及各步骤的设置指南的详细信息，请查看我们的<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">文档</a>。"

#: templates/admin/settings/hcaptcha-description.php:1
msgid "hCaptcha is a free and privacy-oriented spam prevention service. Within your forms, hCaptcha will display a checkbox asking users to prove they're human (much like Google's v2 Checkbox reCAPTCHA). This is a simple step for legitimate site visitors, but is extremely effective at blocking spam."
msgstr "hCaptcha是一个免费的、以隐私为主的垃圾信息防护服务。hCaptcha将会在您的表单中显示一个复选框，要求用户确认自己是人类（很像Google reCAPTCHA v2的复选框）。这对合法的访问者来说是一个简单的步骤，但在阻止垃圾信息方面却非常有效。"

#: includes/functions.php:1080
msgid "Vietnam"
msgstr "Vietnam"

#: includes/functions.php:1051
msgid "Eswatini (Kingdom of)"
msgstr "Eswatini (Kingdom of)"

#: includes/functions.php:956
msgid "Kosovo"
msgstr "Kosovo"

#: includes/class-frontend.php:914
msgid "Google reCAPTCHA v2"
msgstr "Google reCAPTCHA v2"

#. translators: %s - The CAPTCHA provider name.
#: includes/class-process.php:211
msgid "%s verification failed, please try again later."
msgstr "%s验证失败，请稍后再试。"

#: includes/class-process.php:206
msgid "Google reCAPTCHA"
msgstr "Google reCAPTCHA"

#: includes/admin/class-settings.php:459
msgid "Word Limit"
msgstr "字数限制"

#: includes/admin/class-settings.php:449
msgid "Character Limit"
msgstr "字符限制"

#. translators: %s - Link to the WPForms.com doc article.
#: includes/admin/builder/class-builder.php:608
msgid "Disabling entry storage for this form will completely prevent any new submissions from getting saved to your site. If you still intend to keep a record of entries through notification emails, then please <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">test your form</a> to ensure emails send reliably."
msgstr "禁用此表单的条目保存将完全禁止任何新提交的条目记录被保存到您的站点。如果您仍然打算通过通知邮件来保存条目记录，请 <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">测试您的表单</a>，以确保邮件能够可靠发送。"

#: includes/admin/builder/panels/class-settings.php:202
msgid "Enable hCaptcha"
msgstr "启用验证码"

#: includes/admin/admin.php:169
msgid "Could not disconnect this account."
msgstr "无法断开此帐户的连接。"

#. translators: %s - URL to WPForms.com doc article.
#: includes/fields/class-select.php:258
msgid "For details, including how this looks and works for your site's visitors, please check out <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">our doc</a>. "
msgstr "有关详细信息，包括对您站点的访问者如何显示和使用，请查看 <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">我们的文档</a>。"

#: src/Logger/ListTable.php:421
msgid "Apply"
msgstr "应用"

#: src/Logger/ListTable.php:412
msgid "All Logs"
msgstr "所有日志"

#: src/Logger/ListTable.php:381
msgid "No logs found."
msgstr "未找到日志"

#: src/Logger/ListTable.php:345
msgid "Delete All Logs"
msgstr "删除所有日志"

#: src/Logger/ListTable.php:287
msgid "Search Logs"
msgstr "搜索日志"

#. translators: %s: search query
#: src/Logger/ListTable.php:278
msgid "Search results for \"%s\""
msgstr "“%s”的搜索结果"

#: src/Logger/ListTable.php:273
msgid "View Logs"
msgstr "查看日志"

#: src/Logger/ListTable.php:259 src/Logger/ListTable.php:449
msgid "Types"
msgstr "类型"

#: src/Logger/ListTable.php:256 src/Logger/ListTable.php:436
msgid "Log Title"
msgstr "日志标题"

#: src/Logger/ListTable.php:255 src/Logger/ListTable.php:455
msgid "Log ID"
msgstr "日志ID"

#: src/Logger/ListTable.php:40
msgid "Log"
msgstr "日志"

#: src/Logger/Log.php:180
msgid "Record ID doesn't found"
msgstr "未找到记录ID"

#: src/Logger/Log.php:101
msgid "Spam"
msgstr "垃圾内容"

#: src/Logger/Log.php:99
msgid "Providers"
msgstr "提供者"

#: src/Logger/Log.php:98
msgid "Payment"
msgstr "支付"

#: src/Logger/Log.php:97
msgid "Errors"
msgstr "错误"

#: src/Admin/Builder/Help.php:252
msgid "Extending"
msgstr "扩展"

#: src/Admin/Builder/Help.php:251
msgid "Styling"
msgstr "风格"

#: src/Admin/Builder/Help.php:250
msgid "Functionality"
msgstr "功能性"

#: src/Admin/SiteHealth.php:93
msgid "Not writable"
msgstr "不可写"

#: src/Admin/SiteHealth.php:93
msgid "Writable"
msgstr "可写"

#: src/Admin/SiteHealth.php:92
msgid "Uploads directory"
msgstr "上传目录"

#: src/Providers/Provider/Settings/FormBuilder.php:154
#: src/Providers/Provider/Settings/FormBuilder.php:209
msgid "--- Select Form Field ---"
msgstr "--- 选择表单字段 ---"

#: src/Integrations/Divi/WPFormsSelector.php:56
msgid "Select form"
msgstr "选择表单"

#: includes/fields/class-email.php:338
msgid "Denylist"
msgstr "拒绝列表"

#: includes/fields/class-email.php:337
msgid "Allowlist"
msgstr "允许列表"

#: includes/fields/class-email.php:324
msgid "Restrict which email addresses are allowed. Be sure to separate each email address with a comma."
msgstr "限制允许的电子邮箱地址。请务必用逗号分隔每个电子邮箱地址。"

#: includes/fields/class-email.php:323
msgid "Allowlist / Denylist"
msgstr "允许列表/拒绝列表"

#: includes/admin/ajax-actions.php:70
msgid "Something went wrong while saving the form."
msgstr "保存表单时出错。"

#: includes/admin/ajax-actions.php:27
msgid "Something went wrong while performing this action."
msgstr "执行此操作时出现问题。"

#: includes/admin/ajax-actions.php:22
msgid "You are not allowed to perform this action."
msgstr "您不允许执行此操作。"

#: includes/admin/ajax-actions.php:17
msgid "Your session expired. Please reload the builder."
msgstr "您的会话已过期。请重新加载。"

#: includes/admin/builder/class-builder.php:811
msgid "Help"
msgstr "帮助"

#: includes/admin/builder/class-builder.php:593
msgid "Please contact the plugin support team if this behavior persists."
msgstr "如果此问题持续存在，请联系插件支持团队。"

#: includes/admin/builder/class-builder.php:592
msgid "Something went wrong while saving the form. Please reload the page and try again."
msgstr "保存表单时出错。请重新加载页面并重试。"

#: src/Admin/Tools/Views/Logs.php:241
msgid "Log events for specific users only. All users are logged by default."
msgstr "仅记录特定用户的事件。默认情况下会记录所有用户的事件。"

#: src/Admin/Tools/Views/Logs.php:219
msgid "Users"
msgstr "用户"

#: src/Admin/Tools/Views/Logs.php:201
msgid "Select the user roles you want to log. All roles are logged by default."
msgstr "选择您要记录的用户角色。默认情况下，所有角色都会被记录。"

#: src/Admin/Tools/Views/Logs.php:180
msgid "User Roles"
msgstr "用户角色"

#: src/Admin/Tools/Views/Logs.php:163
msgid "Select the types of events you want to log. Everything is logged by default."
msgstr "选择您要记录的事件类型。默认情况下会记录所有事件。"

#: src/Admin/Tools/Views/Logs.php:147
msgid "Log Types"
msgstr "日志类型"

#: src/Admin/Tools/Views/Logs.php:106
msgid "Check this option to start logging WPForms-related events. This is recommended only while debugging."
msgstr "勾选该选项以开始记录WPForms相关事件。建议只在调试时使用该选项。"

#: src/Admin/Tools/Views/Logs.php:100
msgid "Enable Logs"
msgstr "启用日志"

#: src/Admin/Tools/Views/Logs.php:94
msgid "On this page, you can enable and configure the logging functionality while debugging behavior of various parts of the plugin, including forms and entries processing."
msgstr "在这个页面上，您可以启用和配置日志功能，同时调试插件各部分的行为，包括表单和条目的处理。"

#: includes/admin/class-settings.php:415 includes/class-frontend.php:1639
#: includes/fields/class-email.php:565 includes/fields/class-email.php:576
msgid "This email address is not allowed."
msgstr "不允许使用此电子邮箱地址。"

#: includes/admin/class-settings.php:413
msgid "Email Restricted"
msgstr "电子邮箱地址受限"

#: templates/builder/help.php:119
msgid "Unfortunately the error occurred while downloading help data."
msgstr "抱歉，下载帮助数据时发生错误。"

#: templates/builder/help.php:109
msgid "Docs"
msgstr "文档"

#: templates/builder/help.php:109
msgid "View All"
msgstr "查看全部"

#: templates/builder/help.php:74
msgid "Upgrade to WPForms Pro to access our world class customer support."
msgstr "升级到WPForms Pro以获得我们的世界级客户支持。"

#: templates/builder/help.php:70
msgid "Submit a Support Ticket"
msgstr "提交支持工单"

#: templates/builder/help.php:65
msgid "Submit a ticket and our world class support team will be in touch soon."
msgstr "提交一张工单，我们的世界级支持团队将很快与您联系。"

#: templates/builder/help.php:62
msgid "Get Support"
msgstr "获得支持"

#: templates/builder/help.php:56
msgid "View All Documentation"
msgstr "查看所有文档"

#: templates/builder/help.php:51
msgid "Browse documentation, reference material, and tutorials for WPForms."
msgstr "浏览WPForms的文档、参考资料和教程。"

#: templates/builder/help.php:50
msgid "View Documentation"
msgstr "查看文档"

#: templates/builder/help.php:39
msgid "No docs found"
msgstr "未找到文档"

#: templates/builder/help.php:31
msgid "Clear"
msgstr "清除"

#: templates/builder/help.php:30
msgid "Ask a question or search the docs..."
msgstr "提出问题或搜索文档... ..."

#: includes/admin/class-about.php:355
msgid "WordPress.org"
msgstr "WordPress.org"

#: includes/admin/ajax-actions.php:525
msgid "There was an error while performing your request."
msgstr "在执行您的请求时出现了错误。"

#: includes/admin/ajax-actions.php:474
msgid "Plugin activation is disabled for you on this site."
msgstr "您在此站点上禁止了插件启用。"

#: includes/admin/ajax-actions.php:438
msgid "Plugin deactivation is disabled for you on this site."
msgstr "您在此站点上禁止了插件禁用。"

#: includes/admin/builder/panels/class-fields.php:468
msgid "Take your pick from our wide variety of fields and start building out your form!"
msgstr "从我们丰富的选项中挑选，开始建立您的表单吧!"

#: includes/fields/class-number-slider.php:62
msgid "Increment value should be greater than zero. Decimal fractions allowed."
msgstr "增量值应大于零。允许使用小数点。"

#. translators: %1$s - link to a site; %2$s - link to the documentation.
#: templates/emails/summary-footer.php:25
msgid "This email was auto-generated and sent from %1$s. Learn <a href=\"%2$s\">how to disable</a>"
msgstr "此邮件由%1$s生成并自动发送。了解<a href=\"%2$s\">如何禁用</a>"

#. translators: %s - link to the documentation.
#: templates/emails/summary-footer-plain.php:23
msgid "Learn how to disable: %s."
msgstr "了解如何禁用：%s。"

#. translators: %s - link to a site.
#: templates/emails/summary-footer-plain.php:18
msgid "This email was auto-generated and sent from %s."
msgstr "此邮件由%s生成并自动发送。"

#. translators: %1$s - Video tutorial toggle CSS classes, %2$s - shortcode
#. toggle CSS classes.
#: templates/admin/form-embed-wizard/popup.php:49
msgid "You can also <a href=\"#\" class=\"%1$s\">embed your form manually</a> or <a href=\"#\" class=\"%2$s\">use a shortcode</a>"
msgstr "您也可以<a href=\"#\" class=\"%1$s\">手动嵌入您的表单</a>或<a href=\"#\" class=\"%2$s\">使用简码</a>。"

#: templates/admin/form-embed-wizard/popup.php:32
msgid "What would you like to call the new page?"
msgstr "您想给新页面起什么名字？"

#: templates/admin/form-embed-wizard/popup.php:29
msgid "Select the page you would like to embed your form in."
msgstr "选择要嵌入表单的页面。"

#: templates/admin/form-embed-wizard/popup.php:20
msgid "We can help embed your form with just a few clicks!"
msgstr "只需单击几下，我们就可以帮助您嵌入表单！"

#: templates/admin/empty-states/no-forms.php:17
msgid "It looks like you haven’t created any forms yet."
msgstr "看起来您还没有创建任何表单。"

#: src/Admin/Pages/SMTP.php:380
msgid "WP Mail SMTP on WordPress.org"
msgstr "WP Mail SMTP on WordPress.org"

#: src/Admin/Pages/Analytics.php:415
msgid "MonsterInsights on WordPress.org"
msgstr "MonsterInsights on WordPress.org"

#. translators: placeholders are links.
#: src/Forms/Token.php:283
msgid "Please check out our %1$stroubleshooting guide%2$s for details on resolving this issue."
msgstr "请查看我们的%1$s故障排除指南%2$s，以了解解决此问题的详细信息。"

#: src/Forms/Token.php:235
msgid "Form token is invalid. Please refresh the page."
msgstr "表单令牌无效。请刷新页面。"

#: src/Forms/Token.php:223
msgid "This page isn't loading JavaScript properly, and the form will not be able to submit."
msgstr "此页面未正确加载JavaScript，表单将无法提交。"

#: src/Integrations/Elementor/Widget.php:354
msgid "Select a form"
msgstr "选择表单"

#: src/Integrations/Elementor/Widget.php:211
msgid "Display Options"
msgstr "显示选项"

#: src/Admin/Builder/Templates.php:452
#: src/Integrations/Elementor/Widget.php:201
msgid "New form"
msgstr "新表单"

#. translators: %s - WPForms documentation link.
#: src/Integrations/Elementor/Widget.php:173
msgid "<b>Heads up!</b> Don't forget to test your form. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Check out our complete guide!</a>"
msgstr "<b>注意！</b>别忘了测试您的表单。<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">查看我们的完整指南！</a>。"

#. translators: %s - WPForms documentation link.
#: src/Integrations/Elementor/Widget.php:157
msgid "Need to make changes? <a href=\"#\">Edit the selected form.</a>"
msgstr "需要修改吗？<a href=\"#\">编辑所选的表单</a>。"

#: src/Integrations/Elementor/Widget.php:129
msgid "<b>You haven't created a form yet.</b><br> What are you waiting for?"
msgstr "<b>您还没有创建表单。</b><br>还在等什么？"

#: includes/class-process.php:381
msgid "Spam Entry "
msgstr "垃圾条目"

#. translators: %s - Refresh link.
#: includes/admin/settings-api.php:177
msgid "If your license has been upgraded or is incorrect, then please %1$sforce a refresh%2$s."
msgstr "如果您的许可证已经升级或不正确，请%1$s强制刷新%2$s。"

#: includes/admin/builder/panels/class-settings.php:135
msgid "Enable anti-spam protection"
msgstr "启用反垃圾内容保护"

#: includes/admin/builder/class-builder.php:587
msgid "Add Custom Value"
msgstr "添加自定义值"

#: includes/admin/builder/class-builder.php:542
msgid "Your form contains unsaved changes. Would you like to save your changes first."
msgstr "您的表单包含未保存的更改。是否先保存更改。"

#: includes/admin/builder/class-builder.php:525
msgid "Save and Embed"
msgstr "保存并嵌入"

#: includes/admin/class-settings.php:439 includes/class-frontend.php:1661
msgid "Please fill out all blanks."
msgstr "请填完所有的空。"

#: includes/admin/class-settings.php:437
msgid "Input Mask Incomplete"
msgstr "所输入的掩码不完整"

#. translators: %s - WPForms.com smart tags documentation URL.
#: includes/admin/class-settings.php:374
msgid "%1$s These messages are displayed to the users as they fill out a form in real-time. Messages can include plain text and/or %2$sSmart Tags%3$s."
msgstr "%1$s这些消息会在用户填写表单时实时显示给他们。消息可以包括纯文本和/或%2$sSmart Tags%3$s。"

#: src/Admin/Settings/Captcha.php:228
msgid "Check this option to forcefully remove other CAPTCHA occurrences in order to prevent conflicts. Only enable this option if your site is having compatibility issues or instructed by support."
msgstr "勾选此选项可强制删除所有其他验证码以防止冲突。只在您的站点出现兼容性问题或支持人员指示的情况下才进行此操作。"

#: src/Admin/Settings/Captcha.php:201 src/Admin/Settings/Captcha.php:208
msgid "Displays to users who fail the verification process."
msgstr "显示给未通过验证的用户。"

#. translators: %s - WPForms.com form styling setting URL.
#: includes/admin/class-settings.php:271
msgid "Determines which CSS files to load and use for the site (<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">please see our tutorial for full details</a>). \"Base and Form Theme Styling\" is recommended, unless you are experienced with CSS or instructed by support to change settings. "
msgstr "确定站点要加载和使用的CSS文件（<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">请查看我们的教程了解详细信息</a>）。建议使用 \"基础和表单主题样式\"，除非您对CSS有经验或由支持人员指导更改设置。"

#. translators: %d - choice number.
#. translators: %d - Choice item number.
#: includes/fields/class-base.php:475 includes/fields/class-base.php:510
#: includes/fields/class-base.php:1806 includes/fields/class-base.php:1847
msgid "Choice %d"
msgstr "选择%d"

#. translators: %s - URL to the documentation article.
#: templates/admin/empty-states/no-forms.php:32
#: templates/integrations/elementor/no-forms.php:32
msgid "Need some help? Check out our <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">comprehensive guide</a>."
msgstr "需要帮助吗？请查看我们的 <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">综合指南</a>。"

#: templates/integrations/elementor/no-forms.php:26
msgid "Create a form"
msgstr "创建表单"

#: templates/integrations/elementor/no-forms.php:20
msgid "You can use <b>WPForms</b> to build contact forms, surveys, payment forms, and more with just a few clicks."
msgstr "只需点击几下，您可以使用<b>WPForms</b>来建立联系表单、调查问卷、支付表单等。"

#: templates/admin/challenge/modal.php:75
msgid "End Challenge"
msgstr "结束检查"

#: templates/admin/challenge/modal.php:74
msgid "Continue"
msgstr "继续"

#: templates/admin/challenge/modal.php:73
msgid "Pause"
msgstr "暂停"

#: templates/admin/challenge/modal.php:43
msgid "Challenge Complete"
msgstr "检查完成"

#: templates/admin/challenge/modal.php:41
msgid "Check Notifications"
msgstr "检查通知"

#: templates/admin/challenge/modal.php:21
msgid "Toggle list"
msgstr "切换列表"

#: templates/admin/challenge/builder.php:41
msgid "Our form builder is a full-screen, distraction-free experience where you manage your forms. The following steps will walk you through essential areas."
msgstr "我们的表单构建器是一个全屏、无干扰的体验，在这里您可以管理您的表单。下面的步骤将引导您了解基本的信息。"

#: templates/admin/challenge/builder.php:40
msgid "Welcome to the Form Builder"
msgstr "欢迎来到表单构建器"

#: templates/admin/challenge/builder.php:26
msgid "You can add additional fields to your form, if you need them."
msgstr "如果需要，可以向表单中添加其他字段。"

#: templates/admin/challenge/builder.php:42
#: templates/admin/form-embed-wizard/popup.php:41
msgid "Let’s Go!"
msgstr "我们继续吧！"

#: templates/admin/form-embed-wizard/popup.php:40
msgid "Name Your Page"
msgstr "命名您的页面"

#: src/Admin/FormEmbedWizard.php:339
msgid "Select a Page"
msgstr "选择一个页面"

#: templates/admin/form-embed-wizard/popup.php:36
msgid "Create New Page"
msgstr "创建新页面"

#: templates/admin/form-embed-wizard/popup.php:35
msgid "Select Existing Page"
msgstr "选择现有页面"

#: templates/admin/form-embed-wizard/popup.php:23
msgid "Would you like to embed your form in an existing page, or create a new one?"
msgstr "您想将表单嵌入现有页面还是创建新页面？"

#. translators: %s - Link to the WPForms documentation page.
#: templates/admin/challenge/embed.php:24
#: templates/admin/form-embed-wizard/tooltip.php:22
msgid "Click the plus button, search for WPForms, click the block to<br>embed it. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Learn More</a>."
msgstr "点击添加按钮，搜索WPForms，点击区块以<br>将其嵌入。<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">了解更多</a>。"

#: src/Admin/Education/Fields.php:227
msgid "Authorize.Net"
msgstr "Authorize.Net"

#: src/Integrations/UsageTracking/UsageTracking.php:111
msgid "By allowing us to track usage data, we can better help you, as we will know which WordPress configurations, themes, and plugins we should test."
msgstr "通过允许我们追踪使用数据可更好地为您提供帮助，此选项可以让我们知道应该用哪些WordPress配置、主题和插件进行测试。"

#: src/Integrations/UsageTracking/UsageTracking.php:110
msgid "Allow Usage Tracking"
msgstr "允许追踪使用情况"

#. translators: %d - field ID.
#: includes/admin/builder/functions.php:243
msgid "Field #%d"
msgstr "字段#%d"

#: src/Admin/Tools/Views/ActionScheduler.php:59
#: src/Admin/Tools/Views/ActionScheduler.php:85
msgid "Scheduled Actions"
msgstr "计划操作"

#: includes/admin/class-welcome.php:270
msgid "File Uploads"
msgstr "文件上传"

#: includes/admin/class-welcome.php:263
msgid "Advanced Fields"
msgstr "高级字段"

#: includes/fields/class-base.php:2205
msgid "Only values matching specific conditions can be added."
msgstr "只能添加与特定条件匹配的值。"

#: includes/fields/class-base.php:2204
msgid "Only unique values can be added."
msgstr "只能添加唯一的值。"

#: includes/fields/class-base.php:2203
msgid "Press to select."
msgstr "点击选择"

#: includes/fields/class-base.php:2202
msgid "No choices to choose from."
msgstr "没有选择。"

#: includes/fields/class-base.php:2201
msgid "No results found."
msgstr "未找到结果。"

#: includes/fields/class-select.php:289
msgid "Classic style is the default one generated by your browser. Modern has a fresh look and displays all selected options in a single row."
msgstr "经典样式是浏览器生成的默认样式。Modern具有全新的外观，并在一行中显示所有选择的选项。"

#: includes/fields/class-select.php:288
msgid "Style"
msgstr "样式"

#: includes/fields/class-select.php:255
msgid "Allow users to select multiple choices in this field."
msgstr "允许用户在此字段中选择多个选项。"

#: includes/fields/class-select.php:254
msgid "Multiple Options Selection"
msgstr "多选项选择"

#: includes/functions.php:969
msgid "North Macedonia (Republic of)"
msgstr "北马其顿"

#: includes/admin/class-settings.php:501
msgid "Hide Admin Bar Menu"
msgstr "隐藏管理栏菜单"

#: includes/admin/class-settings.php:335
msgid "Check this option to enable sending emails asynchronously, which can make submission processing faster."
msgstr "勾选此选项以启用异步发送邮件，这样能使提交处理速度更快。"

#: includes/admin/class-settings.php:334
msgid "Optimize Email Sending"
msgstr "优化电子邮件发送"

#: includes/admin/builder/class-builder.php:478
msgid "Are you sure you want to leave? You have unsaved changes"
msgstr "您确定要离开吗？您有未保存的更改"

#: src/Admin/AdminBarMenu.php:299
msgid "Support"
msgstr "支持"

#: src/Admin/Notifications.php:440
msgid "Next message"
msgstr "下一条消息"

#: src/Admin/Notifications.php:436
msgid "Previous message"
msgstr "上一条信息"

#: src/Admin/Education/Core.php:126
msgid "Upgrade to Elite"
msgstr "升级到Elite版"

#: src/Admin/Education/Core.php:124
msgid "We're sorry, the %name% is not available on your plan. Please upgrade to the Elite plan to unlock all these awesome features."
msgstr "很抱歉，您的计划中无法使用%name%。请升级到Elite计划，以解锁所有这些超赞功能。"

#: src/Admin/Education/Core.php:123
msgid "is an Elite Feature"
msgstr "是一项Elite版功能"

#. translators: %s - license level, WPForms Pro or WPForms Elite.
#: includes/admin/admin.php:476
msgid "Thanks for your interest in %s!"
msgstr "谢谢您对 %s 感兴趣!"

#: includes/admin/class-about.php:1420
msgid "Pro Addons Included"
msgstr "包括专业版扩展"

#: includes/admin/class-about.php:1256 includes/admin/class-about.php:1270
#: includes/admin/class-about.php:1284
msgid "ActiveCampaign, Constant Contact, Mailchimp, AWeber, GetResponse, Campaign Monitor, Salesforce, Sendinblue, and Drip"
msgstr "ActiveCampaign、Constant Contact、Mailchimp、AWeber、GetResponse、Campaign Monitor、Salesforce、Sendinblue和Drip"

#: includes/admin/class-about.php:1234 includes/admin/class-about.php:1241
msgid "Additional Marketing Integrations"
msgstr "附加营销整合"

#. translators: %s - WPForms doc link.
#: src/Forms/Preview.php:209
msgid "For form testing tips, check out our <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">complete guide!</a>"
msgstr "有关表单测试的提示，请查看我们的<a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">完整指南</a>"

#: src/Admin/AdminBarMenu.php:323 src/Admin/Builder/Shortcuts.php:55
#: src/Forms/Preview.php:177
msgid "View Entries"
msgstr "查看条目"

#: src/Admin/AdminBarMenu.php:322 src/Forms/Preview.php:161
msgid "Edit Form"
msgstr "编辑表单"

#: includes/admin/class-menu.php:264
msgid "Go to WPForms Lite vs Pro comparison page"
msgstr "转到 WPForms 精简版和专业版比较页面"

#: includes/admin/class-menu.php:249
msgid "Go to WPForms Settings page"
msgstr "转到 WPForms 设置页面"

#: src/Integrations/Gutenberg/FormSelector.php:253
msgid "The form cannot be displayed."
msgstr "该表单无法显示。"

#: lite/templates/education/builder/did-you-know.php:21
msgid "Did You Know?"
msgstr "您知道吗？"

#: lite/templates/education/builder/did-you-know.php:30
msgid "Upgrade to Pro"
msgstr "升级至Pro"

#: src/Lite/Admin/Education/Builder/DidYouKnow.php:74
msgid "You can have multiple confirmations with conditional logic."
msgstr "您可以使用条件逻辑进行多次确认。"

#: src/Lite/Admin/Education/Builder/DidYouKnow.php:61
msgid "You can have multiple notifications with conditional logic."
msgstr "您可以具有条件逻辑的多个通知。"

#: src/Lite/Admin/Settings/Access.php:219
msgid "Custom access to the following capabilities…"
msgstr "自定义访问以下功能…"

#: src/Lite/Admin/Settings/Access.php:215
msgid "Delete Others Forms Entries"
msgstr "删除其他表单条目"

#: src/Lite/Admin/Settings/Access.php:214
msgid "Delete Forms Entries"
msgstr "删除表单条目"

#: src/Lite/Admin/Settings/Access.php:213
msgid "Edit Others Forms Entries"
msgstr "编辑其他表单条目"

#: src/Lite/Admin/Settings/Access.php:212
msgid "Edit Forms Entries"
msgstr "编辑表单条目"

#: src/Lite/Admin/Settings/Access.php:209
msgid "View Others Forms Entries"
msgstr "查看其他表单条目"

#: src/Lite/Admin/Settings/Access.php:208
msgid "View Forms Entries"
msgstr "查看表单条目"

#: src/Lite/Admin/Settings/Access.php:207
msgid "Delete Others Forms"
msgstr "删除其他表单"

#: src/Lite/Admin/Settings/Access.php:206
msgid "Delete Forms"
msgstr "删除表单"

#: src/Lite/Admin/Settings/Access.php:203
msgid "View Others Forms"
msgstr "查看其他表单"

#: src/Lite/Admin/Settings/Access.php:202
msgid "View Forms"
msgstr "查看表单"

#: src/Lite/Admin/Settings/Access.php:201
msgid "Edit Others Forms"
msgstr "编辑其他表单"

#: src/Lite/Admin/Settings/Access.php:200
msgid "Edit Forms"
msgstr "编辑表单"

#: src/Lite/Admin/Settings/Access.php:199
msgid "Create Forms"
msgstr "创建表单"

#: src/Lite/Admin/Settings/Access.php:183
#: src/Lite/Admin/Settings/Access.php:184
msgid "User Role Editor Integration"
msgstr "User Role Editor集成"

#: src/Lite/Admin/Settings/Access.php:173
#: src/Lite/Admin/Settings/Access.php:174
msgid "Members Integration"
msgstr "Members集成"

#: src/Lite/Admin/Settings/Access.php:163
#: src/Lite/Admin/Settings/Access.php:164
msgid "Simple Built-in Controls"
msgstr "简单内置控件"

#: src/Lite/Admin/Settings/Access.php:138
msgid "You can easily grant or restrict access using the simple built-in controls, or use our official integrations with Members and User Role Editor plugins."
msgstr "您可以使用简单的内置控件轻松授予或限制访问，或者使用我们与Members和User Role Editor插件的官方集成。"

#: src/Lite/Admin/Settings/Access.php:137
msgid "Access controls allows you to manage and customize access to WPForms functionality."
msgstr "允许您管理和自定义角色对 WPForms 功能的访问。"

#: src/Lite/Admin/Settings/Access.php:136
#: templates/education/admin/settings/geolocation/heading.php:20
msgid "Pro+"
msgstr "Pro+"

#: src/Lite/Admin/Settings/Access.php:134
msgid "Access Controls"
msgstr "权限控制"

#: src/Lite/Admin/Settings/Access.php:84
msgid "Access"
msgstr "访问"

#: includes/admin/builder/class-builder.php:586
msgid "You should enter a valid absolute address to the Confirmation Redirect URL field."
msgstr "您应该在确认重定向网址字段中输入有效的绝对地址。"

#: includes/admin/builder/class-builder.php:564
msgid "This form is currently accepting payments. Entry storage is required to accept payments. To disable entry storage, please first disable payments."
msgstr "此表单当前正在接受付款。接受付款需要输入存储器。若要禁用条目存储，请首先禁用付款。"

#: includes/admin/builder/class-builder.php:563
msgid "Entry storage is currently disabled, but is required to accept payments. Please enable in your form settings."
msgstr "当前已禁用条目存储，但必须接受付款。请在表单设置中启用。"

#: includes/admin/builder/class-builder.php:117
msgid "Sorry, you are not allowed to edit this form."
msgstr "抱歉，您不能编辑此表单。"

#: includes/admin/builder/class-builder.php:113
msgid "Sorry, you are not allowed to create new forms."
msgstr "抱歉，您不能创建新表单。"

#: includes/admin/ajax-actions.php:401
msgid "You do not have permission to perform this operation."
msgstr "您没有权限执行此操作。"

#: src/Admin/Tools/Views/Import.php:253
msgid "Form data cannot be imported."
msgstr "无法导入表单数据。"

#. translators: %s - license level, WPForms Pro or WPForms Elite.
#: includes/admin/admin.php:497
msgid "After purchasing a license,<br>just <strong>enter your license key on the WPForms Settings page</strong>.<br>This will let your site automatically upgrade to %s!"
msgstr "在购买许可证之后，<br>只需 <strong>在WPForms设置页面上输入您的许可证密钥</strong>。<br>即可让您的网站自动升级到 %s！"

#: includes/admin/class-about.php:948
msgid "RafflePress Pro"
msgstr "RafflePress Pro"

#: includes/admin/class-about.php:941
msgid "RafflePress"
msgstr "RafflePress"

#: wpforms.php:149
msgid "Your site already has WPForms Pro activated. If you want to switch to WPForms Lite, please first go to Plugins → Installed Plugins and deactivate WPForms. Then, you can activate WPForms Lite."
msgstr "您的站点已启用WPForms Pro。如果您想切換到WPForms Lite，請转到插件 → 已安裝插件並禁用WPForms。然后，您可以启用WPForms Lite。"

#: includes/class-frontend.php:455
msgid "Please enable JavaScript in your browser to complete this form."
msgstr "请在浏览器中启用JavaScript来完成此表单。"

#: includes/admin/class-about.php:1506
msgid "5 Sites"
msgstr "5个站点"

#: includes/admin/class-about.php:1500
msgid "3 Sites"
msgstr "3个站点"

#: includes/admin/class-about.php:1494
msgid "1 Site"
msgstr "1个站点"

#: includes/admin/class-about.php:1474 includes/admin/class-about.php:1480
#: includes/admin/class-about.php:1486 includes/admin/class-menu.php:265
msgid "Premium Support"
msgstr "高级支持"

#. translators: %s - next license level.
#: includes/admin/class-about.php:827
msgid "Get WPForms %s Today and Unlock all the Powerful Features"
msgstr "立即获取WPForms%s并解锁所有强大的功能"

#. translators: %1$s - current license type; %2$s - suggested license type.
#: includes/admin/class-about.php:128
msgid "%1$s vs %2$s"
msgstr "%1$s vs %2$s"

#: includes/admin/class-about.php:83
msgid "Number of Sites"
msgstr "站点数量"

#: includes/admin/class-menu.php:141 includes/admin/class-menu.php:142
msgid "SMTP"
msgstr "SMTP"

#: includes/admin/builder/panels/class-fields.php:143
msgid "Enabled"
msgstr "激活"

#: includes/admin/builder/class-builder.php:764
#: includes/admin/builder/class-builder.php:771
#: templates/admin/form-embed-wizard/popup.php:72
msgid "Go back"
msgstr "返回"

#: includes/admin/builder/class-builder.php:769
msgid "Oh, hi there!"
msgstr "哦，您好！"

#: includes/fields/class-number-slider.php:445
msgid "Please provide a valid value."
msgstr "请提供有效值。"

#: includes/fields/class-number-slider.php:287
msgid "Determines the increment between selectable values on the slider."
msgstr "确定滑块上可选择值之间的增量。"

#: includes/fields/class-number-slider.php:286
msgid "Increment"
msgstr "增量"

#: includes/fields/class-number-slider.php:393
msgid "Selected Value: {value}"
msgstr "所选值：{value}"

#: includes/fields/class-number-slider.php:255
msgid "Displays the currently selected value below the slider."
msgstr "在滑块下方显示当前选定的值。"

#: includes/fields/class-number-slider.php:254
msgid "Value Display"
msgstr "数值显示"

#: includes/fields/class-number-slider.php:217
msgid "Enter a default value for this field."
msgstr "输入此字段的默认值。"

#: includes/fields/class-number-slider.php:144
msgid "Define the minimum and the maximum values for the slider."
msgstr "定义滑块的最小值和最大值。"

#: includes/fields/class-number-slider.php:143
msgid "Value"
msgstr "值"

#. translators: %1$s - Number slider selected value; %2$s - its minimum value;
#. %3$s - its maximum value.
#: includes/fields/class-number-slider.php:94
msgid "%1$s (%2$s min / %3$s max)"
msgstr "%1$s（最少%2$s / 最多%3$s）"

#: includes/fields/class-number-slider.php:39
msgid "Number Slider"
msgstr "数字滑块"

#: templates/fields/number-slider/builder-option-min-max.php:8
msgid "Maximum"
msgstr "最大"

#: templates/fields/number-slider/builder-option-min-max.php:4
msgid "Minimum"
msgstr "最小"

#: src/Admin/Pages/SMTP.php:173 src/Admin/Pages/SMTP.php:421
msgid "Go to SMTP settings"
msgstr "转到SMTP设置"

#: src/Admin/Pages/SMTP.php:416
msgid "Start Setup"
msgstr "开始安装"

#: src/Admin/Pages/SMTP.php:387
msgid "Activate WP Mail SMTP"
msgstr "激活 WP Mail SMTP"

#: src/Admin/Pages/SMTP.php:372
msgid "Install WP Mail SMTP"
msgstr "安装 WP Mail SMTP"

#: src/Admin/Pages/SMTP.php:343
msgid "Select and configure your mailer."
msgstr "选择并配置您的邮件程序。"

#: src/Admin/Pages/SMTP.php:342
msgid "Set Up WP Mail SMTP"
msgstr "设置 WP Mail SMTP"

#: src/Admin/Pages/SMTP.php:362
msgid "Install WP Mail SMTP from the WordPress.org plugin repository."
msgstr "从WordPress.org插件存储库安装 WP Mail SMTP。"

#: src/Admin/Pages/SMTP.php:361
msgid "Install and Activate WP Mail SMTP"
msgstr "安装并激活 WP Mail SMTP"

#: src/Admin/Pages/SMTP.php:241
msgid "WP Mail SMTP screenshot"
msgstr "WP Mail SMTP屏幕截图"

#: src/Admin/Pages/SMTP.php:214
msgid "Making Email Deliverability Easy for WordPress"
msgstr "使WordPress的电子邮件可传递性变得容易"

#: src/Admin/Pages/SMTP.php:213
msgid "WPForms ♥ WP Mail SMTP"
msgstr "WPForms ♥ WP Mail SMTP"

#: src/Admin/Pages/SMTP.php:164 src/Admin/Pages/SMTP.php:387
msgid "WP Mail SMTP Installed & Activated"
msgstr "WP Mail SMTP已安装并激活"

#: src/Admin/Pages/Analytics.php:533 src/Admin/Pages/SMTP.php:452
msgid "Plugin unavailable."
msgstr "插件不可用。"

#: src/Admin/Pages/Analytics.php:454
msgid "Setup Complete"
msgstr "设置完成"

#: src/Admin/Pages/Analytics.php:448
msgid "Run Setup Wizard"
msgstr "再次运行安装向导"

#: src/Admin/Pages/Analytics.php:421
msgid "Activate MonsterInsights"
msgstr "激活 MonsterInsights"

#: src/Admin/Pages/Analytics.php:407
msgid "Install MonsterInsights"
msgstr "安装 MonsterInsights"

#: src/Admin/Pages/Analytics.php:379
msgid "With the MonsterInsights Form addon you can easily track your form views, entries, conversion rates, and more."
msgstr "使用MonsterInsights表单扩展，您可以轻松地跟踪您的表单浏览量、条目、转换率等。"

#: src/Admin/Pages/Analytics.php:378
msgid "Get Form Conversion Tracking"
msgstr "获取表单转换跟踪"

#: src/Admin/Pages/Analytics.php:377
msgid "Step 3"
msgstr "步骤 3"

#: src/Admin/Pages/Analytics.php:343
msgid "MonsterInsights has an intuitive setup wizard to guide you through the setup process."
msgstr "MonsterInsights具有直观的设置向导，可指导您完成设置过程。"

#: src/Admin/Pages/Analytics.php:342
msgid "Setup MonsterInsights"
msgstr "设置 MonsterInsights"

#: src/Admin/Pages/Analytics.php:341 src/Admin/Pages/SMTP.php:341
msgid "Step 2"
msgstr "步骤 2"

#: src/Admin/Pages/Analytics.php:396
msgid "Install & Activate MonsterInsights"
msgstr "安装 + 激活MonsterInsights"

#: src/Admin/Pages/Analytics.php:307 src/Admin/Pages/SMTP.php:307
msgid "Step 1"
msgstr "步骤 1"

#: src/Admin/Pages/Analytics.php:246
msgid "Automatic integration with WPForms."
msgstr "与WPForms自动集成。"

#: src/Admin/Pages/Analytics.php:245
msgid "Complete UTM tracking with form entries."
msgstr "使用表单条目完成UTM跟踪。"

#: src/Admin/Pages/Analytics.php:244
msgid "View form conversion rates from WordPress."
msgstr "从WordPress查看表单转换率。"

#: src/Admin/Pages/Analytics.php:243 src/Admin/Pages/Analytics.php:397
msgid "Track form impressions and conversions."
msgstr "跟踪表单的展示次数和转化次数。"

#: src/Admin/Pages/Analytics.php:241
msgid "Analytics screenshot"
msgstr "分析屏幕截图"

#: src/Admin/Pages/Analytics.php:215
msgid "MonsterInsights connects WPForms to Google Analytics, providing a powerful integration with their Forms addon. MonsterInsights is a sister company of WPForms."
msgstr "MonsterInsights将WPForms与Google Analytics进行连接，为其表单扩展提供了强大的集成。MonsterInsights是WPForms的姊妹公司。"

#: src/Admin/Pages/Analytics.php:214
msgid "The Best Google Analytics Plugin for WordPress"
msgstr "WordPress的最佳Google Analytics插件"

#: src/Admin/Pages/Analytics.php:213
msgid "WPForms ♥ MonsterInsights"
msgstr "WPForms ♥ MonsterInsights"

#: src/Admin/Pages/Analytics.php:171 src/Admin/Pages/SMTP.php:168
msgid "Go to Plugins page"
msgstr "转到插件页面"

#: src/Admin/Pages/Analytics.php:170 src/Admin/Pages/SMTP.php:167
msgid "Download Now"
msgstr "现在下载"

#: src/Admin/Pages/Analytics.php:168 src/Admin/Pages/Analytics.php:498
#: src/Admin/Pages/SMTP.php:165
#: src/Integrations/UncannyAutomator/UncannyAutomator.php:118
msgid "Install Now"
msgstr "现在安装"

#: src/Admin/Pages/Analytics.php:167 src/Admin/Pages/Analytics.php:421
msgid "MonsterInsights Installed & Activated"
msgstr "MonsterInsights 已安装并激活"

#: src/Admin/Pages/Analytics.php:166 src/Admin/Pages/SMTP.php:163
msgid "Activating..."
msgstr "启用中…"

#: src/Admin/Pages/Analytics.php:165 src/Admin/Pages/SMTP.php:162
msgid "Installing..."
msgstr "正在安装…"

#: src/Admin/FlyoutMenu.php:63
msgid "See Quick Links"
msgstr "查看快速链接"

#: src/Admin/FlyoutMenu.php:124
msgid "Join Our Community"
msgstr "加入我们的社区"

#: src/Admin/FlyoutMenu.php:119
msgid "Support & Docs"
msgstr "支持和文档"

#: lite/templates/education/admin/notice-bar.php:34
#: lite/templates/education/builder/did-you-know.php:31
#: lite/wpforms-lite.php:91
msgid "Dismiss this message."
msgstr "关闭此消息。"

#. translators: %s - WPForms.com Upgrade page URL.
#: lite/templates/education/admin/notice-bar.php:21
msgid "You're using WPForms Lite. To unlock more features consider <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">upgrading to Pro</a>."
msgstr "您正在使用WPForms Lite。要解锁更多的功能，请考虑<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">升级到Pro</a>。"

#. translators: %s - CAPTCHA name.
#: src/Admin/Education/Builder/Captcha.php:176
msgid "%s has been enabled for this form. Don't forget to save your form!"
msgstr "已为此表单启用%s。不要忘记保存表单！"

#. translators: %1$s - CAPTCHA settings page URL; %2$s - WPForms.com doc URL;
#. %3$s - CAPTCHA name.
#: src/Admin/Education/Builder/Captcha.php:159
msgid "The %3$s settings have not been configured yet. Please complete the setup in your <a href=\"%1$s\" target=\"_blank\">WPForms Settings</a>, and check out our <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">step by step tutorial</a> for full details."
msgstr "%3$s的设置尚未配置。请在<a href=\"%1$s\" target=\"_blank\">WPForms设置页面</a>中完成配置，并查看我们的<a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">分步教程</a>以了解详细信息。"

#: src/Admin/Education/Builder/Captcha.php:129
msgid "Google v3 reCAPTCHA"
msgstr "谷歌v3 reCAPTCHA"

#: src/Admin/Education/Builder/Captcha.php:128
msgid "Google Invisible v2 reCAPTCHA"
msgstr "谷歌隐形V2 reCAPTCHA"

#: src/Admin/Education/Builder/Captcha.php:127
msgid "Google Checkbox v2 reCAPTCHA"
msgstr "谷歌复选框V2 reCAPTCHA"

#: src/Admin/Education/Builder/Captcha.php:149
msgid "Something wrong. Please, try again later."
msgstr "出错了，请稍后再试。"

#: src/Admin/Education/Builder/Captcha.php:66
msgid "No form ID found."
msgstr "找不到表单ID。"

#: includes/admin/class-welcome.php:272
msgid "Conversational Forms"
msgstr "会话表单"

#: includes/admin/class-welcome.php:264 src/Logger/Log.php:95
msgid "Conditional Logic"
msgstr "条件逻辑"

#. translators: %s - limit words number.
#: includes/fields/class-text.php:497 includes/fields/class-textarea.php:352
msgid "Text can't exceed %d word."
msgid_plural "Text can't exceed %d words."
msgstr[0] "文本不能超过 %d 个字。"

#. translators: %s - limit characters number.
#: includes/fields/class-text.php:491 includes/fields/class-textarea.php:346
msgid "Text can't exceed %d character."
msgid_plural "Text can't exceed %d characters."
msgstr[0] "文本不能超过 %d 个字符。"

#: includes/fields/class-text.php:276 includes/fields/class-textarea.php:150
msgid "Words"
msgstr "单词"

#: includes/fields/class-text.php:275 includes/fields/class-textarea.php:149
msgid "Characters"
msgstr "字符"

#: includes/fields/class-text.php:244 includes/fields/class-textarea.php:119
msgid "Check this option to limit text length by characters or words count."
msgstr "选中此选项可按字符或字数限制文本长度。"

#: includes/fields/class-text.php:243 includes/fields/class-textarea.php:118
msgid "Limit Length"
msgstr "长度范围"

#: includes/admin/class-menu.php:161 includes/admin/class-menu.php:162
#: src/Admin/AdminBarMenu.php:276 src/Admin/Pages/Community.php:136
msgid "Community"
msgstr "社区"

#: src/Admin/Pages/Community.php:116
msgid "Do you have an idea or suggestion for WPForms? If you have thoughts on features, integrations, addons, or improvements - we want to hear it! We appreciate all feedback and insight from our users."
msgstr "您对WPForms有什么想法或建议吗？如果您有关于功能、集成、扩展或改进方面的想法或建议--我们希望听到您的想法和意见！我们感谢所有来自用户的反馈和见解。"

#: src/Admin/FlyoutMenu.php:129 src/Admin/Pages/Community.php:115
#: src/Admin/Pages/Community.php:117
msgid "Suggest a Feature"
msgstr "暗示一个特点"

#: src/Admin/Pages/Community.php:107
msgid "Join Translators Community"
msgstr "加入翻译社区"

#: src/Admin/Pages/Community.php:106
msgid "We're building a community of translators and i18n experts to translate WPForms. Sign up to our translator community newsletter to learn more and get information on how you can contribute!"
msgstr "我们正在建立一个由翻译人员和i18n专家组成的社区，以翻译WPForms。订阅我们的翻译社区时事通讯，以了解更多信息并获取有关您如何贡献的信息！"

#: src/Admin/Pages/Community.php:105
msgid "WPForms Translators Community"
msgstr "WPForms翻译者社区"

#: src/Admin/Pages/Community.php:97
msgid "Join WPBeginner Engage"
msgstr "加入WPBeginner集团"

#: src/Admin/Pages/Community.php:96
msgid "Hang out with other WordPress experts and like minded website owners such as yourself! Hosted by WPBeginner, the largest free WordPress site for beginners."
msgstr "与其他WordPress专家以及志趣相投的网站所有者（例如您）一起出去玩！由WPBeginner托管，WPBeginner是面向初学者的最大的免费WordPress网站。"

#: src/Admin/Pages/Community.php:95
msgid "WPBeginner Engage Facebook Group"
msgstr "WPBeginner经营Facebook集团"

#: src/Admin/Pages/Community.php:87
msgid "View WPForms Dev Docs"
msgstr "查看WPForms开发文档"

#: src/Admin/Pages/Community.php:86
msgid "Customize and extend WPForms with code. Our comprehensive developer resources include tutorials, snippets, and documentation on core actions, filters, functions, and more."
msgstr "使用代码自定义和扩展WPForms。我们全面的开发人员资源包括有关核心操作、过滤器、函数等的教程，摘要和文档。"

#: src/Admin/Pages/Community.php:85
msgid "WPForms Developer Documentation"
msgstr "WPForms开发人员文档"

#: src/Admin/Pages/Community.php:67
msgid "Join WPForms VIP Circle"
msgstr "加入WPForms VIP圈"

#: src/Admin/Pages/Community.php:66
msgid "Powered by the community, for the community. Anything and everything WPForms: Discussions. Questions. Tutorials. Insights and sneak peaks. Also, exclusive giveaways!"
msgstr "由社区提供动力，为社区服务。 WPForms的一切：讨论。问题。教程。洞察力和潜行高峰。此外，独家赠品！"

#: src/Admin/Pages/Community.php:65
msgid "WPForms VIP Circle Facebook Group"
msgstr "WPForms VIP圈Facebook组"

#: src/Admin/SiteHealth.php:128
msgid "Total submissions (since v1.5.0)"
msgstr "提交总数（自 v1.5.0 起）"

#: src/Admin/SiteHealth.php:109
msgid "Total forms"
msgstr "全部表单"

#: src/Admin/SiteHealth.php:102
msgid "DB tables"
msgstr "数据库表"

#: src/Admin/SiteHealth.php:99
msgid "Not found"
msgstr "未找到"

#: src/Admin/SiteHealth.php:84
msgid "Pro install date"
msgstr "专业版安装日期"

#: src/Admin/SiteHealth.php:76
msgid "Lite install date"
msgstr "精简版安装日期"

#: src/Admin/SiteHealth.php:64
msgid "Version"
msgstr "版本"

#: src/Lite/Admin/Connect.php:229
msgid "No key provided."
msgstr "未提供授权码。"

#: src/Lite/Admin/Connect.php:78
msgid "Please enter your license key to connect."
msgstr "请输入您的许可证密钥进行连接。"

#: src/Admin/Pages/Analytics.php:169 src/Admin/Pages/Analytics.php:498
#: src/Admin/Pages/SMTP.php:166
#: src/Integrations/UncannyAutomator/UncannyAutomator.php:118
msgid "Activate Now"
msgstr "立即激活"

#: includes/admin/class-about.php:900
msgid "WP Mail SMTP Pro"
msgstr "WP Mail SMTP Pro"

#: src/Emails/Summaries.php:191
msgid "Weekly WPForms Email Summaries"
msgstr "每周 WPForms 电子邮件摘要"

#: src/Emails/Summaries.php:108
msgid "Disable Email Summaries"
msgstr "禁用电子邮件摘要"

#: src/Emails/Summaries.php:103
msgid "View Email Summary Example"
msgstr "查看电子邮件摘要示例"

#: src/Emails/Summaries.php:100
msgid "Disable Email Summaries weekly delivery."
msgstr "禁用电子邮件摘要每周传递。"

#: src/Emails/Mailer.php:485
msgid "You cannot send emails with WPForms\\Emails\\Mailer until init/admin_init has been reached."
msgstr "在到达 init/admin_init 之前，您不能使用 WPForms\\Emails\\Mailer 发送电子邮件。"

#: src/Emails/Mailer.php:441
msgid "[WPForms\\Emails\\Mailer] Empty message."
msgstr "[WPForms\\Emails\\Mailer]空消息。"

#: src/Emails/Mailer.php:437
msgid "[WPForms\\Emails\\Mailer] Empty subject line."
msgstr "[WPForms\\Emails\\Mailer]空主题行。"

#. translators: %s - invalid email.
#: src/Emails/Mailer.php:432
msgid "[WPForms\\Emails\\Mailer] Invalid email address %s."
msgstr "[WPForms\\Emails\\Mailer] %s 是一个无效的电子邮箱地址。"

#: src/Lite/Admin/ConnectSkin.php:31
msgid "There was an error installing WPForms Pro. Please try again."
msgstr "安装WPForms专业版时出错。请再试一遍。"

#: src/Lite/Admin/Connect.php:259
msgid "Pro version installed but needs to be activated on the Plugins page inside your WordPress admin."
msgstr "专业版已安装，但需要在WordPress仪表盘内的插件页面启用。"

#: src/Lite/Admin/Connect.php:203
msgid "There was an error while installing an upgrade. Please check file system permissions and try again. Also, you can download the plugin from wpforms.com and install it manually."
msgstr "安装升级时出现错误。请检查文件系统权限后再试。另外，你也可以从wpforms.com下载插件，然后手动安装。"

#: src/Lite/Admin/Connect.php:82
msgid "Only the Lite version can be upgraded."
msgstr "仅基本版可以升级。"

#: includes/admin/settings-api.php:142
msgid "Paste license key here"
msgstr "在此处粘贴许可证密钥"

#: includes/admin/settings-api.php:140
msgid "Already purchased?  Simply enter your license key below to connect with WPForms PRO!"
msgstr "已经购买了？只需在下面输入您的许可证密钥即可连接到WPForms专业版！"

#: includes/admin/class-about.php:292
msgid "The WPForms Team"
msgstr "WPForms 团队"

#: src/Admin/Education/Core.php:90
msgid "Ok"
msgstr "确定"

#: includes/admin/admin.php:161
msgid "Almost Done"
msgstr "几乎完成"

#: includes/admin/admin.php:172
msgid "Unfortunately there was a server connection error."
msgstr "抱歉，服务器连接错误。"

#: includes/admin/admin.php:162
msgid "Oops!"
msgstr "哎呀！"

#: templates/emails/summary-body-plain.php:41
#: templates/emails/summary-body.php:84
msgid "It appears you do not have any form entries yet."
msgstr "看来您还没有任何表单条目。"

#: templates/emails/summary-body-plain.php:22
#: templates/emails/summary-body.php:27
msgid "Let’s see how your forms performed."
msgstr "让我们看看您的表单表现如何。"

#: templates/emails/summary-body-plain.php:20
#: templates/emails/summary-body.php:25
msgid "Let’s see how your forms performed in the past week."
msgstr "让我们看看您的表单在过去一周的表现如何。"

#: templates/admin/empty-states/no-forms.php:15
#: templates/emails/summary-body-plain.php:17
#: templates/emails/summary-body.php:23
msgid "Hi there!"
msgstr "您好！"

#. translators: %1$s - CAPTCHA provider name; %2$s - URL to reCAPTCHA
#. documentation.
#: includes/class-frontend.php:919
msgid "%1$s is not supported by AMP and is currently disabled.<br><a href=\"%2$s\" rel=\"noopener noreferrer\" target=\"_blank\">Upgrade to reCAPTCHA v3</a> for full AMP support. <br><em>Please note: this message is only displayed to site administrators.</em>"
msgstr "AMP不支持%1$s，因此目前已将其禁用。<br> <a href=\"%2$s\" rel=\"noopener noreferrer\" target=\"_blank\">升级到reCAPTCHA v3</a>以获得全面的AMP支持。 <br> <em>注意：此消息仅显示给站点管理员。</em>"

#: includes/fields/class-email.php:562
msgid "The provided emails do not match."
msgstr "提供的电子邮件不匹配。"

#: includes/fields/class-email.php:559 includes/fields/class-email.php:573
msgid "The provided email is not valid."
msgstr "提供的电子邮件无效。"

#: includes/class-process.php:640
msgid "Redirecting…"
msgstr "重定向…"

#: includes/admin/builder/panels/class-settings.php:268
msgid "Enables form submission without page reload."
msgstr "启用表单提交，无需重新加载页面。"

#: includes/admin/builder/panels/class-settings.php:266
msgid "Enable AJAX form submission"
msgstr "启用AJAX表单提交"

#: includes/admin/builder/panels/class-settings.php:197
msgid "Enable Google v3 reCAPTCHA"
msgstr "启用谷歌v3 reCAPTCHA"

#: includes/admin/class-settings.php:508
msgid "Check this option to remove ALL WPForms data upon plugin deletion. All forms and settings will be unrecoverable."
msgstr "勾選此選項会在移除插件時刪除所有WPForms數據。所有表单及设置將無法恢復。"

#: src/Admin/Settings/Captcha.php:222
msgid "0.4"
msgstr "0.4"

#: src/Admin/Settings/Captcha.php:215
msgid "reCAPTCHA v3 returns a score (1.0 is very likely a good interaction, 0.0 is very likely a bot). If the score less than or equal to this threshold, the form submission will be blocked and the message above will be displayed."
msgstr "reCAPTCHA v3返回一个分数（1.0很可能是一个很好的交互，0.0很可能是一个机器人程序）。如果分数小于或等于此阈值，则将阻止表单提交并显示上面的消息。"

#: src/Admin/Settings/Captcha.php:214
msgid "Score Threshold"
msgstr "分数阈值"

#: includes/class-frontend.php:1660 src/Admin/Settings/Captcha.php:203
msgid "Google reCAPTCHA verification failed, please try again later."
msgstr "Google reCAPTCHA验证失败，请稍后再试。"

#: src/Admin/Settings/Captcha.php:200 src/Admin/Settings/Captcha.php:207
msgid "Fail Message"
msgstr "失败消息"

#: src/Admin/Settings/Captcha.php:174
msgid "reCAPTCHA v3"
msgstr "reCAPTCHA v3"

#: templates/admin/settings/recaptcha-description.php:29
msgid "Sites already using one type of reCAPTCHA will need to create new site keys before switching to a different option."
msgstr "已经使用一种reCAPTCHA的站点需要在切换到其他选项之前创建新的站点密钥。"

#: templates/admin/settings/recaptcha-description.php:23
msgid "<strong>v3 reCAPTCHA</strong>: Uses a behind-the-scenes scoring system to detect abusive traffic, and lets you decide the minimum passing score. Recommended for advanced use only (or if using Google AMP)."
msgstr "<strong>v3 reCAPTCHA</strong>：使用后台评分系统检测滥用流量，并让您决定最低通过分数。建议仅用于高级用途（或如果使用谷歌AMP）。"

#: templates/admin/settings/recaptcha-description.php:15
msgid "<strong>v2 Invisible reCAPTCHA</strong>: Uses advanced technology to detect real users without requiring any input."
msgstr "<strong>v2 Invisible reCAPTCHA</strong>：使用先进技术检测真实用户，无需输入。"

#: templates/admin/settings/recaptcha-description.php:7
msgid "<strong>v2 Checkbox reCAPTCHA</strong>: Prompts users to check a box to prove they're human."
msgstr "<strong>v2 Checkbox reCAPTCHA</strong>：提示用户选中一个方框以证明他们是人类。"

#: templates/admin/settings/recaptcha-description.php:2
msgid "Google offers 3 versions of reCAPTCHA (all supported within WPForms):"
msgstr "谷歌提供了3个版本的reCAPTCHA(全部支持WPForms):"

#: src/Integrations/Gutenberg/FormSelector.php:106
msgid "Check out our complete guide!"
msgstr "查看我们的完整指南！"

#: src/Integrations/Gutenberg/FormSelector.php:105
msgid "Do not forget to test your form."
msgstr "不要忘记测试您的表单。"

#: src/Integrations/Gutenberg/FormSelector.php:96
msgid "survey"
msgstr "调查"

#: src/Integrations/Gutenberg/FormSelector.php:95
msgid "contact"
msgstr "偶有联系"

#: includes/admin/class-settings.php:443
msgid "Checkbox Selection Limit"
msgstr "复选框选择限制"

#: includes/admin/class-settings.php:404
msgid "Email Suggestion"
msgstr "电子邮件建议"

#: includes/admin/class-about.php:884
msgid "MonsterInsights Pro"
msgstr "MonsterInsights Pro"

#: includes/fields/class-checkbox.php:347
msgid "Limit the number of checkboxes a user can select. Leave empty for unlimited."
msgstr "限制用户可以选择的复选框数量。留空将不受限制。"

#: includes/fields/class-checkbox.php:346
msgid "Choice Limit"
msgstr "选择限制"

#: includes/admin/class-settings.php:445 includes/class-frontend.php:1643
#: includes/fields/class-checkbox.php:589
msgid "You have exceeded the number of allowed selections: {#}."
msgstr "您已超过允许的选择数：{#}。"

#: includes/admin/class-settings.php:427 includes/class-frontend.php:1641
msgid "Please enter a valid positive number."
msgstr "请输入有效的电话号码。"

#: includes/class-frontend.php:1638
msgid "Click to accept this suggestion."
msgstr "单击以接受该建议。"

#: includes/admin/builder/panels/class-settings.php:187
msgid "Enable Google Checkbox v2 reCAPTCHA"
msgstr "启用谷歌复选框V2 reCAPTCHA"

#: src/Admin/Settings/Captcha.php:172
msgid "Checkbox reCAPTCHA v2"
msgstr "复选框reCAPTCHA v2"

#: includes/class-widget.php:154
msgctxt "Widget"
msgid "Display form description"
msgstr "显示表单描述"

#: includes/class-widget.php:148
msgctxt "Widget"
msgid "Display form name"
msgstr "显示表单名称"

#: includes/class-widget.php:139
msgctxt "Widget"
msgid "No forms"
msgstr "没有表单"

#: includes/class-widget.php:133
msgctxt "Widget"
msgid "Select your form"
msgstr "选择您的表单"

#: includes/class-widget.php:125
msgctxt "Widget"
msgid "Form:"
msgstr "表单："

#: includes/class-widget.php:116
msgctxt "Widget"
msgid "Title:"
msgstr "标题："

#: includes/class-widget.php:50
msgctxt "Widget"
msgid "WPForms"
msgstr "WPForms"

#: includes/class-widget.php:40
msgctxt "Widget"
msgid "Display a form."
msgstr "显示一个表单."

#: includes/integrations.php:75
msgid "Would you like to display the form description?"
msgstr "要显示表单说明吗？"

#: includes/integrations.php:68
msgid "Display Form Description"
msgstr "显示表单描述"

#: includes/integrations.php:60
msgid "Would you like to display the forms name?"
msgstr "您要显示表单名称吗？"

#: includes/integrations.php:57 includes/integrations.php:72
msgid "Yes"
msgstr "是"

#: includes/integrations.php:56 includes/integrations.php:71
msgid "No"
msgstr "否"

#: includes/integrations.php:53
msgid "Display Form Name"
msgstr "显示表单名称"

#: includes/integrations.php:48
msgid "Select a form to add it to your post or page."
msgstr "选择一个表单将它添加到您的文章或者网页。"

#: includes/integrations.php:40
msgid "Add your form"
msgstr "添加表单"

#: includes/integrations.php:39
msgid "Content"
msgstr "内容"

#: includes/integrations.php:30
msgid "No forms found"
msgstr "找不到任何表单"

#: includes/integrations.php:23
msgid "Select a form to display"
msgstr "选择要显示的表单"

#. translators: %s - URL to a non-amp version of a page with the form.
#: includes/class-frontend.php:180
msgid "<a href=\"%s\">Go to the full page</a> to view and submit the form."
msgstr "<a href=\"%s\">转到页面</a>查看并提交表单。"

#: src/Integrations/Elementor/Widget.php:225
#: src/Integrations/Elementor/Widget.php:239
msgid "Hide"
msgstr "隐藏"

#: src/Integrations/Elementor/Widget.php:224
#: src/Integrations/Elementor/Widget.php:238
msgid "Show"
msgstr "显示"

#. translators: %s - link to a site.
#: includes/emails/templates/footer-default.php:37
#: templates/emails/general-footer.php:24
msgid "Sent from %s"
msgstr "从%s发送"

#: includes/emails/class-emails.php:633
msgid "An empty form was submitted."
msgstr "已提交空表单。"

#. translators: %d - field ID.
#: includes/emails/class-emails.php:576 includes/emails/class-emails.php:621
msgid "Field ID #%d"
msgstr "字段ID＃%d"

#: includes/emails/class-emails.php:345
msgid "You cannot send emails with WPForms_WP_Emails() until init/admin_init has been reached."
msgstr "在达到init / admin_init之前，您无法使用WPForms_WP_Emails()发送电子邮件。"

#: includes/providers/class-base.php:1239
msgid "Could not connect to the provider."
msgstr "无法连接到提供商。"

#. translators: %s - Provider type.
#: includes/providers/class-base.php:1096
msgid "Add New %s"
msgstr "添加 %s"

#: includes/providers/class-base.php:895
msgid "Available Form Fields"
msgstr "可用表单选项"

#: includes/providers/class-base.php:890 includes/providers/class-base.php:895
msgid "List Fields"
msgstr "列表字段"

#: includes/providers/class-base.php:830
msgid "We also noticed that you have some segments in your list. You can select specific list segments below if needed. This is optional."
msgstr "我们还注意到您的列表中有一些段。如果需要，您可以在下面选择特定的列表段。这是可选的。"

#: includes/providers/class-base.php:828
msgid "Select Groups"
msgstr "选择组"

#: includes/providers/class-base.php:782
msgid "Select List"
msgstr "选择列表"

#: includes/providers/class-base.php:737
msgid "Select Account"
msgstr "选择帐户"

#. translators: %s - Name field label.
#: includes/providers/class-base.php:531
msgid "%s (Last)"
msgstr "%s(最后)"

#. translators: %s - Name field label.
#: includes/providers/class-base.php:515
msgid "%s (Middle)"
msgstr "%s(中)"

#. translators: %s - Name field label.
#: includes/providers/class-base.php:499
msgid "%s (First)"
msgstr "%s(第一个)"

#. translators: %s - Name field label.
#: includes/providers/class-base.php:484
msgid "%s (Full)"
msgstr "%s(全)"

#: includes/providers/class-base.php:91
msgid "Connection"
msgstr "连接"

#: includes/providers/class-constant-contact.php:897
msgid "With Constant Contact + WPForms, growing your list is easy."
msgstr "使用Constant Contact + WPForms，可以轻松扩展您的列表。"

#: includes/providers/class-constant-contact.php:896
msgid "It doesn&#39;t matter what kind of business you run, what kind of website you have, or what industry you are in - you need to start building your email list today."
msgstr "您运营什么样的业务，拥有什么样的网站或您所在的行业并不重要 - 您需要立即开始构建您的电子邮件列表。"

#: includes/providers/class-constant-contact.php:893
msgid "4. Add your new form to any post, page, or sidebar."
msgstr "4.将新表单添加到任何文章，页面或侧栏。"

#: includes/providers/class-constant-contact.php:889
msgid "3. Connect your Constant Contact email list."
msgstr "3.连接您的Constant Contact电子邮件列表。"

#: includes/providers/class-constant-contact.php:885
msgid "2. Drag and drop any field you want onto your signup form."
msgstr "2.将所需的任何字段拖拽到注册表单上。"

#: includes/providers/class-constant-contact.php:881
msgid "1. Select from our pre-built templates, or create a form from scratch."
msgstr "1.从我们预先构建的模板中选择，或从头开始创建表单。"

#: includes/providers/class-constant-contact.php:877
msgid "Here&#39;s how it works."
msgstr "这是它的工作原理。"

#: includes/providers/class-constant-contact.php:876
msgid "We made the form creation process extremely intuitive, so you can create a form to start capturing emails within 5 minutes or less."
msgstr "我们将表单创建过程变得直观，让您可以在5分钟或更短的时间内创建一个表单并开始收集内容。"

#: includes/providers/class-constant-contact.php:875
msgid "When creating WPForms, our goal was to make a WordPress forms plugin that&#39;s both EASY and POWERFUL."
msgstr "在创建WPForms时，我们的目标是制作一个既简单又强大的WordPress表单插件。"

#: includes/providers/class-constant-contact.php:874
msgid "WPForms Makes List Building Easy"
msgstr "WPForms使列表构建变得容易"

#: includes/providers/class-constant-contact.php:870
msgid "Try Constant Contact Today"
msgstr "立即尝试Constant Contact"

#: includes/providers/class-constant-contact.php:866
msgid "Get expert marketing and support"
msgstr "获得专业的营销和支持"

#: includes/providers/class-constant-contact.php:865
msgid "Create and send professional email newsletters"
msgstr "创建和发送专业的电子邮件简报"

#: includes/providers/class-constant-contact.php:864
msgid "Seamlessly add new contacts to your email list"
msgstr "将新联系人无缝添加到电子邮件列表中"

#: includes/providers/class-constant-contact.php:862
msgid "But when you combine WPForms with Constant Contact, you can nurture your contacts and engage with them even after they leave your website. When you use Constant Contact + WPForms together, you can:"
msgstr "但是当您将WPForms与Constant Contact结合使用时，即使他们离开您的网站，您也可以培养您的联系人并与他们互动。当您一起使用Constant Contact + WPForms时，您可以："

#: includes/providers/class-constant-contact.php:861
msgid "WPForms plugin makes it fast and easy to capture all kinds of visitor information right from your WordPress site - even if you don&#39;t have a Constant Contact account."
msgstr "WPForms插件可以直接从您的WordPress网站快速轻松地捕获各种访问者信息 - 即使您没有Constant Contact帐户。"

#: includes/providers/class-constant-contact.php:858
msgid "Get Started with Constant Contact for Free"
msgstr "免费使用Constant Contact"

#: includes/providers/class-constant-contact.php:855
msgid "With a powerful email marketing service like Constant Contact, you can instantly send out mass notifications and beautifully designed newsletters to engage your subscribers."
msgstr "通过Constant Contact等强大的电子邮件营销服务，您可以立即发送大量通知和设计精美的新闻通讯，吸引订阅者。"

#: includes/providers/class-constant-contact.php:853
msgid "The Best Email Marketing Service"
msgstr "最好的电子邮件营销服务"

#: includes/providers/class-constant-contact.php:852
msgid "High-Converting Form Builder"
msgstr "高转换率的表单构建器"

#: includes/providers/class-constant-contact.php:851
msgid "A Website or Blog"
msgstr "网站或博客"

#: includes/providers/class-constant-contact.php:849
msgid "Here are the 3 things you need to build an email list:"
msgstr "以下是构建电子邮件列表所需的3件事："

#: includes/providers/class-constant-contact.php:848
msgid "You&#39;ve Already Started - Here&#39;s the Next Step (It&#39;s Easy)"
msgstr "您已经开始 - 这是下一步（很容易）"

#. translators: %s - WPBeginners.com Guide to Email Lists URL.
#: includes/providers/class-constant-contact.php:834
msgid "For more details, see this guide on <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">why building your email list is so important</a>."
msgstr "有关详细信息，请参阅<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">为何建立您的电子邮件列表非常重要</a>的指南。"

#: includes/providers/class-constant-contact.php:829
msgid "That&#39;s why it&#39;s crucial to start collecting email addresses and building your list as soon as possible."
msgstr "这就是为什么开始收集电子邮件地址并尽快建立列表至关重要的原因。"

#: includes/providers/class-constant-contact.php:823
msgid "<strong>Email converts</strong> - People who buy products marketed through email spend 138% more than those who don&#39;t receive email offers."
msgstr "<strong>电子邮件转换</strong> - 购买通过电子邮件销售的产品的用户比未收到电子邮件优惠的用户多花费138％。"

#: includes/providers/class-constant-contact.php:815
msgid "<strong>You own your email list</strong> - Unlike with social media, your list is your property and no one can revoke your access to it."
msgstr "<strong>您拥有自己的电子邮件列表</strong> - 与社交媒体不同，您的列表是您的财产，任何人都无法撤销您对其的访问权限。"

#: includes/providers/class-constant-contact.php:807
msgid "<strong>Email is still #1</strong> - At least 91% of consumers check their email on a daily basis. You get direct access to your subscribers, without having to play by social media&#39;s rules and algorithms."
msgstr "<strong>电子邮件仍然是＃1</strong> - 至少91%的消费者每天检查他们的电子邮件。您可以直接访问订阅者，而无需通过社交媒体的规则和算法进行播放。"

#: includes/providers/class-constant-contact.php:792
msgid "Here are 3 big reasons why every smart business in the world has an email list:"
msgstr "以下是世界上每个聪明的企业都拥有电子邮件列表的3个重要原因。"

#: includes/providers/class-constant-contact.php:787
msgid "Email is hands-down the most effective way to nurture leads and turn them into customers, with a return on investment (ROI) of <strong>$44 back for every $1 spent</strong> according to DMA."
msgstr "电子邮件是不二之选，是培育潜在客户并将其转变为客户的最有效方法，根据DMA的调查，每花费1美元 ，电子邮件的投资回报率（ROI） <strong>为44美元</strong> 。"

#: includes/providers/class-constant-contact.php:783
msgid "Wondering if email marketing is really worth your time?"
msgstr "想知道电子​​邮件营销是否值得花时间？"

#: includes/providers/class-constant-contact.php:782
msgid "Grow Your Website with WPForms + Email Marketing"
msgstr "使用WPForms +电子邮件营销拓展您的网站"

#. translators: %s - WPForms Constant Contact internal URL.
#: includes/providers/class-constant-contact.php:703
msgid "Learn More about the <a href=\"%s\">power of email marketing</a>"
msgstr "详细了解<a href=\"%s\">电子邮件营销的力量</a>"

#: includes/providers/class-constant-contact.php:698
msgid "Connect your existing account"
msgstr "连接您现有的帐户"

#: includes/providers/class-constant-contact.php:686
msgid "Get the most out of the <strong>WPForms</strong> plugin &mdash; use it with an active Constant Contact account."
msgstr "充分利用<strong> WPForms </strong>插件＆mdash; 将它与活动的常量联系人帐户一起使用。"

#: includes/providers/class-constant-contact.php:578
#: includes/providers/class-constant-contact.php:695
msgid "Try Constant Contact for Free"
msgstr "免费试用Constant Contact"

#: includes/admin/settings-api.php:143
#: includes/providers/class-constant-contact.php:536
msgid "Connect"
msgstr "连接"

#: includes/providers/class-constant-contact.php:530
#: includes/providers/class-constant-contact.php:635
msgid "Account Nickname"
msgstr "帐户昵称"

#: includes/providers/class-constant-contact.php:524
#: includes/providers/class-constant-contact.php:629
msgid "Authorization Code"
msgstr "授权码"

#: includes/providers/class-constant-contact.php:515
#: includes/providers/class-constant-contact.php:620
msgid "Click here to register with Constant Contact"
msgstr "点击这里注册Constant Contact"

#: includes/providers/class-constant-contact.php:509
#: includes/providers/class-constant-contact.php:614
msgid "Because Constant Contact requires external authentication, you will need to register WPForms with Constant Contact before you can proceed."
msgstr "由于Constant Contact需要外部身份验证，因此您需要先注册WPForms和Constant Contact，然后才能继续。"

#: includes/providers/class-constant-contact.php:504
#: includes/providers/class-constant-contact.php:609
msgid "Click here for documentation on connecting WPForms with Constant Contact."
msgstr "单击此处获取有关将WPForms与Constant Contact连接的文档。"

#: includes/providers/class-constant-contact.php:501
msgid "Please fill out all of the fields below to register your new Constant Contact account."
msgstr "请填写下面的所有字段,以注册您的新固定联系帐户."

#: includes/providers/class-constant-contact.php:405
msgid "API list error: Constant API error"
msgstr "API列表错误：常量API错误"

#. translators: $s - license type.
#: includes/admin/settings-api.php:171
msgid "Your license key level is %s."
msgstr "您的许可证密钥级别：%s。"

#: includes/admin/settings-api.php:164
msgid "Deactivate Key"
msgstr "取消启用密钥"

#: includes/admin/settings-api.php:160
msgid "Verify Key"
msgstr "验证密钥"

#: includes/admin/settings-api.php:132
msgid "As a valued WPForms Lite user you receive <strong>50% off</strong>, automatically applied at checkout!"
msgstr "作为一个有价值的WPForms精简版用户，您可以享受<strong>50% o的折扣</strong>，在结帐时自动应用！"

#. translators: %s - WPForms.com upgrade URL.
#: includes/admin/settings-api.php:115
msgid "To unlock more features consider <strong><a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"wpforms-upgrade-modal\">upgrading to PRO</a></strong>."
msgstr "要解锁更多功能，请考虑<strong><a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"wpforms-upgrade-modal\">升级到专业版</a></strong>。"

#: includes/admin/settings-api.php:109
msgid "You're using WPForms Lite - no license needed. Enjoy!"
msgstr "您正在使用WPForms 精简版-不需要许可证。享用吧！"

#. translators: %s - ID of a setting.
#: includes/admin/settings-api.php:77
msgid "The callback function used for the %s setting is missing."
msgstr "用于 %s 设置的回调函数缺失。"

#: includes/admin/class-settings.php:516
msgid "Check this option to remove ALL WPForms data upon plugin deletion. All forms, entries, and uploaded files will be unrecoverable."
msgstr "勾選此選項可在移除插件時刪除所有WPForms數據。所有表单、項目和上傳的文件將無法恢復。"

#: includes/admin/class-settings.php:507
msgid "Uninstall WPForms"
msgstr "卸载WPForms"

#: includes/admin/class-settings.php:495
msgid "Hide Announcements"
msgstr "隐藏公告"

#: includes/admin/class-settings.php:472 includes/admin/class-settings.php:479
msgid "Manage integrations with popular providers such as Constant Contact, Mailchimp, Zapier, and more."
msgstr "管理与流行提供商的集成，如Constant Contact、Mailchimp、Zapier等。"

#: includes/admin/class-settings.php:433 includes/class-frontend.php:1642
msgid "Field values do not match."
msgstr "字段值不匹配。"

#: includes/admin/class-settings.php:431
msgid "Confirm Value"
msgstr "确认值"

#: includes/admin/class-settings.php:419
msgid "Number"
msgstr "号码"

#: includes/admin/class-settings.php:400 includes/class-frontend.php:1630
msgid "Please enter a valid email address."
msgstr "请输入正确的电子邮件地址。"

#: includes/admin/class-settings.php:394
msgid "Please enter a valid URL."
msgstr "请输入有效网址。"

#: includes/admin/class-settings.php:392
msgid "Website URL"
msgstr "网站地址"

#: includes/admin/class-settings.php:375
msgid "Validation Messages"
msgstr "验证消息"

#: src/Admin/Settings/Captcha.php:227
msgid "No-Conflict Mode"
msgstr "无冲突模式"

#: src/Admin/Settings/Captcha.php:190 src/Admin/Settings/Captcha.php:195
msgid "Secret Key"
msgstr "私钥"

#: src/Admin/Settings/Captcha.php:180 src/Admin/Settings/Captcha.php:185
msgid "Site Key"
msgstr "站点密钥"

#: src/Admin/Settings/Captcha.php:173
msgid "Invisible reCAPTCHA v2"
msgstr "不可见reCAPTCHA v2"

#: includes/fields/class-base.php:940 src/Admin/Settings/Captcha.php:168
msgid "Type"
msgstr "类型"

#: includes/admin/class-settings.php:365
msgid "Check this option to enable the ability to CC: email addresses in the form notification settings."
msgstr "勾选此选项，可以在表单通知设置中启用CC：电子邮件地址的功能。"

#: includes/admin/class-settings.php:364
msgid "Carbon Copy"
msgstr "抄送"

#: includes/admin/class-settings.php:358
msgid "Customize the background color of the HTML email template."
msgstr "自定义HTML电子邮件模板的背景色。"

#: includes/admin/class-settings.php:357
msgid "Background Color"
msgstr "背景颜色"

#: includes/admin/class-settings.php:352
msgid "Upload or choose a logo to be displayed at the top of email notifications.<br>Recommended size is 300x100 or smaller for best support on all devices."
msgstr "上传或选择要显示在电子邮件通知顶部的徽标。<br>建议大小为300x100或更小，以便在所有设备上获得最佳支持。"

#: includes/admin/class-settings.php:351
msgid "Header Image"
msgstr "页眉图像"

#: includes/admin/class-settings.php:346
msgid "Plain text"
msgstr "以纯文本粘贴"

#: includes/admin/class-settings.php:345
msgid "HTML Template"
msgstr "HTML模板"

#: includes/admin/class-settings.php:341
msgid "Determines how email notifications will be formatted. HTML Templates are the default."
msgstr "确定电子邮件通知的格式。HTML模板是默认的。"

#: includes/admin/class-settings.php:340
msgid "Template"
msgstr "模板"

#. translators: %s - WPForms.com GDPR documentation URL.
#: includes/admin/class-settings.php:309
msgid "Check this option to enable GDPR related features and enhancements. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Read our GDPR documentation</a> to learn more."
msgstr "选中此选项以启用 GDPR 相关功能和增强功能。<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">阅读我们的 GDPR 文档</a>以了解更多。"

#: includes/admin/class-settings.php:306
msgid "GDPR Enhancements"
msgstr "GDPR增强"

#: includes/admin/class-settings.php:299
msgid "GDPR"
msgstr "GDPR"

#: includes/admin/class-settings.php:294
msgid "Check this option to load WPForms assets site-wide. Only check if your site is having compatibility issues or instructed to by support."
msgstr "勾选此项以在全站范围内加载WPForms脚本。只当您的站点有兼容性问题或支持人员指示的情况下才可进行此操作。"

#: includes/admin/class-settings.php:293
msgid "Load Assets Globally"
msgstr "全局加载资源"

#: includes/admin/class-settings.php:288
msgid "No styling"
msgstr "没有样式"

#: includes/admin/class-settings.php:287
msgid "Base styling only"
msgstr "仅基础样式"

#: includes/admin/class-settings.php:286
msgid "Base and form theme styling"
msgstr "基础和表单的主题样式"

#: includes/admin/class-settings.php:268
msgid "Include Form Styling"
msgstr "包括表单样式"

#: includes/admin/class-settings.php:256
msgid "License Key"
msgstr "许可密钥"

#: includes/admin/class-settings.php:249
msgid "Your license key provides access to updates and addons."
msgstr "您的许可密钥提供了对更新和扩展的访问。"

#: includes/admin/class-settings.php:249
msgid "License"
msgstr "许可证"

#. translators: %s - WPForms.com Setup reCAPTCHA URL.
#: templates/admin/settings/recaptcha-description.php:34
msgid "<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Read our walk through</a> to learn more and for step-by-step directions."
msgstr "<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">通过阅读我们的指南</a> ，了解更多信息和逐步指导。"

#: templates/admin/settings/recaptcha-description.php:1
msgid "reCAPTCHA is a free anti-spam service from Google which helps to protect your website from spam and abuse while letting real people pass through with ease."
msgstr "reCAPTCHA是谷歌提供的一项免费反垃圾服务，可帮助您的网站防护垃圾内容和滥用，同时让真人轻松通过。"

#: includes/admin/class-settings.php:204 includes/admin/class-settings.php:488
msgid "Misc"
msgstr "杂项"

#: includes/admin/class-settings.php:194 includes/admin/class-settings.php:472
#: includes/admin/class-settings.php:479
msgid "Integrations"
msgstr "集成"

#: includes/admin/class-settings.php:189
msgid "Validation"
msgstr "验证"

#: includes/admin/builder/panels/class-fields.php:141
#: src/Admin/Education/Fields.php:269 src/Admin/Settings/Captcha.php:137
msgid "reCAPTCHA"
msgstr "reCAPTCHA人机识别系统"

#: includes/admin/class-settings.php:181 includes/admin/class-settings.php:186
#: includes/admin/class-settings.php:191 includes/admin/class-settings.php:206
#: src/Admin/Settings/Captcha.php:105 src/Admin/Tools/Views/Logs.php:123
msgid "Save Settings"
msgstr "保存设置"

#: includes/admin/class-settings.php:155
msgid "Settings were successfully saved."
msgstr "设置已成功保存。"

#: includes/admin/class-about.php:1456 includes/admin/class-about.php:1462
msgid "Standard Support"
msgstr "标准支持"

#: includes/admin/class-about.php:1450
msgid "Limited Support"
msgstr "有限支持"

#: includes/admin/class-about.php:1427 includes/admin/class-about.php:1434
#: includes/admin/class-about.php:1441
msgid "All Addons Included"
msgstr "包括所有扩展"

#: includes/admin/class-about.php:1414
msgid "Email Marketing Addons included"
msgstr "包括电子邮件营销扩展"

#: includes/admin/class-about.php:1408
msgid "Custom Captcha Addon included"
msgstr "包括自定义Captcha扩展"

#: includes/admin/class-about.php:1402
msgid "No Addons Included"
msgstr "不包含扩展"

#: includes/admin/class-about.php:1393
msgid "All Advanced Features"
msgstr "所有高级功能"

#: includes/admin/class-about.php:1380
msgid "Multi-page Forms, File Upload Forms, Multiple Form Notifications, Conditional Form Confirmation"
msgstr "多页表单、文件上传表单、多表单通知、条件表单确认"

#: includes/admin/class-about.php:1379 includes/admin/class-about.php:1386
msgid "Limited Advanced Features"
msgstr "有限的高级功能"

#: includes/admin/class-about.php:1373
msgid "No Advanced Features"
msgstr "没有高级功能"

#: includes/admin/class-about.php:1365
msgid "Create interactive Surveys and Polls with beautiful reports"
msgstr "创建交互式调查和投票与好看的报告"

#: includes/admin/class-about.php:1317 includes/admin/class-about.php:1324
#: includes/admin/class-about.php:1331 includes/admin/class-about.php:1338
msgid "Create Payment Forms"
msgstr "创建支付表单"

#: includes/admin/class-about.php:1299 includes/admin/class-about.php:1305
#: includes/admin/class-about.php:1311 includes/admin/class-about.php:1347
#: includes/admin/class-about.php:1353 includes/admin/class-about.php:1359
msgid "Not Available"
msgstr "不可用"

#: includes/admin/class-about.php:1245 includes/admin/class-about.php:1259
#: includes/admin/class-about.php:1273 includes/admin/class-about.php:1287
msgid "<strong>Bonus:</strong> 500+ integrations with Zapier."
msgstr "<strong>奖励：</strong>500+与Zapier的集成。"

#: includes/admin/class-about.php:1255 includes/admin/class-about.php:1269
#: includes/admin/class-about.php:1283
msgid "All Marketing Integrations"
msgstr "所有营销整合"

#: includes/admin/class-about.php:1235 includes/admin/class-about.php:1242
msgid "Constant Contact, Mailchimp, AWeber, GetResponse, Campaign Monitor, Sendinblue, and Drip"
msgstr "Constant Contact、Mailchimp、AWeber、GetResponse、Campaign Monitor、Sendinblue和Drip"

#: includes/admin/class-about.php:1221 includes/admin/class-about.php:1228
msgid "Constant Contact only"
msgstr "仅限常用联系人"

#: includes/admin/class-about.php:1220 includes/admin/class-about.php:1227
msgid "Limited Marketing Integration"
msgstr "有限的营销集成"

#: includes/admin/class-about.php:1212
msgid "All Form Templates including Bonus 100+ pre-made form templates"
msgstr "所有的表单模板，包括Bonus等100+预制的表单模板。"

#: includes/admin/class-about.php:1194 includes/admin/class-about.php:1200
#: includes/admin/class-about.php:1206
msgid "Basic Form Templates"
msgstr "基本表单模板"

#: includes/admin/class-about.php:1174 includes/admin/class-about.php:1180
#: includes/admin/class-about.php:1186
msgid "Powerful Form Logic for Building Smart Forms"
msgstr "用于构建人性化表单的强大表单逻辑"

#: includes/admin/class-about.php:1168
msgid "Not available"
msgstr "不可用"

#: includes/admin/class-about.php:1145 includes/admin/class-about.php:1152
#: includes/admin/class-about.php:1159
msgid "Access to all Standard and Fancy Fields"
msgstr "访问所有标准和自定义字段"

#: includes/admin/class-about.php:1139
msgid "Name, Email, Single Line Text, Paragraph Text, Dropdown, Multiple Choice, Checkboxes, Numbers, and Number Slider"
msgstr "名字、电子邮箱地址、单行文本、段落文本、下拉框、多选选项、复选框、数字和数字幻灯片 在移除插件后，选中该选项以删除所有WPForms数据。所有表单、項目和上傳的文件將無法恢復。"

#: includes/admin/class-about.php:1138
msgid "Standard Fields Only"
msgstr "仅标准字段"

#: includes/admin/class-about.php:1118 includes/admin/class-about.php:1124
#: includes/admin/class-about.php:1130
msgid "Complete Entry Management inside WordPress"
msgstr "完成WordPress内部的入口管理"

#: includes/admin/class-about.php:1112
msgid "Entries via Email Only"
msgstr "仅通过电子邮件发送条目"

#: includes/admin/class-about.php:893 src/Admin/Pages/SMTP.php:378
msgid "WP Mail SMTP"
msgstr "WP Mail SMTP"

#: includes/admin/class-about.php:869
msgid "OptinMonster"
msgstr "OptinMonster"

#: includes/admin/class-about.php:758
msgid "Feature"
msgstr "特征"

#: includes/admin/class-about.php:749
msgid "Get the most out of WPForms by upgrading to Pro and unlocking all of the powerful features."
msgstr "通过升级到Pro并解锁所有强大功能，充分利用WPForms。"

#: includes/admin/class-about.php:698
msgid "Would you like to access WPForms addons to extend the functionality of your forms? The first thing you need to do is install WPForms. Once that’s done, let’s go ahead and look at the process of activating addons."
msgstr "您想访问WPForms扩展来增强您的表单功能吗？您需要做的第一件事就是安装WPForms。完成后，让我们继续看看启用扩展的过程。"

#: includes/admin/class-about.php:694
msgid "How to Install and Activate WPForms Addons"
msgstr "如何安装和启用 WPForms扩展"

#: includes/admin/class-about.php:679
msgid "Do you need to check that your forms are compliant with the European Union’s General Data Protection Regulation? The best way to ensure GDPR compliance for your specific site is always to consult legal counsel. In this guide, we’ll discuss general considerations for GDPR compliance in your WordPress forms."
msgstr "您是否需要检查您的表单是否符合欧盟的通用数据保护法规？ 确保您的特定网站遵守GDPR的最佳方法始终是咨询法律顾问。 在本指南中，我们将讨论WordPress表单中GDPR合规性的一般注意事项。"

#: includes/admin/class-about.php:675
msgid "How to Create GDPR Compliant Forms"
msgstr "如何创建符合GDPR标准的表单"

#: includes/admin/class-about.php:660
msgid "Would you like to learn more about all of the settings available in WPForms? In addition to tons of customization options within the form builder, WPForms has an extensive list of plugin-wide options available. This includes choosing your currency, adding GDPR enhancements, setting up integrations."
msgstr "您想了解更多WPForms中所有可用的设置吗？除了在表单构建器内的大量自定义选项外，WPForms还提供了很多可供插件使用的选项。包括选择货币，添加GDPR增强功能，设置集成。"

#: includes/admin/class-about.php:656
msgid "A Complete Guide to WPForms Settings"
msgstr "WPForms设置的完整指南"

#: includes/admin/class-about.php:645 includes/admin/class-about.php:664
#: includes/admin/class-about.php:683 includes/admin/class-about.php:702
msgid "Read Documentation"
msgstr "阅读文档"

#: includes/admin/class-about.php:641
msgid "Are you wondering which form fields you have access to in WPForms and what each field does? WPForms has lots of field types to make creating and filling out forms easy. In this tutorial, we’ll cover all of the fields available in WPForms."
msgstr "您是否想知道您在WPForms中可以访问哪些表单字段以及每个字段的作用？ WPForms有许多字段类型可以轻松创建和填写表单。 在本教程中，我们将介绍WPForms中可用的所有字段。"

#: includes/admin/class-about.php:637
msgid "How to Choose the Right Form Field"
msgstr "如何选择正确的表单字段"

#: includes/admin/class-about.php:616 includes/admin/class-about.php:838
msgid "Bonus: WPForms Lite users get <span class=\"price-20-off\">50% off regular price</span>, automatically applied at checkout."
msgstr "奖励: wpforms 精简版用户可获得 <span class=\"price-20-off\">50% o 比正常价格低% </span>, 在结账时自动应用。"

#: includes/admin/class-about.php:607
msgid "Get WPForms Pro Today and Unlock all the Powerful Features"
msgstr "立即获取 WPForms Pro和解锁所有的强大功能"

#: includes/admin/class-about.php:518
msgid "Thanks for being a loyal WPForms Lite user. <strong>Upgrade to WPForms Pro</strong> to unlock all the awesome features and experience<br>why WPForms is consistently rated the best WordPress form builder."
msgstr "感谢您成为忠实的WPForms Lite用户。 <strong>升级到WPForms Pro </strong>以解锁所有强大的功能和体验<br>为什么WPForms一直被评为最佳WordPress表单构建器。"

#: includes/admin/class-about.php:494
msgid "How to Display Forms on Your Site"
msgstr "如何在您的网站上显示表单"

#: includes/admin/class-about.php:489
msgid "How to Customize Form Fields"
msgstr "如何自定义表单域"

#: includes/admin/class-about.php:484
msgid "How to Add a New Form"
msgstr "如何添加新表单"

#: includes/admin/class-about.php:478
msgid "In the Forms Overview page, the forms list will be empty because there are no forms yet. To create a new form, click on the Add New button, and this will launch the WPForms Form Builder."
msgstr "在“表单概述”页面中，表单列表将为空，因为还没有表单。 要创建新表单，请单击“添加新”按钮，这将启动WPForms表单构建器。"

#: includes/admin/class-about.php:474
msgid "To begin, you’ll need to be logged into the WordPress admin area. Once there, click on WPForms in the admin sidebar to go the Forms Overview page."
msgstr "首先，您需要登录WordPress管理区域。 在那里，单击管理侧栏中的WPForms以进入“表单概述”页面。"

#: includes/admin/class-about.php:470
msgid "Want to get started creating your first form with WPForms? By following the step by step instructions in this walkthrough, you can easily publish your first form on your site."
msgstr "想要开始使用WPForms创建您的第一个表单？ 按照本演练中的分步说明，您可以轻松地在您的网站上发布您的第一个表单。"

#: includes/admin/class-about.php:466
msgid "Creating Your First Form"
msgstr "创建第一个表单"

#. translators: %s - addon status label.
#: includes/admin/class-about.php:342
msgid "Status: %s"
msgstr "状态： %s"

#: includes/admin/class-about.php:431
msgid "Install Plugin"
msgstr "安装插件"

#: includes/admin/class-about.php:290
msgid "The WPForms Team photo"
msgstr "WPForms团队照片"

#: includes/admin/class-about.php:284
msgid "Yup, we know a thing or two about building awesome products that customers love."
msgstr "是的, 我们对制造客户喜爱的优质产品了如指掌。"

#: includes/admin/class-about.php:261
msgid "Our goal is to take the pain out of creating online forms and make it easy."
msgstr "我们的目标是减轻创建在线表单的痛苦, 让它变得容易。"

#: includes/admin/class-about.php:258
msgid "Over the years, we found that most WordPress contact form plugins were bloated, buggy, slow, and very hard to use. So we started with a simple goal: build a WordPress forms plugin that’s both easy and powerful."
msgstr "多年来, 我们发现大多数 WordPress 联系表形式插件都是臃肿的、缓慢的, 而且非常难以使用。所以我们从一个简单的目标开始: 构建一个既简单又强大的 WordPress 表表插件。"

#: includes/admin/class-about.php:255
msgid "Hello and welcome to WPForms, the most beginner friendly drag & drop WordPress forms plugin. At WPForms, we build software that helps you create beautiful responsive online forms for your website in minutes."
msgstr "您好，欢迎使用 WPForms，这是最适合初学者的拖拽式 WordPress 表单插件。在 WPForms，我们构建的软件可以帮助您在几分钟内创建漂亮的响应式在线表单。"

#: includes/admin/class-about.php:114 src/Admin/Builder/Help.php:249
msgid "Getting Started"
msgstr "入门"

#: includes/admin/class-about.php:80
msgid "Advanced Form Features"
msgstr "高级表单功能"

#: includes/admin/class-about.php:79 includes/admin/class-welcome.php:266
msgid "Surveys & Polls"
msgstr "调查和民意测验"

#: includes/admin/class-about.php:78 includes/admin/class-welcome.php:265
msgid "Payment Forms"
msgstr "付款表单"

#: includes/admin/class-about.php:77 includes/admin/class-welcome.php:274
msgid "Marketing Integrations"
msgstr "营销整合"

#: includes/admin/class-about.php:74
msgid "Form Fields"
msgstr "您能隐藏不需要的表单字段"

#: includes/admin/ajax-actions.php:607
msgid "Addon installed."
msgstr "扩展已安装。"

#: includes/admin/ajax-actions.php:607
msgid "Plugin installed."
msgstr "已安装插件。"

#: includes/admin/ajax-actions.php:627
msgid "Addon installed & activated."
msgstr "扩展安装并启用成功。"

#: includes/admin/ajax-actions.php:627 src/Lite/Admin/Connect.php:182
#: src/Lite/Admin/Connect.php:195 src/Lite/Admin/Connect.php:255
msgid "Plugin installed & activated."
msgstr "已安装并启用插件。"

#: includes/admin/ajax-actions.php:501
msgid "Addon activated."
msgstr "扩展已启用。"

#: includes/admin/ajax-actions.php:499
msgid "Plugin activated."
msgstr "插件已启用。"

#: includes/admin/ajax-actions.php:457
msgid "Could not deactivate the addon. Please deactivate from the Plugins page."
msgstr "无法禁用扩展。 请从“插件”页面禁用。"

#: includes/admin/ajax-actions.php:453
msgid "Addon deactivated."
msgstr "扩展已禁用。"

#: includes/admin/ajax-actions.php:451
msgid "Plugin deactivated."
msgstr "插件已停用。"

#: includes/admin/ajax-actions.php:418
msgid "There was an error and the connection failed. Please contact your web host with the technical details below."
msgstr "出现错误，连接失败。请联系您的网站主机，并提供以下技术详情。"

#: includes/admin/ajax-actions.php:411
msgid "Success! Your server can make SSL connections."
msgstr "成功！您的服务器可以建立SSL连接。"

#: includes/admin/ajax-actions.php:126
msgid "Error creating form."
msgstr "创建表单时出错。"

#: includes/admin/ajax-actions.php:101
msgid "No form name provided."
msgstr "未提供表单名称。"

#: includes/admin/class-about.php:113 includes/admin/class-menu.php:152
msgid "About Us"
msgstr "关于我们"

#: includes/admin/class-menu.php:151
msgid "About WPForms"
msgstr "关于WPForms"

#: includes/admin/class-menu.php:122 src/Admin/Builder/Help.php:253
msgid "Addons"
msgstr "扩展"

#: includes/admin/class-menu.php:112
msgid "Info"
msgstr "信息"

#: includes/admin/class-menu.php:102
msgid "Tools"
msgstr "工具"

#: includes/admin/class-menu.php:101
msgid "WPForms Tools"
msgstr "WPForms工具"

#: includes/admin/class-menu.php:91
msgid "WPForms Settings"
msgstr "WPForms设置"

#: includes/admin/class-about.php:73 includes/admin/class-menu.php:73
msgid "Form Entries"
msgstr "表单条目"

#: includes/admin/class-menu.php:63
msgid "WPForms Builder"
msgstr "WPForms 构建器"

#: includes/admin/class-menu.php:54 src/Admin/AdminBarMenu.php:238
msgid "All Forms"
msgstr "全部表单"

#: includes/admin/builder/panels/class-settings.php:257
msgid "How to use Dynamic Field Population"
msgstr "如何使用动态字段填充"

#: includes/admin/builder/panels/class-settings.php:255
msgid "Enable dynamic fields population"
msgstr "启用动态字段填充"

#: includes/admin/builder/panels/class-settings.php:192
msgid "Enable Google Invisible v2 reCAPTCHA"
msgstr "启用谷歌隐形V2 reCAPTCHA"

#: includes/admin/builder/panels/class-settings.php:126
msgid "Enable anti-spam honeypot"
msgstr "启用反垃圾信息 honeypot"

#: includes/admin/builder/panels/class-settings.php:246
msgid "Enter CSS class names for the form submit button. Multiple names should be separated with spaces."
msgstr "输入表单提交按钮的CSS类名。应使用空格分隔多个类名。"

#: includes/admin/builder/panels/class-settings.php:244
msgid "Submit Button CSS Class"
msgstr "提交按钮CSS类"

#: includes/admin/builder/panels/class-settings.php:116
msgid "Enter the submit button text you would like the button display while the form submit is processing."
msgstr "在表单提交处理期间输入您希望按钮显示的提交按钮文本."

#: includes/admin/builder/panels/class-settings.php:114
msgid "Submit Button Processing Text"
msgstr "提交按钮处理文本"

#: includes/admin/builder/panels/class-settings.php:104
msgid "Submit Button Text"
msgstr "提交按钮文本"

#: includes/admin/builder/panels/class-settings.php:235
msgid "Enter CSS class names for the form wrapper. Multiple class names should be separated with spaces."
msgstr "输入表单包装的CSS类名。多个类名应该用空格分隔。"

#: includes/admin/builder/panels/class-settings.php:233
msgid "Form CSS Class"
msgstr "表单CSS类"

#: includes/admin/builder/panels/class-settings.php:97
#: src/Integrations/Elementor/Widget.php:236
msgid "Form Description"
msgstr "表单描述"

#: includes/admin/builder/panels/class-settings.php:60
msgid "You need to <a href=\"#\" class=\"wpforms-panel-switch\" data-panel=\"setup\">setup your form</a> before you can manage the settings."
msgstr "在管理设置之前，您需要先<a href=\"#\" class=\"wpforms-panel-switch\" data-panel=\"setup\">开始表单</a>。"

#: includes/admin/builder/panels/class-settings.php:38
#: includes/admin/builder/panels/class-settings.php:79
#: includes/admin/class-settings.php:179 includes/admin/class-settings.php:261
#: includes/fields/class-base.php:872
msgid "General"
msgstr "通用"

#: includes/admin/builder/panels/class-settings.php:18
#: includes/admin/class-menu.php:92 includes/admin/class-menu.php:250
msgid "Settings"
msgstr "设置"

#: includes/admin/class-menu.php:131 includes/admin/class-menu.php:132
msgid "Analytics"
msgstr "分析"

#. translators: %s - Addons page URL.
#: includes/admin/builder/panels/class-payments.php:93
msgid "It seems you do not have any payment addons activated. You can head over to the <a href=\"%s\">Addons page</a> to install and activate the addon for your payment service."
msgstr "您似乎没有启用任何支付服务扩展。您可以前往<a href=\"%s\">扩展页面</a>为您的支付服务安装和启用扩展。"

#: includes/admin/builder/panels/class-payments.php:89
#: includes/admin/builder/panels/class-payments.php:110
msgid "Install Your Payment Integration"
msgstr "安装您的付款集成"

#: includes/admin/builder/panels/class-payments.php:37
#: includes/admin/builder/panels/class-providers.php:70
msgid "Default"
msgstr "默认"

#: includes/admin/builder/panels/class-payments.php:18
msgid "Payments"
msgstr "支付"

#: includes/admin/builder/panels/class-providers.php:133
msgid "Select your email marketing service provider or CRM from the options on the left. If you don't see your email marketing service listed, then let us know and we'll do our best to get it added as fast as possible."
msgstr "从左侧的选项中选择您的电子邮件营销服务提供商或CRM。如果您没有看到您的电子邮件营销服务列表，那么让我们知道，我们会尽最大努力让它尽快添加。"

#: includes/admin/builder/panels/class-providers.php:132
msgid "Select Your Marketing Integration"
msgstr "选择您的营销应用集成"

#. translators: %s - plugin admin area Addons page.
#: includes/admin/builder/panels/class-providers.php:116
msgid "It seems you do not have any marketing addons activated. You can head over to the <a href=\"%s\">Addons page</a> to install and activate the addon for your provider."
msgstr "您似乎没有启用任何营销服务扩展。您可以前往<a href=\"%s\">扩展页面</a>安装并启用扩展。"

#: includes/admin/builder/panels/class-providers.php:111
msgid "Install Your Marketing Integration"
msgstr "安装营销应用集成"

#: includes/admin/builder/panels/class-payments.php:58
#: includes/admin/builder/panels/class-providers.php:88
msgid "You need to <a href=\"#\" class=\"wpforms-panel-switch\" data-panel=\"setup\">setup your form</a> before you can manage these settings."
msgstr "在管理这些设置之前，您需要先<a href=\"#\" class=\"wpforms-panel-switch\" data-panel=\"setup\">开始表单</a>。"

#: includes/admin/builder/panels/class-providers.php:53
msgid "Field required"
msgstr "必填字段"

#: includes/admin/builder/panels/class-providers.php:52
msgid "You must provide a connection nickname."
msgstr "必须提供连接昵称。"

#: includes/admin/builder/panels/class-providers.php:51
msgid "Eg: Newsletter Optin"
msgstr "例如:通讯选项"

#: includes/admin/builder/panels/class-providers.php:50
msgid "Enter a %type% nickname"
msgstr "输入%type%昵称"

#: includes/admin/builder/panels/class-providers.php:49
msgid "Are you sure you want to delete this connection?"
msgstr "确定要删除此连接？"

#: includes/admin/builder/panels/class-providers.php:48
msgid "We need to save your progress to continue to the Marketing panel. Is that OK?"
msgstr "我们需要保存您的进度以便继续进入营销面板。这样可以吗？"

#: includes/admin/builder/panels/class-providers.php:18
msgid "Marketing"
msgstr "营销"

#. translators: %s - Form template name.
#: includes/admin/builder/panels/class-setup.php:267
msgid "%s template"
msgstr "%s模板"

#: includes/admin/builder/panels/class-setup.php:298
msgid "Selected"
msgstr "已选择"

#: includes/admin/builder/panels/class-setup.php:113
msgid "Enter your form name here&hellip;"
msgstr "在此处输入表单名称&hellip;"

#: includes/admin/builder/panels/class-setup.php:46
msgid "Setup"
msgstr "设置"

#: includes/admin/builder/panels/class-fields.php:467
msgid "You don't have any fields yet. Add some!"
msgstr "您还没有任何字段。添加一些！"

#: includes/admin/builder/panels/class-fields.php:451
msgid "You don't have any fields yet."
msgstr "您还没有任何字段。"

#: includes/admin/builder/panels/class-fields.php:184
msgid "Payment Fields"
msgstr "付款字段"

#: includes/admin/builder/panels/class-fields.php:180
msgid "Fancy Fields"
msgstr "功能字段"

#: includes/admin/builder/panels/class-fields.php:176
msgid "Standard Fields"
msgstr "标准字段"

#: includes/admin/builder/panels/class-fields.php:92
msgid "You need to <a href=\"#\" class=\"wpforms-panel-switch\" data-panel=\"setup\">setup your form</a> before you can manage the fields."
msgstr "在管理字段之前，您需要先<a href=\"#\" class=\"wpforms-panel-switch\" data-panel=\"setup\">开始表单</a>。"

#: includes/admin/builder/panels/class-fields.php:65
msgid "Field Options"
msgstr "字段选项"

#: includes/admin/builder/panels/class-fields.php:59
msgid "Add Fields"
msgstr "添加字段"

#: includes/admin/builder/panels/class-fields.php:18
msgid "Fields"
msgstr "字段"

#: src/Admin/Builder/Shortcuts.php:49
msgid "Save Form"
msgstr "保存表单"

#: includes/admin/builder/class-builder.php:539
#: includes/admin/builder/class-builder.php:829
msgid "Embed"
msgstr "嵌入"

#: src/Admin/Builder/Shortcuts.php:51
msgid "Embed Form"
msgstr "嵌入表单"

#: includes/admin/builder/class-builder.php:799
msgid "Now editing"
msgstr "正在编辑"

#: includes/admin/builder/class-builder.php:692
msgid "Days"
msgstr "天数"

#: includes/admin/builder/class-builder.php:688
msgid "Months"
msgstr "月"

#: includes/admin/builder/class-builder.php:684
msgid "States Postal Code"
msgstr "省邮政编码"

#: includes/admin/builder/class-builder.php:680
msgid "States"
msgstr "状态"

#: includes/admin/builder/class-builder.php:676
msgid "Countries Postal Code"
msgstr "国家邮政编码"

#: includes/admin/builder/class-builder.php:672
msgid "Countries"
msgstr "国家"

#: includes/admin/builder/class-builder.php:582
msgid "Add"
msgstr "新增"

#: includes/admin/builder/class-builder.php:578
msgid "--- Select Choice ---"
msgstr "--- 选择选项 ---"

#: includes/admin/builder/class-builder.php:576
msgid "Hide Smart Tags"
msgstr "隐藏智能标签"

#: includes/admin/builder/class-builder.php:572
msgid "Delete rule"
msgstr "删除规则"

#: includes/admin/builder/class-builder.php:570
msgid "Create new rule"
msgstr "创建新规则"

#: includes/admin/builder/class-builder.php:565
msgid "Previous"
msgstr "上一条"

#: includes/admin/builder/class-builder.php:562
msgid "less than"
msgstr "少于"

#: includes/admin/builder/class-builder.php:561
msgid "greater than"
msgstr "多于"

#: includes/admin/builder/class-builder.php:560
msgid "ends with"
msgstr "结束于"

#: includes/admin/builder/class-builder.php:559
msgid "starts with"
msgstr "开始于"

#: includes/admin/builder/class-builder.php:558
msgid "does not contain"
msgstr "不包含"

#: includes/admin/builder/class-builder.php:557
msgid "contains"
msgstr "包含"

#: includes/admin/builder/class-builder.php:556
msgid "not empty"
msgstr "非空"

#: includes/admin/builder/class-builder.php:555
msgid "empty"
msgstr "空白"

#: includes/admin/builder/class-builder.php:554
msgid "is not"
msgstr "不是"

#: includes/admin/builder/class-builder.php:553
msgid "is"
msgstr "是"

#: includes/admin/builder/class-builder.php:551
msgid "or"
msgstr "或"

#: includes/admin/builder/class-builder.php:548
msgid "This item must contain at least one choice."
msgstr "此项目必须包含至少一个选项。"

#: includes/admin/builder/class-builder.php:547
msgid "Please enter a form name."
msgstr "请输入一个表单名称。"

#: includes/admin/builder/class-builder.php:546
msgid "(copy)"
msgstr "(复制)"

#: includes/admin/builder/class-builder.php:545
msgid "Are you sure you want to duplicate this field?"
msgstr "是否确定复制此字段？"

#: includes/admin/builder/class-builder.php:543
msgid "Are you sure you want to delete this field?"
msgstr "您确定要删除此字段吗？"

#: includes/admin/builder/class-builder.php:540
msgid "Exit"
msgstr "退出"

#: includes/admin/builder/class-builder.php:538
msgid "Changing templates on an existing form will DELETE existing form fields. Are you sure you want apply the new template?"
msgstr "更改现有表单上的模板将删除现有表单字段。确实要应用新模板吗？"

#: includes/admin/builder/class-builder.php:537
#: templates/builder/templates-item.php:49
msgid "Use Template"
msgstr "使用模板"

#: includes/admin/builder/class-builder.php:531
msgid "Loading"
msgstr "加载"

#: includes/admin/builder/class-builder.php:530
msgid "Select your column"
msgstr "选择您的列"

#: includes/admin/builder/class-builder.php:529
msgid "Select your layout"
msgstr "选择您的布局"

#: includes/admin/builder/class-builder.php:528
msgid "Hide Layouts"
msgstr "隐藏布局"

#: includes/admin/builder/class-builder.php:524
msgid "Save and Exit"
msgstr "保存并退出"

#: includes/admin/builder/class-builder.php:523
msgid "Saved!"
msgstr "已保存!"

#: src/Admin/Education/Core.php:106
msgid "Saving ..."
msgstr "保存中…"

#: includes/admin/builder/class-builder.php:521
#: includes/admin/builder/class-builder.php:836
msgid "Save"
msgstr "保存"

#: includes/admin/builder/class-builder.php:519
msgid "You must provide a confirmation name"
msgstr "您必须提供确认名称"

#: includes/admin/builder/class-builder.php:518
msgid "Eg: Alternative Confirmation"
msgstr "例如：替代确认"

#: includes/admin/builder/class-builder.php:517
msgid "Enter a confirmation name"
msgstr "输入确认名称"

#: includes/admin/builder/class-builder.php:516
msgid "Are you sure you want to delete this confirmation?"
msgstr "您确定要删除此确认吗？"

#: includes/admin/builder/class-builder.php:514
msgid "You must provide a notification name"
msgstr "您必须提供通知名称"

#: includes/admin/builder/class-builder.php:513
msgid "Eg: User Confirmation"
msgstr "例如：用户确认"

#: includes/admin/builder/class-builder.php:512
msgid "Enter a notification name"
msgstr "输入通知名称"

#: includes/admin/builder/class-builder.php:511
msgid "Are you sure you want to delete this notification?"
msgstr "您确定要删除此通知吗？"

#: includes/admin/builder/class-builder.php:510
msgid "No email fields"
msgstr "没有电子邮件字段"

#: includes/admin/builder/class-builder.php:505
msgid "No fields available"
msgstr "没有可用的字段"

#: includes/admin/builder/class-builder.php:504
msgid "Available Fields"
msgstr "可用字段"

#: includes/admin/builder/class-builder.php:501
msgid "This field cannot be deleted or duplicated."
msgstr "无法删除或复制此字段。"

#: includes/admin/builder/class-builder.php:500
msgid "Field Locked"
msgstr "字段锁定"

#: includes/admin/builder/class-builder.php:499
msgid "Field"
msgstr "字段"

#: includes/admin/builder/class-builder.php:498
msgid "Are you sure you want to disable conditional logic? This will remove the rules for this field or setting."
msgstr "您确定要禁用条件逻辑吗？这将删除此字段或设置的规则。"

#: includes/admin/builder/class-builder.php:497
msgid "Due to form changes, conditional logic rules will be removed or updated:"
msgstr "由于表单更改，条件逻辑规则将被删除或更新。"

#: includes/admin/builder/class-builder.php:483
msgid "Hide presets"
msgstr "隐藏预设"

#: includes/admin/builder/class-builder.php:482
msgid "Show presets"
msgstr "显示预设"

#: includes/admin/builder/class-builder.php:481
msgid ""
"Blue\n"
"Red\n"
"Green"
msgstr ""
"蓝\n"
"红\n"
"绿"

#: includes/admin/builder/class-builder.php:480
msgid "Add Choices (one per line)"
msgstr "添加选项（每行一个）"

#: includes/admin/builder/class-builder.php:479
msgid "Hide Bulk Add"
msgstr "隐藏批量添加"

#: includes/admin/builder/class-builder.php:476
msgid "Add New Choices"
msgstr "添加新选项"

#. translators: $1$s - WPForms plugin name; $2$s - WP.org review link; $3$s -
#. WP.org review link.
#: includes/admin/class-review.php:224
msgid "Please rate %1$s <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">&#9733;&#9733;&#9733;&#9733;&#9733;</a> on <a href=\"%3$s\" target=\"_blank\" rel=\"noopener\">WordPress.org</a> to help us spread the word. Thank you from the WPForms team!"
msgstr "请评价 %1$s <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">&#9733;&#9733;&#9733;&#9733;&#9733;</a>在<a href=\"%3$s\" target=\"_blank\" rel=\"noopener\">WordPress.org</a>，帮助我们宣传。 来自WPForms团队的感谢！"

#: includes/admin/class-review.php:166
msgid "Hey, I noticed you created a contact form with WPForms - that’s awesome! Could you please do me a BIG favor and give it a 5-star rating on WordPress to help us spread the word and boost our motivation?"
msgstr "嘿，我注意到您用WPForms创建了一个联系表单-太棒了！您能帮我一个大忙，在WordPress上给它5星级的评价，帮助我们传播插件，提高我们的积极性吗？"

#: includes/admin/class-review.php:108 includes/admin/class-review.php:171
msgid "I already did"
msgstr "我已经做了"

#: includes/admin/class-review.php:107 includes/admin/class-review.php:170
msgid "Nope, maybe later"
msgstr "不，以后再说"

#: includes/admin/class-review.php:106 includes/admin/class-review.php:169
msgid "Ok, you deserve it"
msgstr "好，您应得的"

#: includes/admin/class-review.php:104 includes/admin/class-review.php:167
msgid "~ Syed Balkhi<br>Co-Founder of WPForms"
msgstr "~ Syed Balkhi<br>WPForms表单的联合创始人之一"

#: includes/admin/class-review.php:103
msgid "Hey, I noticed you collected over 50 entries from WPForms - that’s awesome! Could you please do me a BIG favor and give it a 5-star rating on WordPress to help us spread the word and boost our motivation?"
msgstr "嘿，我注意到您从WPForms上收集了超过50个条目——太棒了!您能帮我一个大忙吗?帮我在WordPress上给它一个5星的评级，帮助我们传播这个词，提高我们的动力?"

#: includes/admin/class-menu.php:64
#: includes/admin/overview/class-overview.php:163
#: src/Admin/AdminBarMenu.php:257
msgid "Add New"
msgstr "新增"

#: includes/admin/overview/class-overview.php:160
msgid "Forms Overview"
msgstr "表单概述"

#: includes/admin/overview/class-overview.php:111
msgid "Number of forms per page:"
msgstr "每页表单数："

#. translators: %s - Duplicated forms count.
#: includes/admin/overview/class-overview.php:229
msgid "%s form was successfully duplicated."
msgid_plural "%s forms were successfully duplicated."
msgstr[0] "已成功复制%s表单。"

#: includes/admin/overview/class-overview.php:236
msgid "Security check failed. Please try again."
msgstr "安全检查失败。请再试一次。"

#. translators: %s - Deleted forms count.
#: includes/admin/overview/class-overview.php:221
msgid "%s form was successfully deleted."
msgid_plural "%s forms were successfully deleted."
msgstr[0] "已成功删除%s表单。"

#: includes/admin/overview/class-overview-table.php:338
msgid "Delete this form"
msgstr "删除此表单"

#: includes/admin/overview/class-overview-table.php:318
msgid "Duplicate"
msgstr "重复"

#: includes/admin/overview/class-overview-table.php:317
msgid "Duplicate this form"
msgstr "复制表单"

#: includes/admin/overview/class-overview-table.php:199
#: includes/admin/overview/class-overview-table.php:296
msgid "View preview"
msgstr "查看预览"

#: includes/admin/overview/class-overview-table.php:216
#: includes/admin/overview/class-overview-table.php:286
msgid "View entries"
msgstr "查看条目"

#: includes/admin/overview/class-overview-table.php:233
#: includes/admin/overview/class-overview-table.php:268
msgid "Edit This Form"
msgstr "编辑此表单"

#: includes/admin/overview/class-overview-table.php:64
msgid "Created"
msgstr "创建"

#: includes/admin/overview/class-overview-table.php:63
msgid "Shortcode"
msgstr "短代码"

#: src/Admin/SiteHealth.php:77 src/Admin/SiteHealth.php:85
#: src/Admin/Tools/Views/System.php:117 src/Admin/Tools/Views/System.php:121
msgid "M j, Y @ g:ia"
msgstr "Y年n月j日@ H：i"

#: src/Admin/Tools/Views/Import.php:241 src/Admin/Tools/Views/Import.php:254
msgid "Error"
msgstr "错误"

#: src/Admin/Tools/Views/Import.php:240
msgid "Please upload a valid .json form export file."
msgstr "请上载有效的.json表单导出文件。"

#: src/Admin/Tools/Views/System.php:69
msgid "Test Connection"
msgstr "测试连接"

#: src/Admin/Tools/Views/System.php:67
msgid "Click the button below to verify your web server can perform SSL connections successfully."
msgstr "单击下面的按钮以验证您的Web服务器是否可以成功执行SSL连接。"

#: src/Admin/Tools/Views/System.php:66
msgid "Test SSL Connections"
msgstr "测试SSL连接"

#: src/Admin/Tools/Views/System.php:61
msgid "System Information"
msgstr "系统信息"

#: src/Admin/Tools/Views/Export.php:213
msgid "Export Template"
msgstr "导出模板"

#: src/Admin/Tools/Views/Export.php:217
msgid "You need to create a form before you can generate a template."
msgstr "您需要先创建表单，然后才能生成模板。"

#: src/Admin/Tools/Views/Export.php:204
msgid "Select a form to generate PHP code that can be used to register a custom form template."
msgstr "选择一个表单以生成可用于注册自定义表单模板的PHP代码。"

#. translators: %s - WPForms.com docs URL.
#: src/Admin/Tools/Views/Export.php:185
msgid "For more information <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">see our documentation</a>."
msgstr "有关更多信息，请<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">查看我们的文档</a>。"

#: src/Admin/Tools/Views/Export.php:197
msgid "The following code can be used to register your custom form template. Copy and paste the following code to your theme's functions.php file or include it within an external file."
msgstr "以下代码可用于注册自定义表单模板。将以下代码复制并粘贴到主题的functions.php文件中，或将其包含在外部文件中。"

#: src/Admin/Tools/Views/Export.php:178
msgid "Form Template Export"
msgstr "表单模板导出"

#: src/Admin/Tools/Views/Export.php:162
msgid "You need to create a form before you can use form export."
msgstr "在使用表单导出之前，您需要创建一个表单。"

#: src/Admin/Tools/Views/Export.php:153
msgid "Select Form(s)"
msgstr "选择表单"

#: src/Admin/Tools/Views/Export.php:148
msgid "Form exports files can be used to create a backup of your forms or to import forms into another site."
msgstr "表单导出文件可用于创建表单备份或将表单导入其他网站。"

#: src/Admin/Tools/Views/Export.php:146
msgid "Form Export"
msgstr "表单导出"

#: src/Admin/Tools/Views/Importer.php:361
msgid "Upgrade to the PRO plan to import these fields."
msgstr "升级到PRO计划以导入这些字段。"

#: src/Admin/Tools/Views/Importer.php:352
msgid "The following fields are not supported and were not imported:"
msgstr "以下字段不受支持且未导入："

#: src/Admin/Tools/Views/Importer.php:344
msgid "The following fields are available in PRO and were imported as text fields:"
msgstr "以下字段在专业版中可用，并作为文本字段导入："

#: src/Admin/Tools/Views/Importer.php:336
msgid "The following fields are available in PRO and were not imported:"
msgstr "以下字段在专业版中可用，但未导入："

#: includes/admin/builder/class-builder.php:822
#: includes/admin/overview/class-overview-table.php:297
#: src/Admin/Settings/Captcha.php:233 src/Admin/Tools/Views/Importer.php:332
msgid "Preview"
msgstr "预览"

#: includes/admin/overview/class-overview-table.php:269
#: src/Admin/Tools/Views/Importer.php:330
msgid "Edit"
msgstr "编辑"

#: src/Admin/Tools/Views/Importer.php:234
msgid "Below is the list of form fields that may be impacted:"
msgstr "以下是可能受影响的表单字段列表："

#: src/Admin/Tools/Views/Importer.php:231
msgid "Continue Import without Upgrading"
msgstr "继续导入而不升级"

#: src/Admin/Tools/Views/Importer.php:225
msgid "You can continue with the import without upgrading, and we will do our best to match the fields. However, some of them will be omitted due to compatibility issues."
msgstr "您可以继续导入而无需升级，我们将尽力匹配这些字段。但是，由于兼容性问题，其中一些将被省略。"

#: src/Admin/Tools/Views/Importer.php:224
msgid "One or more of your forms contain fields that are not available in WPForms Lite. To properly import these fields, we recommend upgrading to WPForms Pro."
msgstr "您的一个或多个表单包含WPForms Lite中不可用的字段。要正确导入这些字段，我们建议您升级到WPForms Pro。"

#: src/Admin/Tools/Views/Importer.php:223
msgid "Heads Up!"
msgstr "小心!"

#: src/Admin/Tools/Views/Importer.php:181
msgid "Forms to Import"
msgstr "要导入的表单"

#: src/Admin/Tools/Views/Importer.php:176
msgid "Select All"
msgstr "选择所有"

#: includes/admin/overview/class-overview-table.php:405
#: src/Admin/Tools/Views/Importer.php:162
msgid "No forms found."
msgstr "没有找到表单。"

#: src/Admin/Tools/Views/Importer.php:157
msgid "Available Forms"
msgstr "可用表单"

#: src/Admin/Tools/Views/Importer.php:153
msgid "Select the forms you would like to import."
msgstr "选择要导入的表单。"

#: src/Admin/Tools/Views/Importer.php:137
msgid "Form Import"
msgstr "表单导入"

#: src/Admin/Tools/Views/Import.php:196
msgid "Not Active"
msgstr "未启用"

#: includes/admin/class-about.php:428 src/Admin/Tools/Views/Import.php:194
msgid "Not Installed"
msgstr "没有安装"

#: src/Admin/Tools/Views/Import.php:188
msgid "Select previous contact form plugin..."
msgstr "选择以前的联系表单插件…"

#: src/Admin/Tools/Views/Import.php:183
msgid "No form importers are currently enabled."
msgstr "当前未启用表单导入程序。"

#: src/Admin/Tools/Views/Import.php:179
msgid "WPForms makes it easy for you to switch by allowing you import your third-party forms with a single click."
msgstr "WPForms允许您只需单击一次即可导入第三方表单，从而使您可以轻松地进行切换。"

#: src/Admin/Tools/Views/Import.php:178
msgid "Not happy with other WordPress contact form plugins?"
msgstr "对其他WordPress联系表单插件不满意？"

#: src/Admin/Tools/Views/Import.php:177
msgid "Import from Other Form Plugins"
msgstr "从其他表单插件导入"

#: src/Admin/Tools/Views/Import.php:153
msgid "Choose a file&hellip;"
msgstr "选择一个文件&hellip;"

#: src/Admin/Tools/Views/Import.php:151
msgid "No file chosen"
msgstr "没有选择文件"

#: src/Admin/Tools/Views/Import.php:148
msgid "files selected"
msgstr "已选的文件"

#: src/Admin/Tools/Views/Import.php:143
msgid "Select a WPForms export file."
msgstr "选择WPForms导出文件。"

#: src/Admin/Tools/Views/Import.php:142
msgid "WPForms Import"
msgstr "WPForms导入"

#: src/Admin/Tools/Views/System.php:37
msgid "System Info"
msgstr "系统信息"

#: src/Admin/Tools/Views/Export.php:58 src/Admin/Tools/Views/Export.php:158
msgid "Export"
msgstr "导出"

#: src/Admin/Tools/Views/Import.php:67 src/Admin/Tools/Views/Import.php:160
#: src/Admin/Tools/Views/Import.php:213 src/Admin/Tools/Views/Importer.php:190
msgid "Import"
msgstr "导入"

#: src/Admin/Tools/Importers/Base.php:111
msgid "There was an error while creating a new form."
msgstr "创建新表单时出错。"

#. translators: %1$s - field type; %2$s - field name if available.
#: src/Admin/Tools/Importers/ContactForm7.php:537
msgid "%1$s Field %2$s"
msgstr "%1$s 字段 %2$s"

#: src/Admin/Tools/Importers/ContactForm7.php:445
msgid "Notification 2"
msgstr "通知2"

#: src/Admin/Tools/Importers/ContactForm7.php:337
msgid "Acceptance Field"
msgstr "接受字段"

#: src/Admin/Tools/Importers/ContactForm7.php:102
msgid "The form you are trying to import does not exist."
msgstr "您试图导入的表单不存在。"

#: src/Admin/Tools/Importers/ContactForm7.php:101
msgid "Unknown Form"
msgstr "未知表单"

#: src/Admin/Tools/Importers/NinjaForms.php:429
#: src/Admin/Tools/Importers/NinjaForms.php:444
msgid "Notification"
msgstr "通知"

#: src/Admin/Tools/Importers/ContactForm7.php:130
#: src/Admin/Tools/Importers/NinjaForms.php:135
msgid "Notification 1"
msgstr "通知1"

#: includes/admin/builder/panels/class-fields.php:150
#: includes/admin/builder/panels/class-settings.php:106
#: includes/class-form.php:358 src/Admin/Tools/Importers/ContactForm7.php:124
#: src/Admin/Tools/Importers/NinjaForms.php:129
msgid "Submit"
msgstr "提交评论"

#: src/Admin/Tools/Importers/ContactForm7.php:125
#: src/Admin/Tools/Importers/NinjaForms.php:130
#: src/Admin/Tools/Importers/PirateForms.php:453
msgid "Sending"
msgstr "发送"

#: src/Admin/Tools/Importers/ContactForm7.php:157
#: src/Admin/Tools/Importers/NinjaForms.php:162
#: src/Admin/Tools/Importers/PirateForms.php:439
msgid "No form fields found."
msgstr "找不到表单字段。"

#: src/Admin/Tools/Importers/NinjaForms.php:240
#: src/Admin/Tools/Importers/PirateForms.php:251
#: src/Admin/Tools/Importers/PirateForms.php:345
msgid "Single Checkbox Field"
msgstr "单个复选框字段"

#: src/Admin/Tools/Importers/PirateForms.php:91
#: src/Admin/Tools/Importers/PirateForms.php:180
msgid "Default Form"
msgstr "默认表单"

#. translators: %s - WPForms.com upgrade from Lite to paid docs page URL.
#: includes/admin/admin.php:510
msgid "Check out <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">our documentation</a> for step-by-step instructions."
msgstr "有关分步说明，请查看 <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">我们的文档</a>."

#: includes/admin/admin.php:505
msgid "(Don't worry, all your forms and settings will be preserved.)"
msgstr "(别担心,您所有的表单和设置都会保存下来.)"

#. translators: %s - WPForms.com contact page URL.
#: includes/admin/admin.php:482
msgid "If you have any questions or issues just <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">let us know</a>."
msgstr "如果您有任何问题或疑问，请<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">告诉我们</a>."

#: includes/admin/admin.php:442
msgid "<strong>Please Note:</strong> Support for PHP 5.5 will be discontinued in 2020. After this, if no further action is taken, WPForms functionality will be disabled."
msgstr "<strong>请注意：</strong>2020年将停止对PHP5.5的支持。在此之后，如果不采取进一步的操作，WPForms功能将被禁用。"

#. translators: %1$s - WPForms plugin name; %2$s - WPForms.com URL to a related
#. doc.
#: includes/admin/admin.php:428
msgid "Your site is running an outdated version of PHP that is no longer supported and may cause issues with %1$s. <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">Read more</a> for additional information."
msgstr "您的站点正在运行不再受支持的过时版本的PHP，可能会导致 %1$s. 出现问题。有关其他信息，请<a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">了解详情</a>."

#: includes/admin/admin.php:209
msgid "Press to select"
msgstr "点击选择"

#: includes/admin/admin.php:208
msgid "No choices to choose from"
msgstr "没有选择可供选择"

#: includes/admin/admin.php:207
msgid "No results found"
msgstr "找不到结果"

#: includes/admin/admin.php:206 includes/fields/class-base.php:2200
msgid "Loading..."
msgstr "载入中…"

#: includes/admin/admin.php:204 includes/admin/builder/class-builder.php:580
msgid "Use Image"
msgstr "使用图片"

#: includes/admin/admin.php:203 includes/admin/builder/class-builder.php:579
msgid "Upload or Choose Your Image"
msgstr "上传或选择图像"

#: includes/admin/admin.php:202
msgid "Upgrade was successfully completed!"
msgstr "升级成功完成！"

#: includes/admin/admin.php:201
msgid "Testing"
msgstr "测试"

#. translators: %s - WPForms.com docs page URL.
#: includes/admin/admin.php:189
msgid "You've selected <strong>No Styling</strong>, which will likely result in significant styling issues and is recommended only for developers. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Please check out our tutorial</a> for more details and recommendations."
msgstr "您已选择<strong>无样式</strong>，这可能会导致显著的样式问题，建议只针对开发人员。更多细节和建议<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">请看我们的教程</a>。"

#. translators: %s - WPForms.com docs page URL.
#: includes/admin/admin.php:175
msgid "You've selected <strong>Base Styling Only</strong>, which may result in styling issues. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Please check out our tutorial</a> for common issues and recommendations."
msgstr "您已选择 <strong>仅基本样式</strong>，这可能导致样式问题。针对常见问题和建议<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">请看我们的教程</a>。"

#: includes/admin/admin.php:171
msgid "Save and Refresh"
msgstr "保存并更新"

#: includes/admin/admin.php:170
msgid "Could not authenticate with the provider."
msgstr "无法与提供商进行身份验证。"

#: includes/admin/admin.php:168
msgid "Are you sure you want to disconnect this account?"
msgstr "您确定要断开此帐户吗？"

#: includes/admin/admin.php:167
msgid "needs to be activated to import its forms. Would you like us to activate it for you?"
msgstr "需要激活才能导入其表单。您是否希望我们为您激活它？"

#: includes/admin/admin.php:165
msgid "needs to be installed and activated to import its forms. Would you like us to install and activate it for you?"
msgstr "需要安装和激活才能导入其表单。您是否希望我们为您安装并激活它？"

#: includes/admin/admin.php:164
msgid "Install and Activate"
msgstr "安装并启用它"

#: includes/admin/admin.php:163 includes/admin/builder/class-builder.php:495
msgid "OK"
msgstr "OK"

#: includes/admin/admin.php:158
msgid "Please select at least one form to import."
msgstr "请选择至少一个要导入的表单。"

#: includes/admin/admin.php:157 includes/admin/builder/class-builder.php:506
#: src/Admin/Education/Builder/Captcha.php:156
#: src/Integrations/Gutenberg/FormSelector.php:104 wpforms.php:148
msgid "Heads up!"
msgstr "小心！"

#: includes/admin/admin.php:156
msgid "Are you sure you want to duplicate this form?"
msgstr "您确定要复制此表单吗？"

#: includes/admin/builder/class-builder.php:544
msgid "Are you sure you want to delete this choice?"
msgstr "您确定要删除此选择吗？"

#: includes/admin/admin.php:154
msgid "Mark entry unread"
msgstr "标记条目未读"

#: includes/admin/admin.php:153
msgid "Mark entry read"
msgstr "标记条目已读"

#: includes/admin/admin.php:152
msgid "Star entry"
msgstr "标星"

#: includes/admin/admin.php:151
msgid "Unstar entry"
msgstr "取消标星"

#: includes/admin/admin.php:150
msgid "Are you sure you want to delete this note?"
msgstr "确实要删除此注释吗？"

#: includes/admin/admin.php:149
msgid "Entries Field Columns"
msgstr "条目字段列"

#: includes/admin/admin.php:148
msgid "Show Empty Fields"
msgstr "显示空字段"

#: includes/admin/admin.php:147
msgid "Hide Empty Fields"
msgstr "隐藏空字段"

#: includes/admin/admin.php:137
msgid "Searching Addons"
msgstr "搜索扩展"

#: includes/admin/admin.php:134
msgid "Install Addon"
msgstr "安装扩展"

#: includes/admin/admin.php:133 includes/admin/class-about.php:414
msgid "Inactive"
msgstr "未启用"

#: includes/admin/admin.php:132
msgid "Deactivate"
msgstr "停用"

#: includes/admin/admin.php:131 includes/admin/class-about.php:406
msgid "Active"
msgstr "活动"

#: includes/admin/admin.php:130 includes/admin/class-about.php:409
msgid "Activated"
msgstr "已启用"

#: includes/admin/admin.php:129 includes/admin/admin.php:166
#: includes/admin/class-about.php:417
#: templates/education/admin/settings/geolocation/submit.php:24
msgid "Activate"
msgstr "启用"

#: includes/admin/admin.php:139 includes/admin/builder/class-builder.php:494
#: includes/admin/class-editor.php:158 src/Admin/Education/Core.php:91
msgid "Cancel"
msgstr "取消"

#. translators: %s - WPForms Builder page.
#: includes/admin/class-editor.php:142
msgid "Whoops, you haven't created a form yet. Want to <a href=\"%s\">give it a go</a>?"
msgstr "哎呀，您还没有创建一个表单。想要<a href=\"%s\">试一试</a>？"

#: includes/admin/class-editor.php:136
msgid "Show form description"
msgstr "显示表单说明"

#: includes/admin/class-editor.php:135
msgid "Show form name"
msgstr "显示表单名称"

#: includes/admin/class-editor.php:129
msgid "Select a form below to insert"
msgstr "选择下面的表单进行插入"

#. translators: %s - WPForms documentation URL.
#: includes/admin/class-editor.php:114
msgid "Heads up! Don't forget to test your form. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Check out our complete guide</a>!"
msgstr "小心！别忘了测试一下您的表单。 <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">查看我们的完整指南</a>！"

#: includes/admin/admin.php:140 includes/admin/builder/class-builder.php:496
#: includes/admin/class-editor.php:105 src/Admin/Education/Core.php:92
#: templates/builder/help.php:23
msgid "Close"
msgstr "关闭"

#: includes/admin/class-editor.php:104
msgid "Insert Form"
msgstr "插入表单"

#: includes/admin/class-editor.php:46 includes/admin/class-editor.php:49
#: includes/admin/class-editor.php:162
msgid "Add Form"
msgstr "添加表单"

#: includes/admin/class-welcome.php:309
msgid "As a business owner, time is my most valuable asset. WPForms allow me to create smart online forms with just a few clicks. With their pre-built form templates and the drag & drop builder, I can create a new form that works in less than 2 minutes without writing a single line of code. Well worth the investment."
msgstr "作为一名企业主，时间是我最宝贵的资产。WPForms让我只需单机几下就可创建人性化的在线表单。通过其预制的表单模板和拖拽式创建工具，我可以快速的在2分钟内创建一个新的表单而无需编写一行代码。非常值得投资。"

#: includes/admin/class-welcome.php:302
msgid "WPForms is by far the easiest form plugin to use. My clients love it – it’s one of the few plugins they can use without any training. As a developer I appreciate how fast, modern, clean and extensible it is."
msgstr "WPForms是迄今为止最容易使用的表单插件。我的客户很喜欢它——它是少数几个不需要任何培训就可以使用的插件之一。作为一个开发人员，我很欣赏它的快速、现代、干净和可扩展性。"

#: includes/admin/class-welcome.php:298
msgid "Testimonials"
msgstr "客户评价"

#: includes/admin/class-welcome.php:282
msgid "per year"
msgstr "每年"

#: includes/admin/class-welcome.php:279
msgid "PRO"
msgstr "专业版"

#: includes/admin/class-about.php:1468
msgid "Priority Support"
msgstr "优先支持"

#: includes/admin/class-about.php:1512 includes/admin/class-about.php:1518
#: includes/admin/class-about.php:1524
msgid "Unlimited Sites"
msgstr "无限网站"

#: includes/admin/class-settings.php:199 includes/admin/class-welcome.php:271
#: templates/education/admin/settings/geolocation/heading.php:16
msgid "Geolocation"
msgstr "地理位置"

#: includes/admin/class-welcome.php:268
msgid "Form Abandonment"
msgstr "表单废弃"

#: includes/admin/class-welcome.php:273
msgid "User Registration"
msgstr "用户注册"

#: includes/admin/class-welcome.php:267
msgid "Signatures"
msgstr "签名"

#: includes/admin/class-welcome.php:248
msgid "See All Features"
msgstr "查看全部功能"

#: includes/admin/class-welcome.php:239
msgid "Spam Protection"
msgstr "垃圾内容防护"

#: includes/admin/class-welcome.php:234
msgid "Easily embed your forms in blog posts, pages, sidebar widgets, footer, etc."
msgstr "轻松地将表单嵌入博客文章、页面、边栏小工具、页脚等。"

#: includes/admin/class-welcome.php:233
msgid "Easy to Embed"
msgstr "易于嵌入"

#: includes/admin/class-welcome.php:228
msgid "Create subscription forms and connect with your email marketing service."
msgstr "创建订阅表单并连接到您的电子邮件营销服务。"

#: includes/admin/class-welcome.php:227
msgid "Marketing &amp; Subscriptions"
msgstr "营销和订阅"

#: includes/admin/class-welcome.php:222
msgid "Easily collect payments, donations, and online orders without hiring a developer."
msgstr "无需聘请开发人员即可轻松收集付款、捐款和在线订单。"

#: includes/admin/class-welcome.php:221
msgid "Payments Made Easy"
msgstr "支付方便"

#: includes/admin/class-welcome.php:216
msgid "View all your leads in one place to streamline your workflow."
msgstr "在一处查看您的所有结果，以简化您的工作流程。"

#: includes/admin/class-welcome.php:215 includes/admin/class-welcome.php:269
msgid "Entry Management"
msgstr "进入管理"

#: includes/admin/class-welcome.php:210
msgid "Respond to leads quickly with our instant form notification feature for your team."
msgstr "使用我们为您的团队提供的即时表单通知功能快速回复潜在客户。"

#: includes/admin/class-welcome.php:209
msgid "Instant Notifications"
msgstr "即时通知"

#: includes/admin/class-welcome.php:204
msgid "Easily create high performance forms with our smart conditional logic."
msgstr "使用我们的人性化条件逻辑轻松创建高性能表单。"

#: includes/admin/class-about.php:76 includes/admin/class-welcome.php:203
#: src/Lite/Admin/Education/Builder/Fields.php:79
msgid "Smart Conditional Logic"
msgstr "智能条件逻辑"

#: includes/admin/class-welcome.php:198
msgid "WPForms is 100% responsive meaning it works on mobile, tablets & desktop."
msgstr "WPForms是100%响应意味着它可以在手机、平板和电脑上工作。"

#: includes/admin/class-welcome.php:197
msgid "Responsive Mobile Friendly"
msgstr "响应式移动友好"

#: includes/admin/class-welcome.php:192
msgid "Start with pre-built form templates to save even more time."
msgstr "从预先构建的表单模板开始，以节省更多时间。"

#: includes/admin/class-about.php:75 includes/admin/class-welcome.php:191
msgid "Form Templates"
msgstr "表单模板"

#: includes/admin/class-welcome.php:186
msgid "Easily create an amazing form in just a few minutes without writing any code."
msgstr "不需要编写任何代码，只需几分钟就可以轻松创建一个令人惊叹的表单。"

#: includes/admin/class-welcome.php:185
msgid "Drag &amp; Drop Form Builder"
msgstr "拖拽是表单构建器"

#: includes/admin/class-welcome.php:179
msgid "WPForms is both easy to use and extremely powerful. We have tons of helpful features that allow us to give you everything you need from a form builder."
msgstr "WPForms既易于使用又非常强大。我们有很多实用的功能，可为您提供表单生成器所需的一切。"

#: includes/admin/class-welcome.php:178
msgid "WPForms Features &amp; Addons"
msgstr "WPForms功能和扩展"

#: includes/admin/class-welcome.php:163
msgid "Read the Full Guide"
msgstr "阅读完整指南"

#: includes/admin/class-welcome.php:157 includes/admin/class-welcome.php:326
msgid "Create Your First Form"
msgstr "创建您的第一个表单"

#: includes/admin/class-welcome.php:152
msgid "WPForms makes it easy to create forms in WordPress. You can watch the video tutorial or read our guide on how create your first form."
msgstr "WPForms使得在WordPress中创建表单变得容易。您可以观看视频教程或阅读有关如何创建第一个表单的指南。"

#: includes/admin/class-welcome.php:146 includes/admin/class-welcome.php:147
msgid "Watch how to create your first form"
msgstr "观看如何创建您的第一个表单"

#: includes/admin/class-welcome.php:143
msgid "Thank you for choosing WPForms - the most powerful drag & drop WordPress form builder in the market."
msgstr "感谢您选择WPForms--市面上最强大的拖拽式WordPress表单构建器。"

#: includes/admin/class-welcome.php:63 includes/admin/class-welcome.php:64
#: includes/admin/class-welcome.php:142
msgid "Welcome to WPForms"
msgstr "欢迎使用WPForms"

#: src/Admin/Tools/Views/Logs.php:51 src/Admin/Tools/Views/Logs.php:93
#: src/Logger/ListTable.php:39
msgid "Logs"
msgstr "日志"

#: includes/admin/class-settings.php:388 includes/class-frontend.php:1629
#: includes/functions.php:2020
msgid "This field is required."
msgstr "该字段是必填项。"

#: includes/functions.php:1136
msgid "Saturday"
msgstr "星期六"

#: includes/functions.php:1135
msgid "Friday"
msgstr "星期五"

#: includes/functions.php:1134
msgid "Thursday"
msgstr "星期四"

#: includes/functions.php:1133
msgid "Wednesday"
msgstr "星期三"

#: includes/functions.php:1132
msgid "Tuesday"
msgstr "星期二"

#: includes/functions.php:1131
msgid "Monday"
msgstr "星期一"

#: includes/functions.php:1130
msgid "Sunday"
msgstr "星期日"

#: includes/functions.php:1114
msgid "December"
msgstr "十二月"

#: includes/functions.php:1113
msgid "November"
msgstr "十一月"

#: includes/functions.php:1112
msgid "October"
msgstr "十月"

#: includes/functions.php:1111
msgid "September"
msgstr "九月"

#: includes/functions.php:1110
msgid "August"
msgstr "八月"

#: includes/functions.php:1109
msgid "July"
msgstr "七月"

#: includes/functions.php:1108
msgid "June"
msgstr "六月"

#: includes/functions.php:1107
msgid "May"
msgstr "五月"

#: includes/functions.php:1106
msgid "April"
msgstr "四月"

#: includes/functions.php:1105
msgid "March"
msgstr "三月"

#: includes/functions.php:1104
msgid "February"
msgstr "二月"

#: includes/functions.php:1103
msgid "January"
msgstr "一月"

#: includes/functions.php:1087
msgid "Zimbabwe"
msgstr "津巴布韦"

#: includes/functions.php:1086
msgid "Zambia"
msgstr "赞比亚"

#: includes/functions.php:1085
msgid "Yemen"
msgstr "也门"

#: includes/functions.php:1084
msgid "Western Sahara"
msgstr "西撒哈拉"

#: includes/functions.php:1083
msgid "Wallis and Futuna"
msgstr "瓦利斯群岛和富图纳群岛"

#: includes/functions.php:1082
msgid "Virgin Islands (U.S.)"
msgstr "维尔京群岛（美国）"

#: includes/functions.php:1081
msgid "Virgin Islands (British)"
msgstr "维尔京群岛（英属）"

#: includes/functions.php:1079
msgid "Venezuela (Bolivarian Republic of)"
msgstr "委内瑞拉（玻利瓦尔共和国）"

#: includes/functions.php:1078
msgid "Vatican City State"
msgstr "梵蒂冈城国"

#: includes/functions.php:1077
msgid "Vanuatu"
msgstr "瓦努阿图"

#: includes/functions.php:1076
msgid "Uzbekistan"
msgstr "乌兹别克斯坦"

#: includes/functions.php:1075
msgid "Uruguay"
msgstr "乌拉圭"

#: includes/functions.php:1074
msgid "United States Minor Outlying Islands"
msgstr "美国本土外小岛屿"

#: includes/functions.php:1073
msgid "United States of America"
msgstr "美国"

#: includes/functions.php:1072
msgid "United Kingdom of Great Britain and Northern Ireland"
msgstr "大不列颠及北爱尔兰联合王国"

#: includes/functions.php:1071
msgid "United Arab Emirates"
msgstr "阿拉伯联合酋长国"

#: includes/functions.php:1070
msgid "Ukraine"
msgstr "乌克兰"

#: includes/functions.php:1069
msgid "Uganda"
msgstr "乌干达"

#: includes/functions.php:1068
msgid "Tuvalu"
msgstr "图瓦卢"

#: includes/functions.php:1067
msgid "Turks and Caicos Islands"
msgstr "特克斯和凯科斯群岛"

#: includes/functions.php:1066
msgid "Turkmenistan"
msgstr "土库曼斯坦"

#: includes/functions.php:1065
msgid "Turkey"
msgstr "土耳其"

#: includes/functions.php:1064
msgid "Tunisia"
msgstr "突尼斯"

#: includes/functions.php:1063
msgid "Trinidad and Tobago"
msgstr "特立尼达和多巴哥"

#: includes/functions.php:1062
msgid "Tonga"
msgstr "汤加语"

#: includes/functions.php:1061
msgid "Tokelau"
msgstr "托克劳"

#: includes/functions.php:1060
msgid "Togo"
msgstr "多哥"

#: includes/functions.php:1059
msgid "Timor-Leste"
msgstr "东帝汶"

#: includes/functions.php:1058
msgid "Thailand"
msgstr "泰国"

#: includes/functions.php:1057
msgid "Tanzania (United Republic of)"
msgstr "坦桑尼亚联合共和国"

#: includes/functions.php:1056
msgid "Tajikistan"
msgstr "塔吉克斯坦斯坦斯坦"

#: includes/functions.php:1054
msgid "Syrian Arab Republic"
msgstr "阿拉伯叙利亚共和国"

#: includes/functions.php:1053
msgid "Switzerland"
msgstr "瑞士"

#: includes/functions.php:1052
msgid "Sweden"
msgstr "瑞典"

#: includes/functions.php:1050
msgid "Svalbard and Jan Mayen"
msgstr "斯瓦尔巴岛和扬马延岛"

#: includes/functions.php:1049
msgid "Suriname"
msgstr "苏里南"

#: includes/functions.php:1048
msgid "Sudan"
msgstr "苏丹"

#: includes/functions.php:1047
msgid "Sri Lanka"
msgstr "斯里兰卡"

#: includes/functions.php:1046
msgid "Spain"
msgstr "西班牙"

#: includes/functions.php:1045
msgid "South Sudan"
msgstr "南非"

#: includes/functions.php:1044
msgid "South Georgia and the South Sandwich Islands"
msgstr "南乔治亚及南桑威奇群岛"

#: includes/functions.php:1043
msgid "South Africa"
msgstr "南非"

#: includes/functions.php:1042
msgid "Somalia"
msgstr "索马里"

#: includes/functions.php:1041
msgid "Solomon Islands"
msgstr "所罗门群岛"

#: includes/functions.php:1040
msgid "Slovenia"
msgstr "斯洛文尼亚"

#: includes/functions.php:1039
msgid "Slovakia"
msgstr "斯洛伐克"

#: includes/functions.php:1038
msgid "Sint Maarten (Dutch part)"
msgstr "圣马丁岛（荷兰的一部分）"

#: includes/functions.php:1037
msgid "Singapore"
msgstr "新加坡"

#: includes/functions.php:1036
msgid "Sierra Leone"
msgstr "塞拉利昂"

#: includes/functions.php:1035
msgid "Seychelles"
msgstr "塞舌尔群岛"

#: includes/functions.php:1034
msgid "Serbia"
msgstr "塞尔维亚"

#: includes/functions.php:1033
msgid "Senegal"
msgstr "塞内加尔"

#: includes/functions.php:1032
msgid "Saudi Arabia"
msgstr "沙特阿拉伯"

#: includes/functions.php:1031
msgid "Sao Tome and Principe"
msgstr "圣多美和普林西比"

#: includes/functions.php:1030
msgid "San Marino"
msgstr "圣马力诺"

#: includes/functions.php:1029
msgid "Samoa"
msgstr "萨摩亚"

#: includes/functions.php:1028
msgid "Saint Vincent and the Grenadines"
msgstr "圣文森特和格林纳丁斯"

#: includes/functions.php:1027
msgid "Saint Pierre and Miquelon"
msgstr "圣皮埃尔和密克隆"

#: includes/functions.php:1026
msgid "Saint Martin (French part)"
msgstr "圣马丁（法国部分）"

#: includes/functions.php:1025
msgid "Saint Lucia"
msgstr "圣卢西亚"

#: includes/functions.php:1024
msgid "Saint Kitts and Nevis"
msgstr "圣基茨和尼维斯"

#: includes/functions.php:1023
msgid "Saint Helena, Ascension and Tristan da Cunha"
msgstr "圣赫勒拿，阿森松岛和特里斯坦达库尼亚"

#: includes/functions.php:1022
msgid "Saint Barthélemy"
msgstr "圣巴泰勒米"

#: includes/functions.php:1021
msgid "Rwanda"
msgstr "卢旺达"

#: includes/functions.php:1020
msgid "Russian Federation"
msgstr "俄罗斯联邦"

#: includes/functions.php:1019
msgid "Romania"
msgstr "罗马尼亚"

#: includes/functions.php:1018
msgid "Réunion"
msgstr "留尼汪岛"

#: includes/functions.php:1017
msgid "Qatar"
msgstr "卡塔尔"

#: includes/functions.php:1016
msgid "Puerto Rico"
msgstr "波多黎各"

#: includes/functions.php:1015
msgid "Portugal"
msgstr "葡萄牙"

#: includes/functions.php:1014
msgid "Poland"
msgstr "波兰"

#: includes/functions.php:1013
msgid "Pitcairn"
msgstr "皮特凯恩"

#: includes/functions.php:1012
msgid "Philippines"
msgstr "菲律宾"

#: includes/functions.php:1011
msgid "Peru"
msgstr "秘鲁"

#: includes/functions.php:1010
msgid "Paraguay"
msgstr "巴拉圭"

#: includes/functions.php:1009
msgid "Papua New Guinea"
msgstr "巴布亚新几内亚"

#: includes/functions.php:1008
msgid "Panama"
msgstr "巴拿马"

#: includes/functions.php:1007
msgid "Palestine (State of)"
msgstr "巴勒斯坦(国家)"

#: includes/functions.php:1006
msgid "Palau"
msgstr "帕劳共和国"

#: includes/functions.php:1005
msgid "Pakistan"
msgstr "巴基斯坦"

#: includes/functions.php:1004
msgid "Oman"
msgstr "阿曼"

#: includes/functions.php:1003
msgid "Norway"
msgstr "挪威"

#: includes/functions.php:1002
msgid "Northern Mariana Islands"
msgstr "北马里亚纳群岛"

#: includes/functions.php:1001
msgid "Norfolk Island"
msgstr "诺福克岛"

#: includes/functions.php:1000
msgid "Niue"
msgstr "纽埃"

#: includes/functions.php:999
msgid "Nigeria"
msgstr "尼日尔尔利亚"

#: includes/functions.php:998
msgid "Niger"
msgstr "尼日尔尔尔"

#: includes/functions.php:997
msgid "Nicaragua"
msgstr "尼加拉瓜"

#: includes/functions.php:996
msgid "New Zealand"
msgstr "新西兰"

#: includes/functions.php:995
msgid "New Caledonia"
msgstr "新喀里多尼亚"

#: includes/functions.php:994
msgid "Netherlands"
msgstr "荷兰"

#: includes/functions.php:993
msgid "Nepal"
msgstr "尼泊尔"

#: includes/functions.php:992
msgid "Nauru"
msgstr "瑙鲁语 (Naoero)"

#: includes/functions.php:991
msgid "Namibia"
msgstr "纳米比亚"

#: includes/functions.php:990
msgid "Myanmar"
msgstr "缅甸"

#: includes/functions.php:989
msgid "Mozambique"
msgstr "莫桑比克"

#: includes/functions.php:988
msgid "Morocco"
msgstr "摩洛哥"

#: includes/functions.php:987
msgid "Montserrat"
msgstr "蒙特塞拉特"

#: includes/functions.php:986
msgid "Montenegro"
msgstr "黑山"

#: includes/functions.php:985
msgid "Mongolia"
msgstr "蒙古国"

#: includes/functions.php:984
msgid "Monaco"
msgstr "摩纳哥"

#: includes/functions.php:983
msgid "Moldova (Republic of)"
msgstr "摩尔多瓦（共和国）"

#: includes/functions.php:982
msgid "Micronesia (Federated States of)"
msgstr "密克罗尼西亚（联邦）"

#: includes/functions.php:981
msgid "Mexico"
msgstr "墨西哥"

#: includes/functions.php:980
msgid "Mayotte"
msgstr "马约特岛"

#: includes/functions.php:979
msgid "Mauritius"
msgstr "毛里求斯"

#: includes/functions.php:978
msgid "Mauritania"
msgstr "毛里塔尼亚"

#: includes/functions.php:977
msgid "Martinique"
msgstr "马提尼克"

#: includes/functions.php:976
msgid "Marshall Islands"
msgstr "马绍尔群岛"

#: includes/functions.php:975
msgid "Malta"
msgstr "马耳他"

#: includes/functions.php:974
msgid "Mali"
msgstr "马里"

#: includes/functions.php:973
msgid "Maldives"
msgstr "马尔代夫"

#: includes/functions.php:972
msgid "Malaysia"
msgstr "马来西亚"

#: includes/functions.php:971
msgid "Malawi"
msgstr "马拉维"

#: includes/functions.php:970
msgid "Madagascar"
msgstr "马达加斯加"

#: includes/functions.php:968
msgid "Macao"
msgstr "澳门"

#: includes/functions.php:967
msgid "Luxembourg"
msgstr "卢森堡"

#: includes/functions.php:966
msgid "Lithuania"
msgstr "立陶宛"

#: includes/functions.php:965
msgid "Liechtenstein"
msgstr "列支敦士登"

#: includes/functions.php:964
msgid "Libya"
msgstr "利比亚"

#: includes/functions.php:963
msgid "Liberia"
msgstr "利比里亚"

#: includes/functions.php:962
msgid "Lesotho"
msgstr "莱索托"

#: includes/functions.php:961
msgid "Lebanon"
msgstr "黎巴嫩"

#: includes/functions.php:960
msgid "Latvia"
msgstr "拉脱维亚"

#: includes/functions.php:959
msgid "Lao People's Democratic Republic"
msgstr "老挝人民民主共和国"

#: includes/functions.php:958
msgid "Kyrgyzstan"
msgstr "吉尔吉斯斯坦"

#: includes/functions.php:957
msgid "Kuwait"
msgstr "科威特"

#: includes/functions.php:955
msgid "Korea (Republic of)"
msgstr "韩国"

#: includes/functions.php:954
msgid "Korea (Democratic People's Republic of)"
msgstr "朝鲜"

#: includes/functions.php:953
msgid "Kiribati"
msgstr "基里巴斯"

#: includes/functions.php:952
msgid "Kenya"
msgstr "肯尼亚"

#: includes/functions.php:951
msgid "Kazakhstan"
msgstr "哈萨克斯坦"

#: includes/functions.php:950
msgid "Jordan"
msgstr "约旦"

#: includes/functions.php:949
msgid "Jersey"
msgstr "新泽西"

#: includes/functions.php:948
msgid "Japan"
msgstr "日本"

#: includes/functions.php:947
msgid "Jamaica"
msgstr "牙买加"

#: includes/functions.php:946
msgid "Italy"
msgstr "意大利"

#: includes/functions.php:945
msgid "Israel"
msgstr "以色列"

#: includes/functions.php:944
msgid "Isle of Man"
msgstr "马恩岛"

#: includes/functions.php:943
msgid "Ireland (Republic of)"
msgstr "爱尔兰(共和国)"

#: includes/functions.php:942
msgid "Iraq"
msgstr "伊拉克"

#: includes/functions.php:941
msgid "Iran (Islamic Republic of)"
msgstr "伊朗（伊斯兰共和国）"

#: includes/functions.php:940
msgid "Indonesia"
msgstr "印尼"

#: includes/functions.php:939
msgid "India"
msgstr "印度"

#: includes/functions.php:938
msgid "Iceland"
msgstr "冰岛"

#: includes/functions.php:937
msgid "Hungary"
msgstr "匈牙利"

#: includes/functions.php:936
msgid "Hong Kong"
msgstr "香港证券交易所"

#: includes/functions.php:935
msgid "Honduras"
msgstr "洪都拉斯"

#: includes/functions.php:934
msgid "Heard Island and McDonald Islands"
msgstr "赫德岛和麦克唐纳群岛"

#: includes/functions.php:933
msgid "Haiti"
msgstr "海地"

#: includes/functions.php:932
msgid "Guyana"
msgstr "圭亚那"

#: includes/functions.php:931
msgid "Guinea-Bissau"
msgstr "几内亚比绍"

#: includes/functions.php:930
msgid "Guinea"
msgstr "几内亚"

#: includes/functions.php:929
msgid "Guernsey"
msgstr "格恩西"

#: includes/functions.php:928
msgid "Guatemala"
msgstr "危地马拉"

#: includes/functions.php:927
msgid "Guam"
msgstr "关岛"

#: includes/functions.php:926
msgid "Guadeloupe"
msgstr "瓜德罗普岛"

#: includes/functions.php:925
msgid "Grenada"
msgstr "格林纳达"

#: includes/functions.php:924
msgid "Greenland"
msgstr "格陵兰"

#: includes/functions.php:923
msgid "Greece"
msgstr "希腊"

#: includes/functions.php:922
msgid "Gibraltar"
msgstr "直布罗陀"

#: includes/functions.php:921
msgid "Ghana"
msgstr "加纳"

#: includes/functions.php:920
msgid "Germany"
msgstr "德国"

#: includes/functions.php:919
msgctxt "Country"
msgid "Georgia"
msgstr "格鲁吉亚"

#: includes/functions.php:918
msgid "Gambia"
msgstr "冈比亚 (西非的独立国)"

#: includes/functions.php:917
msgid "Gabon"
msgstr "加蓬"

#: includes/functions.php:916
msgid "French Southern Territories"
msgstr "法国南部领土"

#: includes/functions.php:915
msgid "French Polynesia"
msgstr "法属波利尼西亚"

#: includes/functions.php:914
msgid "French Guiana"
msgstr "法属圭亚那"

#: includes/functions.php:913
msgid "France"
msgstr "法国"

#: includes/functions.php:912
msgid "Finland"
msgstr "芬兰"

#: includes/functions.php:911
msgid "Fiji"
msgstr "斐济"

#: includes/functions.php:910
msgid "Faroe Islands"
msgstr "法罗群岛"

#: includes/functions.php:909
msgid "Falkland Islands (Malvinas)"
msgstr "福克兰群岛"

#: includes/functions.php:908
msgid "Ethiopia"
msgstr "埃塞俄比亚"

#: includes/functions.php:907
msgid "Estonia"
msgstr "爱沙尼亚"

#: includes/functions.php:906
msgid "Eritrea"
msgstr "厄立特里亚"

#: includes/functions.php:905
msgid "Equatorial Guinea"
msgstr "赤道几内亚"

#: includes/functions.php:904
msgid "El Salvador"
msgstr "萨尔瓦多"

#: includes/functions.php:903
msgid "Egypt"
msgstr "埃及"

#: includes/functions.php:902
msgid "Ecuador"
msgstr "厄瓜多尔"

#: includes/functions.php:901
msgid "Dominican Republic"
msgstr "多明尼加共和国"

#: includes/functions.php:900
msgid "Dominica"
msgstr "多米尼加"

#: includes/functions.php:899
msgid "Djibouti"
msgstr "吉布提"

#: includes/functions.php:898
msgid "Denmark"
msgstr "丹麦"

#: includes/functions.php:897
msgid "Czech Republic"
msgstr "捷克共和国"

#: includes/functions.php:896
msgid "Cyprus"
msgstr "塞浦路斯"

#: includes/functions.php:895
msgid "Curaçao"
msgstr "库拉索"

#: includes/functions.php:894
msgid "Cuba"
msgstr "古巴"

#: includes/functions.php:893
msgid "Croatia"
msgstr "克罗地亚"

#: includes/functions.php:892
msgid "Côte d'Ivoire"
msgstr "科特迪瓦"

#: includes/functions.php:891
msgid "Costa Rica"
msgstr "哥斯达黎加"

#: includes/functions.php:890
msgid "Cook Islands"
msgstr "库克群岛"

#: includes/functions.php:889
msgid "Congo (Democratic Republic of the)"
msgstr "刚果(民主共和国)"

#: includes/functions.php:888
msgid "Congo"
msgstr "刚果"

#: includes/functions.php:887
msgid "Comoros"
msgstr "科摩罗"

#: includes/functions.php:886
msgid "Colombia"
msgstr "哥伦比亚"

#: includes/functions.php:885
msgid "Cocos (Keeling) Islands"
msgstr "科科斯（基林）群岛"

#: includes/functions.php:884
msgid "Christmas Island"
msgstr "圣诞岛"

#: includes/functions.php:883
msgid "China"
msgstr "中国"

#: includes/functions.php:882
msgid "Chile"
msgstr "智利"

#: includes/functions.php:881
msgid "Chad"
msgstr "乍得"

#: includes/functions.php:880
msgid "Central African Republic"
msgstr "中非共和国"

#: includes/functions.php:879
msgid "Cayman Islands"
msgstr "开曼群岛"

#: includes/functions.php:878
msgid "Canada"
msgstr "加拿大"

#: includes/functions.php:877
msgid "Cameroon"
msgstr "喀麦隆"

#: includes/functions.php:876
msgid "Cambodia"
msgstr "柬埔寨"

#: includes/functions.php:875
msgid "Cabo Verde"
msgstr "佛得角"

#: includes/functions.php:874
msgid "Burundi"
msgstr "布隆迪"

#: includes/functions.php:873
msgid "Burkina Faso"
msgstr "布吉纳法索"

#: includes/functions.php:872
msgid "Bulgaria"
msgstr "保加利亚"

#: includes/functions.php:871
msgid "Brunei Darussalam"
msgstr "汶莱"

#: includes/functions.php:870
msgid "British Indian Ocean Territory"
msgstr "英属印度洋领地"

#: includes/functions.php:869
msgid "Brazil"
msgstr "巴西"

#: includes/functions.php:868
msgid "Bouvet Island"
msgstr "布维岛"

#: includes/functions.php:867
msgid "Botswana"
msgstr "博茨瓦纳"

#: includes/functions.php:866
msgid "Bosnia and Herzegovina"
msgstr "波斯尼亚和黑塞哥维那"

#: includes/functions.php:864
msgid "Bolivia (Plurinational State of)"
msgstr "玻利维亚（多民族国）"

#: includes/functions.php:863
msgid "Bhutan"
msgstr "不丹"

#: includes/functions.php:862
msgid "Bermuda"
msgstr "百慕大"

#: includes/functions.php:861
msgid "Benin"
msgstr "贝宁"

#: includes/functions.php:860
msgid "Belize"
msgstr "伯利兹"

#: includes/functions.php:859
msgid "Belgium"
msgstr "比利时"

#: includes/functions.php:858
msgid "Belarus"
msgstr "白俄罗斯"

#: includes/functions.php:857
msgid "Barbados"
msgstr "巴巴多斯"

#: includes/functions.php:856
msgid "Bangladesh"
msgstr "孟加拉国"

#: includes/functions.php:855
msgid "Bahrain"
msgstr "巴林"

#: includes/functions.php:854
msgid "Bahamas"
msgstr "巴哈马"

#: includes/functions.php:853
msgid "Azerbaijan"
msgstr "阿塞拜疆"

#: includes/functions.php:852
msgid "Austria"
msgstr "奥地利"

#: includes/functions.php:851
msgid "Australia"
msgstr "澳洲"

#: includes/functions.php:850
msgid "Aruba"
msgstr "阿鲁巴"

#: includes/functions.php:849
msgid "Armenia"
msgstr "亚美尼亚"

#: includes/functions.php:848
msgid "Argentina"
msgstr "阿根廷"

#: includes/functions.php:847
msgid "Antigua and Barbuda"
msgstr "安提瓜岛和巴布达"

#: includes/functions.php:846
msgid "Antarctica"
msgstr "南极洲"

#: includes/functions.php:845
msgid "Anguilla"
msgstr "安圭拉"

#: includes/functions.php:844
msgid "Angola"
msgstr "安哥拉"

#: includes/functions.php:843
msgid "Andorra"
msgstr "安道尔"

#: includes/functions.php:842
msgid "American Samoa"
msgstr "美属萨摩亚"

#: includes/functions.php:841
msgid "Algeria"
msgstr "阿尔及利亚"

#: includes/functions.php:840
msgid "Albania"
msgstr "阿尔巴尼亚"

#: includes/functions.php:839
msgid "Åland Islands"
msgstr "奥兰群岛"

#: includes/functions.php:838
msgid "Afghanistan"
msgstr "阿富汗"

#: includes/functions.php:822
msgid "Wyoming"
msgstr "怀俄明州"

#: includes/functions.php:821
msgid "Wisconsin"
msgstr "威斯康辛州"

#: includes/functions.php:820
msgid "West Virginia"
msgstr "西维吉尼亚州"

#: includes/functions.php:819
msgid "Washington"
msgstr "华盛顿州"

#: includes/functions.php:818
msgid "Virginia"
msgstr "弗吉尼亚"

#: includes/functions.php:817
msgid "Vermont"
msgstr "佛蒙特州"

#: includes/functions.php:816
msgid "Utah"
msgstr "犹他州"

#: includes/functions.php:815
msgid "Texas"
msgstr "德克萨斯州"

#: includes/functions.php:814
msgid "Tennessee"
msgstr "田纳西州"

#: includes/functions.php:813
msgid "South Dakota"
msgstr "南达科他州"

#: includes/functions.php:812
msgid "South Carolina"
msgstr "南卡罗来纳州"

#: includes/functions.php:811
msgid "Rhode Island"
msgstr "罗德岛州"

#: includes/functions.php:810
msgid "Pennsylvania"
msgstr "宾夕法尼亚州"

#: includes/functions.php:809
msgid "Oregon"
msgstr "俄勒冈州"

#: includes/functions.php:808
msgid "Oklahoma"
msgstr "俄克拉荷马州"

#: includes/functions.php:807
msgid "Ohio"
msgstr "俄亥俄州"

#: includes/functions.php:806
msgid "North Dakota"
msgstr "北达科他州"

#: includes/functions.php:805
msgid "North Carolina"
msgstr "北卡罗莱纳州"

#: includes/functions.php:804
msgid "New York"
msgstr "纽约"

#: includes/functions.php:803
msgid "New Mexico"
msgstr "新墨西哥"

#: includes/functions.php:802
msgid "New Jersey"
msgstr "新泽西"

#: includes/functions.php:801
msgid "New Hampshire"
msgstr "新汉普郡"

#: includes/functions.php:800
msgid "Nevada"
msgstr "内布拉斯加州"

#: includes/functions.php:799
msgid "Nebraska"
msgstr "内布拉斯加州"

#: includes/functions.php:798
msgid "Montana"
msgstr "蒙塔纳州"

#: includes/functions.php:797
msgid "Missouri"
msgstr "密苏里"

#: includes/functions.php:796
msgid "Mississippi"
msgstr "密西西比州"

#: includes/functions.php:795
msgid "Minnesota"
msgstr "明尼苏达州"

#: includes/functions.php:794
msgid "Michigan"
msgstr "密歇根州"

#: includes/functions.php:793
msgid "Massachusetts"
msgstr "马萨诸塞州"

#: includes/functions.php:792
msgid "Maryland"
msgstr "马里兰"

#: includes/functions.php:791
msgid "Maine"
msgstr "曼恩河"

#: includes/functions.php:790
msgid "Louisiana"
msgstr "美国路易斯安那州"

#: includes/functions.php:789
msgid "Kentucky"
msgstr "肯塔基州"

#: includes/functions.php:788
msgid "Kansas"
msgstr "堪萨斯州"

#: includes/functions.php:787
msgid "Iowa"
msgstr "爱荷华州"

#: includes/functions.php:786
msgid "Indiana"
msgstr "印第安纳州"

#: includes/functions.php:785
msgid "Illinois"
msgstr "伊利诺斯州"

#: includes/functions.php:784
msgid "Idaho"
msgstr "爱达荷"

#: includes/functions.php:783
msgid "Hawaii"
msgstr "夏威夷州"

#: includes/functions.php:782
msgctxt "US State"
msgid "Georgia"
msgstr "格鲁吉亚"

#: includes/functions.php:781
msgid "Florida"
msgstr "佛罗里达"

#: includes/functions.php:780
msgid "District of Columbia"
msgstr "哥伦比亚特区"

#: includes/functions.php:779
msgid "Delaware"
msgstr "特拉华州"

#: includes/functions.php:778
msgid "Connecticut"
msgstr "康乃狄克州"

#: includes/functions.php:777
msgid "Colorado"
msgstr "科罗拉多州"

#: includes/functions.php:776
msgid "California"
msgstr "加利福尼亚"

#: includes/functions.php:775
msgid "Arkansas"
msgstr "阿肯色州"

#: includes/functions.php:774
msgid "Arizona"
msgstr "亚利桑那州"

#: includes/functions.php:773
msgid "Alaska"
msgstr "阿拉斯加州"

#: includes/functions.php:772
msgid "Alabama"
msgstr "亚拉巴马州"

#: includes/admin/builder/class-builder.php:552
msgid "Other"
msgstr "其他"

#: includes/admin/class-about.php:82
msgid "Customer Support"
msgstr "客户支持"

#: includes/templates/class-blank.php:22
msgid "The blank form allows you to create any type of form using our drag & drop builder."
msgstr "空白表单允许您使用我们的拖拽是构建器创建任何类型的表单。"

#: includes/admin/builder/class-builder.php:600
#: includes/templates/class-blank.php:18
msgid "Blank Form"
msgstr "空白表单"

#: includes/class-form.php:359 includes/templates/class-blank.php:34
msgid "Sending..."
msgstr "发送中…"

#: src/SmartTags/SmartTags.php:133
msgid "Lost Password URL"
msgstr "忘记密码URL"

#: src/SmartTags/SmartTags.php:132
msgid "Register URL"
msgstr "注册网址"

#: src/SmartTags/SmartTags.php:131
msgid "Logout URL"
msgstr "注销网址"

#: src/SmartTags/SmartTags.php:130
msgid "Login URL"
msgstr "登录网址"

#: src/SmartTags/SmartTags.php:129
msgid "Referrer URL"
msgstr "推荐人网址"

#: src/SmartTags/SmartTags.php:128
msgid "Author Email"
msgstr "作者电子邮件"

#: src/SmartTags/SmartTags.php:127
msgid "Author Name"
msgstr "作者姓名"

#: src/SmartTags/SmartTags.php:126
msgid "Author ID"
msgstr "作者ID"

#: src/SmartTags/SmartTags.php:125
msgid "User Meta"
msgstr "用户元数据"

#: src/SmartTags/SmartTags.php:124
msgid "User Email"
msgstr "用户邮箱"

#: src/SmartTags/SmartTags.php:123
msgid "User Last Name"
msgstr "用户名字"

#: src/SmartTags/SmartTags.php:122
msgid "User First Name"
msgstr "用户姓氏"

#: src/SmartTags/SmartTags.php:121
msgid "User Full Name"
msgstr "用户全名"

#: src/SmartTags/SmartTags.php:120
msgid "User Display Name"
msgstr "显示用户姓名"

#: src/Logger/ListTable.php:483 src/SmartTags/SmartTags.php:119
msgid "User ID"
msgstr "用户 ID"

#: src/SmartTags/SmartTags.php:118
msgid "User IP Address"
msgstr "用户 IP 地址"

#: src/SmartTags/SmartTags.php:117
msgid "Query String Variable"
msgstr "查询字符串变量"

#: src/Logger/ListTable.php:260 src/Logger/ListTable.php:445
#: src/SmartTags/SmartTags.php:116
msgid "Date"
msgstr "日期"

#: src/SmartTags/SmartTags.php:115
msgid "Embedded Post/Page ID"
msgstr "嵌入的文章/页面ID"

#: src/SmartTags/SmartTags.php:114
msgid "Embedded Post/Page URL"
msgstr "嵌入的文章/页面的URL"

#: src/SmartTags/SmartTags.php:113
msgid "Embedded Post/Page Title"
msgstr "嵌入的文章/页面标题"

#: includes/admin/builder/panels/class-settings.php:87
#: src/Integrations/Elementor/Widget.php:222 src/SmartTags/SmartTags.php:112
msgid "Form Name"
msgstr "表单名称"

#: src/Logger/ListTable.php:258 src/Logger/ListTable.php:459
#: src/SmartTags/SmartTags.php:111
msgid "Form ID"
msgstr "表单 ID"

#: src/Logger/ListTable.php:471
msgid "Entry ID"
msgstr "条目 ID"

#: src/SmartTags/SmartTags.php:107
msgid "Site Administrator Email"
msgstr "网站管理员电子邮件"

#: includes/fields/class-name.php:322
msgid "Last name field advanced options."
msgstr "姓氏字段高级选项。"

#: includes/fields/class-name.php:322
msgid "Last Name"
msgstr "姓"

#: includes/fields/class-name.php:305
msgid "Middle name field advanced options."
msgstr "中间名字段高级选项。"

#: includes/fields/class-name.php:305
msgid "Middle Name"
msgstr "名"

#: includes/fields/class-name.php:288
msgid "First name field advanced options."
msgstr "名字字段高级选项。"

#: includes/fields/class-name.php:288
msgid "First Name"
msgstr "名"

#: includes/fields/class-name.php:275 includes/fields/class-name.php:292
#: includes/fields/class-name.php:309 includes/fields/class-name.php:326
msgid "Placeholder"
msgstr "占位符"

#: includes/fields/class-name.php:271
msgid "Name field advanced options."
msgstr "名称字段高级选项。"

#: includes/fields/class-name.php:229
msgid "First Middle Last"
msgstr "前中后"

#: includes/fields/class-name.php:228
msgid "First Last"
msgstr "最后一个"

#: includes/fields/class-name.php:227
msgid "Simple"
msgstr "简单"

#: includes/fields/class-name.php:216
msgid "Select format to use for the name form field"
msgstr "选择用于名称表单字段的格式"

#: includes/fields/class-name.php:215
msgid "Format"
msgstr "格式"

#: includes/fields/class-name.php:122 includes/fields/class-name.php:392
msgid "Last"
msgstr "后一页"

#: includes/fields/class-name.php:101 includes/fields/class-name.php:387
msgid "Middle"
msgstr "居中"

#: includes/fields/class-name.php:80 includes/fields/class-name.php:382
msgid "First"
msgstr "前一页"

#: includes/admin/overview/class-overview-table.php:61
#: includes/fields/class-name.php:18 includes/fields/class-name.php:271
msgid "Name"
msgstr "名称"

#: includes/fields/class-text.php:301
msgid "See Examples & Docs"
msgstr "参见示例和文档"

#: includes/fields/class-text.php:300
msgid "Enter your custom input mask."
msgstr "输入自定义输入掩码。"

#: includes/fields/class-text.php:299
msgid "Input Mask"
msgstr "输入掩码"

#: includes/fields/class-text.php:18
msgid "Single Line Text"
msgstr "单行文本"

#: includes/admin/class-settings.php:421 includes/class-frontend.php:1640
#: includes/fields/class-number.php:161
msgid "Please enter a valid number."
msgstr "请输入有效的号码。"

#: includes/fields/class-number.php:18
msgid "Numbers"
msgstr "数字"

#: includes/fields/class-textarea.php:18
msgid "Paragraph Text"
msgstr "多段文本"

#: includes/fields/class-email.php:296
msgid "Enter text for the confirmation field placeholder."
msgstr "输入确认字段占位符的文本。"

#: includes/fields/class-email.php:295
msgid "Confirmation Placeholder Text"
msgstr "确认占位符文本"

#: includes/fields/class-email.php:257
msgid "Check this option to ask users to provide an email address twice."
msgstr "选中此选项可要求用户提供两次电子邮件地址。"

#: includes/fields/class-email.php:256
msgid "Enable Email Confirmation"
msgstr "启用电子邮件确认"

#: includes/fields/class-email.php:122 includes/fields/class-email.php:430
msgid "Confirm Email"
msgstr "确认邮件地址"

#: includes/admin/class-settings.php:184 includes/admin/class-settings.php:327
#: includes/admin/class-settings.php:398 includes/fields/class-email.php:25
#: includes/fields/class-email.php:99 includes/fields/class-email.php:425
msgid "Email"
msgstr "电邮"

#: includes/fields/class-checkbox.php:388
msgid "Check this option to adjust the field styling to support Disclaimers and Terms of Service type agreements."
msgstr "选中此选项可调整字段样式以支持免责声明和服务条款类型协议。"

#: includes/fields/class-checkbox.php:387
msgid "Enable Disclaimer / Terms of Service Display"
msgstr "启用免责声明/服务条款显示"

#. translators: %s - choice number.
#: includes/admin/builder/class-builder.php:589
#: includes/fields/class-checkbox.php:151
#: includes/fields/class-checkbox.php:709 includes/fields/class-radio.php:130
#: includes/fields/class-radio.php:539
msgid "Choice %s"
msgstr "选择%s"

#: includes/fields/class-checkbox.php:148
msgid "Checked"
msgstr "复选"

#: includes/fields/class-checkbox.php:18
msgid "Checkboxes"
msgstr "多选按钮"

#: includes/admin/builder/panels/class-fields.php:330
#: includes/fields/class-base.php:1986
msgid "Delete Field"
msgstr "删除字段"

#: includes/admin/builder/panels/class-fields.php:327
#: includes/fields/class-base.php:1983
msgid "Duplicate Field"
msgstr "复制字段"

#: includes/fields/class-base.php:1944
msgid "No field type found"
msgstr "没有找到字段类型"

#: includes/fields/class-base.php:1939
msgid "No form ID found"
msgstr "找不到表单ID"

#: includes/fields/class-base.php:1934
#: src/Admin/Education/Builder/Captcha.php:61 src/Admin/Pages/Analytics.php:523
#: src/Admin/Pages/SMTP.php:442 src/Logger/Log.php:174
#: src/Providers/Provider/Settings/PageIntegrations.php:204
msgid "You do not have permission."
msgstr "您没有权限。"

#: includes/admin/ajax-actions.php:368 includes/emails/class-emails.php:571
#: includes/emails/class-emails.php:616 includes/fields/class-base.php:1736
#: src/SmartTags/SmartTag/FieldHtmlId.php:38
msgid "(empty)"
msgstr "（空）"

#. translators: %s - dynamic source type name.
#: includes/fields/class-base.php:1543
msgid "Dynamic %s Source"
msgstr "动态%s源"

#. translators: %s - dynamic source type name.
#: includes/fields/class-base.php:1540
msgid "Select %s to use for auto-populating field choices."
msgstr "选择%s以用于自动填充字段选择。"

#: includes/fields/class-base.php:1476
msgid "Dynamic Choices"
msgstr "动态选择"

#: includes/fields/class-base.php:1469 includes/fields/class-base.php:1529
msgid "Taxonomy"
msgstr "类别"

#: includes/fields/class-base.php:1468 includes/fields/class-base.php:1518
msgid "Post Type"
msgstr "文章"

#: includes/fields/class-base.php:1465
msgid "Select auto-populate method to use."
msgstr "选择要使用的自动填充方法。"

#: includes/fields/class-base.php:1455
msgid "Choice Layout"
msgstr "选择布局"

#: includes/fields/class-base.php:1453
msgid "Inline"
msgstr "内联"

#: includes/fields/class-base.php:1452
msgid "Three Columns"
msgstr "三栏"

#: includes/fields/class-base.php:1451
msgid "Two Columns"
msgstr "两栏"

#: includes/fields/class-base.php:1450
msgid "One Column"
msgstr "一栏"

#: includes/fields/class-base.php:1448
msgid "Select the layout for displaying field choices."
msgstr "选择用于显示字段选项的布局。"

#: includes/fields/class-base.php:1395
msgid "Hide Label"
msgstr "隐藏标签"

#: includes/fields/class-base.php:1386
msgid "Check this option to hide the form field label."
msgstr "选中此选项可隐藏表单字段标题。"

#: includes/fields/class-base.php:1376
msgid "CSS Classes"
msgstr "CSS类"

#: includes/admin/builder/class-builder.php:527
#: includes/fields/class-base.php:1373
msgid "Show Layouts"
msgstr "显示布局"

#: includes/fields/class-base.php:1371
msgid "Enter CSS class names for the form field container. Class names should be separated with spaces."
msgstr "为表单域字段输入CSS类名。类名应该用空格分隔。"

#: includes/fields/class-base.php:1360
msgid "Placeholder Text"
msgstr "点位符文本"

#: includes/fields/class-base.php:1359
msgid "Enter text for the form field placeholder."
msgstr "输入表单字段占位符文本。"

#: includes/fields/class-base.php:1331
msgid "Field Size"
msgstr "字段大小"

#: includes/fields/class-base.php:1329
msgid "Large"
msgstr "大型"

#: includes/fields/class-base.php:1328
msgid "Medium"
msgstr "中型"

#: includes/fields/class-base.php:1327
msgid "Small"
msgstr "小型"

#: includes/fields/class-base.php:1325
msgid "Select the default form field size."
msgstr "选择默认表单字段大小。"

#: includes/fields/class-base.php:1314 includes/fields/class-name.php:279
#: includes/fields/class-name.php:296 includes/fields/class-name.php:313
#: includes/fields/class-name.php:330
#: includes/fields/class-number-slider.php:216
msgid "Default Value"
msgstr "默认值"

#: includes/admin/builder/class-builder.php:575
#: includes/admin/builder/functions.php:55 includes/fields/class-base.php:1313
msgid "Show Smart Tags"
msgstr "显示智能标签"

#: includes/fields/class-base.php:1312
msgid "Enter text for the default form field value."
msgstr "输入默认表单字段值的文本。"

#: includes/fields/class-base.php:1284 includes/fields/class-email.php:336
#: src/Admin/Settings/Captcha.php:138
msgid "None"
msgstr "暂无"

#: includes/fields/class-base.php:1283 includes/fields/class-select.php:301
msgid "Classic"
msgstr "经典"

#: includes/fields/class-base.php:1282 includes/fields/class-select.php:302
msgid "Modern"
msgstr "现代"

#: includes/fields/class-base.php:1269
msgid "Select the style for the image choices."
msgstr "为图像选项选择样式。"

#: includes/fields/class-base.php:1268
msgid "Image Choice Style"
msgstr "图像选项样式"

#: includes/fields/class-base.php:1240
msgid "Check this option to enable using images with the choices."
msgstr "选中此选项以启用带有选项的图像使用."

#: includes/fields/class-base.php:1239
msgid "Use image choices"
msgstr "使用图像选项"

#: includes/fields/class-base.php:1134
msgid "Items"
msgstr "个项目"

#: includes/admin/ajax-actions.php:340 includes/fields/class-base.php:1081
msgid "taxonomy"
msgstr "分类"

#: includes/admin/ajax-actions.php:308 includes/fields/class-base.php:1074
msgid "post type"
msgstr "文章类型"

#: includes/admin/settings-api.php:386 includes/fields/class-base.php:1056
#: includes/fields/class-base.php:1190
msgid "Upload Image"
msgstr "上传图像"

#: includes/admin/builder/class-builder.php:581
#: includes/fields/class-base.php:1047 includes/fields/class-base.php:1181
msgid "Remove Image"
msgstr "移除图片"

#: includes/admin/builder/class-builder.php:477
#: includes/fields/class-base.php:1002
msgid "Bulk Add"
msgstr "批量添加"

#: includes/fields/class-base.php:1001 includes/fields/class-base.php:1135
msgid "Add choices for the form field."
msgstr "为表单字段添加选项。"

#: includes/fields/class-base.php:976
msgid "Choices"
msgstr "选择"

#: includes/fields/class-base.php:966
msgid "Code"
msgstr "代码"

#: includes/fields/class-base.php:965
msgid "Enter code for the form field."
msgstr "输入表单字段的代码。"

#: includes/admin/class-settings.php:386 includes/fields/class-base.php:917
msgid "Required"
msgstr "必填"

#: includes/fields/class-base.php:910
msgid "Check this option to mark the field required. A form will not submit unless all required fields are provided."
msgstr "选中此选项可标记必填字段。除非提供了所有必填字段，否则表单将不会提交。"

#: includes/fields/class-base.php:899
msgid "Description"
msgstr "描述"

#: includes/fields/class-base.php:898
msgid "Enter text for the form field description."
msgstr "输入表单字段描述文本。"

#: includes/fields/class-base.php:888
msgid "Label"
msgstr "标签"

#: includes/fields/class-base.php:887
msgid "Enter text for the form field label. Field labels are recommended and can be hidden in the Advanced Settings."
msgstr "输入表单字段标题文本。建议使用字段标题，可以在高级设置中隐藏。"

#: includes/fields/class-checkbox.php:297 includes/fields/class-radio.php:262
msgid "Check this option to randomize the order of the choices."
msgstr "选中此选项可随机化选项的顺序。"

#: includes/fields/class-checkbox.php:296 includes/fields/class-radio.php:261
msgid "Randomize Choices"
msgstr "随机选择"

#: includes/fields/class-radio.php:18
msgid "Multiple Choice"
msgstr "多项选择"

#: includes/fields/class-gdpr-checkbox.php:175
msgid "Agreement"
msgstr "协议"

#: includes/fields/class-gdpr-checkbox.php:24
msgid "I consent to having this website store my submitted information so they can respond to my inquiry."
msgstr "我同意让这个网站存储我提交的信息，以便他们可以回应我的查询。"

#: includes/fields/class-gdpr-checkbox.php:18
msgid "GDPR Agreement"
msgstr "GDPR协议"

#: includes/fields/class-checkbox.php:319 includes/fields/class-radio.php:284
#: includes/fields/class-select.php:233
msgid "Check this option to manually set form field values."
msgstr "勾选此选项可手动设置表单字段值。"

#: includes/fields/class-checkbox.php:318 includes/fields/class-radio.php:283
#: includes/fields/class-select.php:232
msgid "Show Values"
msgstr "显示值"

#: includes/fields/class-checkbox.php:36 includes/fields/class-radio.php:36
#: includes/fields/class-select.php:59
msgid "Third Choice"
msgstr "第三选择"

#: includes/fields/class-checkbox.php:30 includes/fields/class-radio.php:30
#: includes/fields/class-select.php:54
msgid "Second Choice"
msgstr "第二选择"

#: includes/fields/class-checkbox.php:24 includes/fields/class-radio.php:24
#: includes/fields/class-select.php:49
msgid "First Choice"
msgstr "第一选择"

#: includes/fields/class-select.php:43
msgid "Dropdown"
msgstr "下拉"

#: src/Forms/Honeypot.php:79
msgid "WPForms honeypot field triggered."
msgstr "WPForms蜜罐字段已触发。"

#: includes/class-process.php:324 includes/class-process.php:421
#: includes/class-process.php:722
msgid "Form has not been submitted, please see the errors below."
msgstr "表单尚未提交，请参阅以下错误。"

#: includes/class-process.php:482
msgid "Uploaded files combined size exceeds allowed maximum."
msgstr "上载的文件总计大小超过了允许的最大值。"

#: includes/class-process.php:159
msgid "Invalid form."
msgstr "无效的表单。"

#. translators: %s - WPForms.com URL for documentation with more details.
#: wpforms.php:198
msgid "<strong>Note:</strong> WPForms plugin is disabled on your site until you fix the issue. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Read more for additional information.</a>"
msgstr "<strong> 注: </strong>在解决此问题之前, 您的网站上禁用了WPForms插件。<a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">阅读更多信息 </a>"

#. translators: %s - WPBeginner URL for recommended WordPress hosting.
#: wpforms.php:181
msgid "Your site is running an <strong>insecure version</strong> of PHP that is no longer supported. Please contact your web hosting provider to update your PHP version or switch to a <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">recommended WordPress hosting company</a>."
msgstr "您的网站正在运行一个 <strong> 不安全的 php 版本 </strong> 不再受支持。请联系您的虚拟主机提供商更新您的 php 版本或切换到一个 <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">推荐 wordpress 主机公司 </a>。"

#: includes/admin/class-welcome.php:286 lite/templates/admin/addons.php:55
#: src/Admin/Tools/Views/Importer.php:363 src/Lite/Admin/Pages/Addons.php:101
msgid "Upgrade Now"
msgstr "现在升级"

#: lite/templates/admin/addons.php:19
msgid "Search Addons"
msgstr "搜索扩展"

#: includes/admin/class-about.php:81 includes/admin/class-menu.php:121
#: lite/templates/admin/addons.php:18
msgid "WPForms Addons"
msgstr "WPForms扩展"

#: lite/wpforms-lite.php:739
msgid "Upgrade to WPForms Pro Now"
msgstr "立即升级到WPForms专业版"

#: lite/wpforms-lite.php:733
msgid "See Geolocation Data"
msgstr "请参阅地理位置数据"

#: lite/wpforms-lite.php:732
msgid "Resend Notifications"
msgstr "重新发送通知"

#: lite/wpforms-lite.php:731
msgid "Print Entries"
msgstr "打印条目"

#: lite/wpforms-lite.php:730
msgid "Mark Read / Unread"
msgstr "标记已读/未读"

#: lite/wpforms-lite.php:727
msgid "Save Favorite Entries"
msgstr "保存收藏的条目"

#: lite/wpforms-lite.php:726
msgid "Add Notes / Comments"
msgstr "添加备注/评论"

#: lite/wpforms-lite.php:725
msgid "Export Entries in a CSV File"
msgstr "导出CSV文件中的条目"

#: lite/wpforms-lite.php:724
msgid "View Entries in Dashboard"
msgstr "在仪表板中查看条目"

#: lite/wpforms-lite.php:720
msgid "Once you upgrade to WPForms Pro, all future form entries will be stored in your WordPress database and displayed on this Entries screen."
msgstr "一旦您升级到WPForms Pro，所有未来的表单条目将存储在您的WordPress数据库中，并显示在这个条目屏幕上。"

#: lite/wpforms-lite.php:719
msgid "Form entries are not stored in WPForms Lite."
msgstr "表单条目不存储在WPForms Lite中。"

#: lite/wpforms-lite.php:717
msgid "View and Manage All Your Form Entries inside WordPress"
msgstr "查看和管理WordPress中的所有表单条目"

#: lite/wpforms-lite.php:547
msgid "<strong>Bonus:</strong> WPForms Lite users get <span class=\"green\">50% off regular price</span>, automatically applied at checkout."
msgstr "<strong> 优惠: </strong>wpforms 精简版用户获得 <span class=\"green\">50%优惠</span>, 在结账时自动应用。"

#: lite/wpforms-lite.php:541
msgid "Get WPForms Pro Today and Unlock all the Powerful Features »"
msgstr "立即获取WPForms Pro并解锁所有强大的功能»"

#: lite/wpforms-lite.php:522
msgid "Pro Features:"
msgstr "专业版特色："

#: lite/wpforms-lite.php:505
msgid "Thanks for being a loyal WPForms Lite user. Upgrade to WPForms Pro to unlock all the awesome features and experience why WPForms is consistently rated the best WordPress form builder."
msgstr "感谢您成为忠实的WPF​​orms Lite用户。升级到WPForms Pro以解锁所有强大的功能，并体验WPForms一直被评为最佳WordPress表单构建器的原因。"

#: includes/admin/class-about.php:512 lite/wpforms-lite.php:504
msgid "Get WPForms Pro and Unlock all the Powerful Features"
msgstr "获取WPForms Pro并解锁所有强大的功能"

#: lite/wpforms-lite.php:503 src/Admin/Notifications.php:431
msgid "Dismiss this message"
msgstr "关闭此消息"

#. translators: %s - WPForms.com docs page URL.
#: lite/wpforms-lite.php:467
msgid "You've just turned off notification emails for this form. Since entries are not stored in WPForms Lite, notification emails are recommended for collecting entry details. For setup steps, <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">please see our notification tutorial</a>."
msgstr "您刚刚关闭了此表单的通知电子邮件。由于条目不存储在 wpforms lite 中, 因此建议发送通知电子邮件来收集条目详细信息。有关设置步骤, <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\"> 请参阅我们的通知教程 </a>。"

#: lite/wpforms-lite.php:422
msgid "Confirmation Redirect URL"
msgstr "确认重定向网址"

#: lite/wpforms-lite.php:409
msgid "Confirmation Page"
msgstr "确认页"

#: lite/wpforms-lite.php:396
msgid "Automatically scroll to the confirmation message"
msgstr "自动滚动到确认消息"

#: lite/wpforms-lite.php:375
msgid "Confirmation Message"
msgstr "确认消息"

#: lite/wpforms-lite.php:362
msgid "Go to URL (Redirect)"
msgstr "转到网址（重定向）"

#: lite/wpforms-lite.php:361
msgid "Show Page"
msgstr "显示页面"

#: lite/wpforms-lite.php:356
msgid "Confirmation Type"
msgstr "确认类型"

#: includes/admin/builder/class-builder.php:520 lite/wpforms-lite.php:335
msgid "Default Confirmation"
msgstr "默认确认"

#: lite/wpforms-lite.php:327
msgid "Add New Confirmation"
msgstr "添加新确认"

#: lite/wpforms-lite.php:326
msgid "Multiple confirmations"
msgstr "多次确认"

#: includes/admin/builder/panels/class-settings.php:40
#: lite/wpforms-lite.php:323
msgid "Confirmations"
msgstr "确认"

#: includes/class-form.php:374 includes/class-process.php:583
#: lite/wpforms-lite.php:315 lite/wpforms-lite.php:377
#: src/Admin/Tools/Importers/ContactForm7.php:143
#: src/Admin/Tools/Importers/NinjaForms.php:148
#: src/Admin/Tools/Importers/PirateForms.php:471
msgid "Thanks for contacting us! We will be in touch with you shortly."
msgstr "谢谢您联络我们！我们会尽快与您联系。"

#. translators: %s - {all_fields} Smart Tag.
#: lite/wpforms-lite.php:265
msgid "To display all form fields, use the %s Smart Tag."
msgstr "要显示所有表单域，请使用%s智能标记。"

#: lite/wpforms-lite.php:360 src/Logger/ListTable.php:257
#: src/Logger/ListTable.php:440
msgid "Message"
msgstr "邮件"

#: lite/wpforms-lite.php:219
msgid "From Email"
msgstr "发件人邮箱"

#: lite/wpforms-lite.php:201
msgid "From Name"
msgstr "从名字"

#. translators: %s - form name.
#: includes/class-form.php:364 lite/wpforms-lite.php:188
#: src/Admin/Tools/Importers/ContactForm7.php:133
#: src/Admin/Tools/Importers/ContactForm7.php:448
#: src/Admin/Tools/Importers/NinjaForms.php:138
#: src/Admin/Tools/Importers/NinjaForms.php:432
#: src/Admin/Tools/Importers/PirateForms.php:460
msgid "New Entry: %s"
msgstr "新条目：%s"

#: lite/wpforms-lite.php:169
msgid "CC"
msgstr "抄送"

#: lite/wpforms-lite.php:153
msgid "Enter the email address to receive form entry notifications. For multiple notifications, separate email addresses with a comma."
msgstr "输入接收表单输入通知的电子邮件地址。对于多个通知，使用逗号分隔电子邮件地址。"

#: lite/wpforms-lite.php:150
msgid "Send To Email Address"
msgstr "发送到电子邮件地址"

#: includes/admin/builder/class-builder.php:515 lite/wpforms-lite.php:139
#: src/Admin/Tools/Importers/PirateForms.php:457
msgid "Default Notification"
msgstr "默认通知"

#: includes/admin/builder/class-builder.php:549
#: includes/admin/builder/functions.php:361 includes/fields/class-base.php:1467
#: src/Integrations/Divi/WPFormsSelector.php:72
#: src/Integrations/Divi/WPFormsSelector.php:82
msgid "Off"
msgstr "关闭"

#: includes/admin/builder/class-builder.php:550
#: includes/admin/builder/functions.php:360
#: src/Integrations/Divi/WPFormsSelector.php:73
#: src/Integrations/Divi/WPFormsSelector.php:83
msgid "On"
msgstr "开启"

#: lite/wpforms-lite.php:83
msgid "Add New Notification"
msgstr "添加新通知"

#: lite/wpforms-lite.php:82
msgid "Multiple notifications"
msgstr "多个通知"

#: includes/admin/builder/panels/class-settings.php:39 lite/wpforms-lite.php:78
#: src/Admin/AdminBarMenu.php:219 src/Admin/Notifications.php:427
msgid "Notifications"
msgstr "通知"

#. translators: %s - form name.
#: includes/class-process.php:794 lite/wpforms-lite.php:67
msgid "New %s Entry"
msgstr "新%s条目"

#: src/Forms/Preview.php:184
msgid "Close this window"
msgstr "关闭此窗口"

#: src/Forms/Preview.php:189
msgid "This is a preview of your form. This page is not publicly accessible."
msgstr "这是表单的预览。此页面无法公开访问。"

#. translators: %s - form title.
#: src/Forms/Preview.php:122
msgid "%s Preview"
msgstr "%s预览"

#: src/Integrations/Divi/WPFormsSelector.php:77
#: src/Integrations/Gutenberg/FormSelector.php:103
msgid "Show Description"
msgstr "显示说明"

#: src/Integrations/Divi/WPFormsSelector.php:67
#: src/Integrations/Gutenberg/FormSelector.php:102
msgid "Show Title"
msgstr "显示标题"

#: includes/integrations.php:44 src/Forms/Preview.php:123
#: src/Integrations/Divi/WPFormsSelector.php:60
#: src/Integrations/Elementor/Widget.php:115
#: src/Integrations/Elementor/Widget.php:143
#: src/Integrations/Gutenberg/FormSelector.php:101
#: templates/emails/summary-body-plain.php:32
#: templates/emails/summary-body.php:59
msgid "Form"
msgstr "表单"

#: src/Integrations/Gutenberg/FormSelector.php:100
msgid "Form Settings"
msgstr "表单设置"

#: src/Integrations/Gutenberg/FormSelector.php:99
msgid "Select a Form"
msgstr "选择一个表单"

#: src/Integrations/Gutenberg/FormSelector.php:94
msgid "form"
msgstr "表单"

#: src/Integrations/Gutenberg/FormSelector.php:92
msgid "Select and display one of your forms."
msgstr "选择并显示一个表单。"

#: src/Providers/Provider/Settings/FormBuilder.php:478
msgid "Add New Connection"
msgstr "添加新连接"

#: src/Providers/Provider/Settings/FormBuilder.php:305
msgid "Something went wrong while performing an AJAX request."
msgstr "执行AJAX请求时出错了。"

#: src/Admin/Education/Core.php:159
#: src/Providers/Provider/Settings/FormBuilder.php:299
msgid "You do not have permission to perform this action."
msgstr "您无权执行此操作。"

#: includes/providers/class-base.php:968
#: src/Providers/Provider/Settings/FormBuilder.php:107
msgid "Marketing provider connection"
msgstr "营销提供商连接"

#: src/Providers/Provider/Settings/FormBuilder.php:240
msgid "Map custom fields (or properties) to form fields values."
msgstr "映射自定义字段（或属性）以形成字段值。"

#: includes/admin/overview/class-overview-table.php:339
#: includes/admin/overview/class-overview-table.php:359
#: src/Providers/Provider/Settings/FormBuilder.php:230
msgid "Delete"
msgstr "删除"

#: src/Providers/Provider/Settings/FormBuilder.php:177
msgid "Remove"
msgstr "移除"

#: src/Providers/Provider/Settings/FormBuilder.php:171
#: src/Providers/Provider/Settings/FormBuilder.php:224
msgid "Add Another"
msgstr "添加另一个"

#: includes/admin/builder/class-builder.php:577
#: src/Providers/Provider/Settings/FormBuilder.php:132
#: src/Providers/Provider/Settings/FormBuilder.php:189
msgid "--- Select Field ---"
msgstr "--- 选择字段 ---"

#: src/Providers/Provider/Settings/FormBuilder.php:147
#: src/Providers/Provider/Settings/FormBuilder.php:202
msgid "Field Name"
msgstr "字段名称"

#: src/Providers/Provider/Settings/FormBuilder.php:121
msgid "Form Field Value"
msgstr "表单字段值"

#: src/Providers/Provider/Settings/FormBuilder.php:120
msgid "Custom Field Name"
msgstr "自定义字段名称"

#: src/Providers/Provider/Settings/PageIntegrations.php:262
msgid "Missing required data in payload."
msgstr "缺少有效负载中的所需数据。"

#: src/Providers/Provider/Settings/PageIntegrations.php:254
msgid "You do not have permissions."
msgstr "您没有权限。"

#: includes/providers/class-base.php:1195
msgid "Connection missing"
msgstr "连接丢失"

#: includes/providers/class-base.php:1179
#: includes/providers/class-base.php:1227
msgid "Missing data"
msgstr "缺失数据"

#: includes/providers/class-base.php:162 includes/providers/class-base.php:1171
#: includes/providers/class-base.php:1219
msgid "You do not have permission"
msgstr "您没有得到许可"

#: includes/providers/class-base.php:1339
#: src/Providers/Provider/Settings/PageIntegrations.php:160
msgid "Please fill out all of the fields below to add your new provider account."
msgstr "请填写以下所有字段以添加新的提供商帐户。"

#: includes/providers/class-base.php:749 includes/providers/class-base.php:1332
#: includes/providers/class-constant-contact.php:498
#: src/Providers/Provider/Settings/FormBuilder.php:484
#: src/Providers/Provider/Settings/PageIntegrations.php:153
msgid "Add New Account"
msgstr "添加新帐户"

#. translators: %s - provider name.
#: includes/providers/class-base.php:1276
#: src/Providers/Provider/Settings/PageIntegrations.php:148
msgid "Connect to %s"
msgstr "绑定 %s 帐号"

#: includes/providers/class-base.php:1250
#: includes/providers/class-base.php:1322
#: src/Providers/Provider/Settings/PageIntegrations.php:122
msgid "Disconnect"
msgstr "断开"

#. translators: %s - Connection date.
#: includes/providers/class-base.php:1249
#: includes/providers/class-base.php:1321
#: src/Providers/Provider/Settings/PageIntegrations.php:121
msgid "Connected on: %s"
msgstr "已连接：%s"

#: includes/providers/class-base.php:1306
#: src/Providers/Provider/Settings/PageIntegrations.php:89
msgid "Connected"
msgstr "已连接"

#. translators: %s - provider name.
#. translators: %s - addon name.
#: includes/providers/class-base.php:1303
#: lite/templates/education/admin/settings/integrations-item.php:36
#: src/Providers/Provider/Settings/PageIntegrations.php:86
msgid "Integrate %s with WPForms"
msgstr "将%s 与WPForms集成"

#: includes/providers/class-base.php:1294
#: src/Providers/Provider/Settings/PageIntegrations.php:77
msgid "Show Accounts"
msgstr "显示帐户"

#: templates/admin/challenge/welcome.php:19
msgid "Start the WPForms Challenge"
msgstr "启动 WPForms 挑战"

#: templates/admin/challenge/welcome.php:16
msgid "Create your first form with our guided setup wizard in less than 5 minutes to experience the WPForms difference."
msgstr "使用我们的指导设置向导在不到5分钟的时间内创建您的第一个表单，以体验WPForms的魅力。"

#: templates/admin/challenge/welcome.php:15
msgid "Take the WPForms Challenge"
msgstr "参加WPForms挑战"

#: templates/admin/challenge/embed.php:107
msgid "Submit Feedback"
msgstr "提交反馈"

#: templates/admin/challenge/embed.php:105
msgid "Yes, I give WPForms permission to contact me for any follow up questions."
msgstr "是的，如果有任何后续问题，我会允许WPForms与我联系。"

#. translators: %1$d - Number of minutes; %2$s - Single or plural word
#. 'minute'.
#: templates/admin/challenge/embed.php:96
msgid "We're sorry that it took longer than %1$d %2$s to create a form. Our goal is to create the most beginner friendly WordPress form plugin. Please take a moment to let us know how we can improve WPForms."
msgstr "很抱歉，创建表单所用的时间超过了%1$d %2$s。我们的目标是创建最适合初学者使用的WordPress表单插件。请花一点时间告诉我们如何改进WPForms。"

#: templates/admin/challenge/embed.php:90
msgid "Help us improve WPForms"
msgstr "帮助我们改善WPForms"

#: templates/admin/challenge/embed.php:79
msgid "Rate WPForms on WordPress.org"
msgstr "在WordPress.org上对 WPForms 进行评分"

#: templates/admin/challenge/embed.php:63
msgid "second"
msgid_plural "seconds"
msgstr[0] "秒"

#. translators: %1$s - Number of minutes in HTML container; %2$s - Single or
#. plural word 'minute'; %3$s - Number of seconds in HTML container; %4$s -
#. Single or plural word 'second'; %5$s - 5 rating star symbols HTML.
#: templates/admin/challenge/embed.php:59
msgid "You completed the WPForms Challenge in <b>%1$s %2$s %3$s %4$s</b>. Share your success story with other WPForms users and help us spread the word <b>by giving WPForms a 5-star rating (%5$s) on WordPress.org</b>. Thanks for your support and we look forward to bringing you more awesome features."
msgstr "您在<b>%1$s %2$s %3$s %4$s</b>完成了WPForms质询。与其他WPForms用户分享您的成功故事，并<b>在WordPress.org上给WPForms一个5星评级(%5$s)</b>。感谢您的支持，我们期待着能为您带来更多强大的功能。"

#: templates/admin/challenge/embed.php:53
msgid "Congrats, you did it!"
msgstr "恭喜，您做到了！"

#: templates/admin/challenge/embed.php:41
#: templates/admin/form-embed-wizard/tooltip.php:39
msgid "Click the “Add Form” button, select your form, then add the embed code."
msgstr "单击“添加表单”按钮，选择您的表单，然后添加嵌入代码。"

#: templates/admin/challenge/embed.php:18
#: templates/admin/form-embed-wizard/tooltip.php:17
msgid "Add a Block"
msgstr "添加块"

#: templates/admin/challenge/builder.php:32
msgid "The default notification settings might be sufficient, but double&#8209;check to be sure."
msgstr "默认通知设置可能已足够，但请仔细检查确认。"

#: templates/admin/challenge/builder.php:22
msgid "Build your form from scratch or use one of our pre-made templates."
msgstr "从头开始构建表单或使用我们预先制作的模板之一。"

#: templates/admin/challenge/builder.php:17
#: templates/admin/challenge/builder.php:27
#: templates/admin/challenge/builder.php:33
#: templates/admin/challenge/embed.php:43
#: templates/admin/form-embed-wizard/tooltip.php:41
msgid "Done"
msgstr "完成"

#: templates/admin/challenge/builder.php:16
msgid "Give your form a name so you can easily identify it."
msgstr "为表单指定一个名称，以便您可以轻松地识别它。"

#. translators: %s - minutes in 2:00 format.
#: templates/admin/challenge/modal.php:59
msgid "%s remaining"
msgstr "还剩%s"

#: templates/admin/challenge/modal.php:54
msgid "WPForms Challenge"
msgstr "WPForms 挑战"

#: templates/admin/challenge/modal.php:23
msgid "Cancel challenge"
msgstr "取消质询"

#: templates/admin/challenge/modal.php:22
msgid "Skip challenge"
msgstr "跳过质询"

#: templates/admin/challenge/modal.php:70
msgid "Start Challenge"
msgstr "开始挑战"

#: templates/admin/challenge/embed.php:40
#: templates/admin/challenge/modal.php:42
#: templates/admin/form-embed-wizard/popup.php:18
#: templates/admin/form-embed-wizard/tooltip.php:38
msgid "Embed in a Page"
msgstr "嵌入页面"

#: templates/admin/challenge/builder.php:31
msgid "Check Notification Settings"
msgstr "检查通知设置"

#: templates/admin/challenge/modal.php:40
msgid "Add Fields to Your Form"
msgstr "向表单中添加字段"

#: includes/admin/builder/panels/class-setup.php:117
#: src/Admin/Tools/Views/Export.php:208
#: templates/admin/challenge/builder.php:21
#: templates/admin/challenge/modal.php:39
msgid "Select a Template"
msgstr "选择一个模板"

#: includes/admin/builder/panels/class-setup.php:112
#: templates/admin/challenge/builder.php:15
#: templates/admin/challenge/modal.php:38
msgid "Name Your Form"
msgstr "命名您的表单"

#: templates/admin/challenge/embed.php:61
#: templates/admin/challenge/embed.php:98
#: templates/admin/challenge/modal.php:31
msgid "minute"
msgid_plural "minutes"
msgstr[0] "分钟"

#. translators: %1$d - Number of minutes; %2$s - Single or plural word
#. 'minute'.
#: templates/admin/challenge/modal.php:29
msgid "Complete the <b>WPForms Challenge</b> and get up and running within %1$d&nbsp;%2$s."
msgstr "完成 <b> WPForms挑战 </b>, 并在 %1$d&nbsp;%2$s 内启动并运行。"

#: src/Lite/Admin/DashboardWidget.php:370
msgid "Install"
msgstr "安装"

#: includes/admin/class-about.php:877 src/Admin/Pages/Analytics.php:413
#: src/Lite/Admin/DashboardWidget.php:368
msgid "MonsterInsights"
msgstr "MonsterInsights"

#: src/Lite/Admin/DashboardWidget.php:367
msgid "Recommended Plugin:"
msgstr "推荐插件："

#: src/Lite/Admin/DashboardWidget.php:344
msgid "Show all forms"
msgstr "显示所有表单"

#: src/Lite/Admin/DashboardWidget.php:316
msgid "No entries were submitted yet."
msgstr "尚未提交任何条目。"

#: src/Lite/Admin/DashboardWidget.php:281
msgid "Total Entries by Form"
msgstr "按表单分类列出的条目"

#: src/Lite/Admin/DashboardWidget.php:271
msgid "Go to WPForms.com"
msgstr "前往WPForms.com"

#: includes/admin/class-welcome.php:333 src/Admin/FlyoutMenu.php:112
#: src/Admin/Tools/Views/Importer.php:229
#: src/Lite/Admin/DashboardWidget.php:265
#: src/Lite/Admin/Settings/Access.php:242 templates/builder/help.php:79
#: templates/education/admin/settings/geolocation/submit.php:40
msgid "Upgrade to WPForms Pro"
msgstr "升级至WPForms专业版"

#: src/Lite/Admin/DashboardWidget.php:262
msgid "Upgrade to Pro and get access to the reports."
msgstr "升级到专业版并访问报告。"

#: src/Lite/Admin/DashboardWidget.php:261
msgid "Form entries are not stored in Lite."
msgstr "精简版不存储表单条目。"

#: src/Lite/Admin/DashboardWidget.php:260
msgid "Form entries reports are not available."
msgstr "表单条目报告不可用。"

#: src/Lite/Admin/DashboardWidget.php:259
msgid "View all Form Entries inside WordPress Dashboard"
msgstr "查看WordPress仪表板中的所有表单条目"

#: src/Lite/Admin/DashboardWidget.php:249
msgid "Last 7 days"
msgstr "最近一周"

#: src/Lite/Admin/DashboardWidget.php:246
msgid "Total Entries"
msgstr "全部条目"

#: includes/admin/builder/panels/class-fields.php:431
#: lite/templates/education/builder/did-you-know.php:27
#: src/Admin/Pages/Analytics.php:475
#: src/Integrations/UncannyAutomator/UncannyAutomator.php:174
#: src/Lite/Admin/DashboardWidget.php:227
#: src/Lite/Admin/DashboardWidget.php:372
msgid "Learn More"
msgstr "了解更多"

#: src/Lite/Admin/DashboardWidget.php:222
#: templates/admin/empty-states/no-forms.php:25
msgid "Create Your Form"
msgstr "创建表单"

#: src/Lite/Admin/DashboardWidget.php:218
#: templates/admin/empty-states/no-forms.php:18
msgid "You can use WPForms to build contact forms, surveys, payment forms, and more with just a few clicks."
msgstr "只需点击几下，您就可以使用WPForms构建联系表单、调查表单、付款表单等。"

#: src/Lite/Admin/DashboardWidget.php:217
msgid "Create Your First Form to Start Collecting Leads"
msgstr "创建第一个表单开始收集潜在顾客"

#: includes/admin/builder/class-builder.php:768
#: includes/admin/builder/class-builder.php:791
#: includes/admin/class-welcome.php:138 src/Lite/Admin/DashboardWidget.php:216
#: templates/admin/challenge/modal.php:52 templates/builder/help.php:21
msgid "Sullie the WPForms mascot"
msgstr "Sullie 是 WPForms的吉祥物"

#: includes/admin/class-menu.php:74
#: includes/admin/overview/class-overview-table.php:287
#: src/Lite/Admin/DashboardWidget.php:143 src/Logger/Log.php:96
#: templates/emails/summary-body-plain.php:32
#: templates/emails/summary-body.php:60
msgid "Entries"
msgstr "条目"

#: src/Lite/Admin/DashboardWidget.php:141
msgid "Show Less"
msgstr "显示更少"

#: src/Lite/Admin/DashboardWidget.php:140
#: src/Lite/Admin/DashboardWidget.php:345
msgid "Show More"
msgstr "显示更多"

#. translators: %s - addon name.
#. translators: %s - Addon name
#: src/Admin/Addons/Addons.php:320
#: src/Admin/Education/Builder/Geolocation.php:112
msgid "%s addon"
msgstr "%s扩展"

#: src/Admin/Education/Fields.php:236
msgid "Total"
msgstr "合计"

#: src/Admin/Education/Fields.php:201
msgid "Dropdown Items"
msgstr "下拉项"

#: src/Admin/Education/Fields.php:193
msgid "Checkbox Items"
msgstr "复选框项目"

#: src/Admin/Education/Fields.php:185
msgid "Multiple Items"
msgstr "多项"

#: src/Admin/Education/Fields.php:177
msgid "Single Item"
msgstr "单项"

#: src/Admin/Education/Fields.php:168
msgid "Net Promoter Score"
msgstr "净推荐人得分"

#: src/Admin/Education/Fields.php:159
msgid "Likert Scale"
msgstr " 好感度"

#: src/Admin/Education/Fields.php:150
msgid "Signature"
msgstr "签名"

#: includes/admin/builder/panels/class-fields.php:140
#: includes/class-frontend.php:914 includes/class-process.php:202
#: src/Admin/Education/Builder/Captcha.php:123
#: src/Admin/Education/Fields.php:269 src/Admin/Settings/Captcha.php:136
msgid "hCaptcha"
msgstr "hCaptcha"

#: src/Admin/Education/Fields.php:133
msgid "Rating"
msgstr "评分"

#: src/Admin/Education/Fields.php:101
msgid "Section Divider"
msgstr "部分分割器"

#: includes/emails/class-emails.php:549 src/Admin/Education/Fields.php:93
msgid "Page Break"
msgstr "分页符"

#: src/Admin/Education/Fields.php:77
msgid "File Upload"
msgstr "文件上传"

#: src/Admin/Education/Fields.php:125
msgid "HTML"
msgstr "编辑 HTML 源码"

#: src/Admin/Education/Fields.php:117
msgid "Hidden Field"
msgstr "隐藏字段"

#: src/Admin/Education/Fields.php:61
msgid "Date / Time"
msgstr "日期 / 时间"

#: src/Admin/Education/Fields.php:45
msgid "Phone"
msgstr "电话"

#: src/Admin/Education/Fields.php:85
msgid "Password"
msgstr "密码"

#: src/Admin/Education/Fields.php:53
msgid "Address"
msgstr "地址"

#: src/Admin/Education/Fields.php:69
msgid "Website / URL"
msgstr "网站 / URL"

#: includes/admin/builder/panels/class-payments.php:79
#: includes/admin/class-welcome.php:261 src/Admin/Education/Core.php:117
msgid "Upgrade to PRO"
msgstr "升级到PRO"

#: src/Admin/Education/Core.php:116 src/Admin/Education/Core.php:125
msgid "Already purchased?"
msgstr "已经购买？"

#: src/Admin/Education/Core.php:135
msgid "<strong>Bonus:</strong> WPForms Lite users get <span>50% off</span> regular price, automatically applied at checkout."
msgstr "<strong>奖励</strong>：WPForms Lite用户可获得<span> 50％折扣</span>正常价格，并在结帐时自动应用。"

#: src/Admin/Education/Core.php:115
msgid "We're sorry, the %name% is not available on your plan. Please upgrade to the PRO plan to unlock all these awesome features."
msgstr "很抱歉，您的计划中没有%name%。请升级到专业版以解锁所有这些非常棒的功能。"

#: src/Admin/Education/Core.php:114
msgid "is a PRO Feature"
msgstr "是PRO功能"

#. Author of the plugin
#: includes/admin/class-menu.php:41 includes/admin/class-menu.php:42
#: includes/admin/class-menu.php:53 includes/admin/class-menu.php:111
#: includes/class-form.php:71 includes/integrations.php:36
#: src/Emails/Templates/General.php:93 src/Integrations/Divi/Divi.php:207
#: src/Integrations/Divi/WPFormsSelector.php:35
#: src/Integrations/Elementor/Widget.php:41
#: src/Integrations/Gutenberg/FormSelector.php:91
#: src/Lite/Admin/DashboardWidget.php:162
msgid "WPForms"
msgstr "WPForms"

#. Description of the plugin
msgid "Beginner friendly WordPress contact form plugin. Use our Drag & Drop form builder to create your WordPress forms."
msgstr "对新手友好的WordPress联系表单插件，使用拖拽式表单构建器来创建您的WordPress表单。"

#. Plugin URI of the plugin
#. Author URI of the plugin
msgid "https://wpforms.com"
msgstr "https://wpforms.com"

#. Plugin Name of the plugin
msgid "WPForms Lite"
msgstr "WPForms Lite"