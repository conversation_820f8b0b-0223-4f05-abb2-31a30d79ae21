{"translation-revision-date": "2025-07-21 17:33:10+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "zh_CN"}, "Enable customers to filter the product collection by selecting one or more %s attributes.": ["支持客户通过选择一个或多个 %s 属性来筛选产品集合。"], "Empty filter options": ["清空筛选器选项"], "Product counts": ["产品数量"], "Show results for any of the attributes selected (displayed products don’t have to have them all).": ["显示任何选定属性的结果（显示的产品不一定要包含所有属性）。"], "Show results for <b>all</b> selected attributes. Displayed products must contain <b>all of them</b> to appear in the results.": ["显示<b>所有</b>选定属性的结果。 显示的产品必须包含<b>所有这些属性</b>才会出现在结果中。"], "Logic": ["逻辑"], "Determine the order of filter options.": ["确定筛选器选项的顺序。"], "Sort order": ["排序"], "Choose the attribute to show in this filter.": ["选择要在该筛选器中显示的属性。"], "Least results first": ["出现次数最少的结果优先"], "Most results first": ["出现次数最多的结果优先"], "Name, Z to A": ["名称，Z 到 A"], "Name, A to Z": ["名称，A 到 Z"], "Red": ["红色"], "Green": ["绿色"], "Gray": ["灰色"], "There are no products with the selected attributes.": ["没有找到具有所选属性的产品。"], "Please select an attribute to use this filter!": ["请选择一个属性，以便使用此过滤器！"], "Select an option": ["选择一个选项"], "Yellow": ["黄色"], "Blue": ["蓝色"], "Attributes are needed for filtering your products. You haven't created any attributes yet.": ["需要选择属性才能筛选您的产品。您尚未创建任何属性。"], "Attribute": ["属性"], "Display": ["显示"], "Any": ["任何"], "All": ["所有"]}}, "comment": {"reference": "assets/client/blocks/product-filter-attribute.js"}}