{"translation-revision-date": "2025-07-21 17:33:10+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "zh_CN"}, "Manual (menu order + name)": ["手动（菜单顺序 + 名称）"], "Set the products order in this collection.": ["设置该集合中的产品顺序。"], "All Product Collection blocks using the Default Query will sync to this sort order.": ["使用默认查询的所有产品集合区块都将同步设置为此排列顺序。"], "Default sort by": ["默认排序方式"], "🙏🏻 Thanks for sharing — we're on it!": ["🙏🏻 感谢分享 — 我们正在处理！"], "Share if you would like to discuss your experience or participate in future research.": ["如果您想讨论您的体验或参与未来的研究，请尽情分享。"], "Email address (Optional)": ["电子邮件地址（可选）"], "What did you try to build using this block? What did and didn't work?": ["您尝试用此区块构建了什么？ 哪些有效？哪些无效？"], "How can we improve the %s block for you? (Optional)": ["我们如何为您优化 %s 区块？ （可选）"], "It was easy for me to accomplish what I wanted with the %s.": ["通过 %s，我可以轻松实现自己的目标。"], "Share your experience": ["分享您的体验"], "Products per page": ["每页产品数"], "Reload full page": ["重新加载整个页面"], "Browsing between pages requires a full page reload.": ["在不同页面之间浏览需要重新载入整个页面。"], "Related by": ["相关内容"], "Specify a fixed width.": ["指定一个固定宽度。"], "Stretch to fill available space.": ["拉伸以填充可用空间。"], "From a specific product": ["来自特定产品"], "Products to show": ["要显示的产品"], "From the current product": ["来自当前产品"], "From products in the order": ["来自订单中的产品"], "From products in the cart": ["来自购物车中的产品"], "Select a product to pull the linked products from": ["选择一个产品，以提取其关联产品"], "Linked products will be pulled from the product a shopper is currently viewing": ["关联产品将从购物者当前查看的产品中提取"], "Search for products to display…": ["搜索要显示的产品…"], "Hand-Picked": ["精选"], "Rating, low to high": ["评级，从低到高"], "Rating, high to low": ["评级，从高到低"], "Sales, low to high": ["销售额，从低到高"], "Sales, high to low": ["销售额，从高到低"], "Price, low to high": ["价格，从低到高"], "Price, high to low": ["价格，从高到低"], "Show a list of products based on fixed criteria.": ["根据固定标准显示产品列表。"], "Show products based on specific criteria and allow shoppers to filter.": ["根据特定标准显示产品，并允许购物者进行筛选。"], "Fixed": ["固定"], "Display products based on the current template and allow shoppers to filter.": ["根据当前模板显示产品，并允许购物者进行筛选。"], "You may also like": ["您可能还喜欢"], "Upsells are typically products that are extra profitable or better quality or more expensive. Experiment with combinations to boost sales.": ["追加销售产品通常是指利润更高、质量更好或价格更贵的产品。 尝试各种组合来促进销售。"], "Recommend products like this one.": ["推荐类似的产品。"], "Recommended products": ["推荐产品"], "Select specific products to recommend to customers.": ["选择特定产品向客户推荐。"], "Featured Products": ["特色产品"], "By suggesting complementary products in the cart using cross-sells, you can significantly increase the average order value.": ["通过利用交叉销售在购物车中推荐互补性产品，您可以显著提高平均订单价值。"], "Cross-Sells": ["交叉销售"], "Related Products": ["相关产品"], "<strong>%s</strong> requires a product to be selected in order to display associated items.": ["<strong>%s</strong> 要求选择产品才能显示相关项目。"], "Previously selected product is no longer available.": ["之前选择的产品已不再可用。"], "Linked Product": ["关联产品"], "Actual products will vary depending on the product being viewed.": ["实际产品会因查看产品的不同而有所差异。"], "What products do you want to show?": ["您想展示哪些产品？"], "create your own": ["自行创建"], "or": ["或"], "Actual products will vary depending on the page being viewed.": ["实际的产品会因浏览页面的不同而有所差异。"], "Missing": ["缺少"], "Reload full page can't be disabled because there are incompatible blocks inside the Product Collection block.": ["无法禁用“重新载入整个页面”选项，因为“产品集合”区块内有不兼容的区块。"], "Enable to enforce full page reload on certain interactions, like using paginations controls.": ["启用以在进行某些交互（如使用分页控件）时强制重新加载整个页面。"], "Choose collection": ["选择产品集合"], "MAX": ["最高"], "MIN": ["最低"], "PRICE RANGE": ["价格范围"], "Auto": ["自动"], "Showcase your featured products.": ["展示您的特色产品。"], "On sale products": ["正在促销的产品"], "Highlight products that are currently on sale.": ["突出显示当前正在促销的产品。"], "Recommend your best-selling products.": ["推荐您的畅销产品。"], "Recommend products with the highest review ratings.": ["推荐评级最高的产品。"], "New arrivals": ["新品"], "Recommend your newest products.": ["推荐您的最新产品。"], "New Arrivals": ["新品"], "Automatically adjust the number of columns to better fit smaller screens.": ["自动调整列数，以更好地适应较小的屏幕。"], "Responsive": ["响应式"], "Product Collection query operator\u0004Before": ["之前"], "Product Collection query operator\u0004Within": ["期间"], "Show only featured products": ["仅显示特色产品"], "Grid": ["网格"], "Stack": ["堆叠"], "Display products in a single column.": ["以单列显示产品。"], "Display products using rows and columns.": ["使用行和列来显示产品。"], "Grid view": ["网格视图"], "List view": ["列表视图"], "Revert to Products (Beta)": ["恢复为“产品”（测试版）"], "Product Collection": ["产品集合"], "Products (Beta) block was upgraded to <strongText />, an updated version with new features and simplified settings.": ["“产品”（测试版）区块已升级为 <strongText />，这是增加了新功能且简化了设置的更新版本。"], "Select a product": ["选择一个产品"], "Max pages to show": ["可显示的最大页面数"], "Limit the pages you want to show, even if the query has more results. To show all pages use 0 (zero).": ["限制显示的页面数，即便无法展示所有查询结果。 如需显示所有页面，请选择 0（零）。"], "Offset": ["偏移"], "Keyword": ["关键字"], "Oldest to newest": ["最旧到最新"], "Newest to oldest": ["最新到最旧"], "Z → A": ["标题降序排列"], "A → Z": ["标题升序排列"], "Please enter a valid email address.": ["请输入有效的电子邮箱地址。"], "Fill": ["填充"], "Manage attributes": ["管理属性"], "Show only products on sale": ["仅显示促销产品"], "You may be interested in…": ["您可能对此感兴趣…"], "An error has prevented the block from being updated.": ["发生错误，区块未能成功更新。"], "Stock Status": ["库存状态"], "%1$s, has %2$d variation": ["%1$s，具有 %2$d 个变体"], "%1$d variations": ["%1$d 个变体"], "%d term": ["%d 个字词"], "Help us improve": ["帮助我们改进"], "%1$s, has %2$d product": ["%1$s，有%2$d产品"], "%1$s, has %2$d term": ["%1$s，有%2$d项"], "Loading…": ["载入中…"], "Hand-Picked Products": ["精选产品"], "Select product": ["选择产品"], "Created": ["已创建"], "Best Sellers": ["最佳销售商"], "On Sale": ["打折"], "Price Range": ["价格范围"], "Product Summary": ["产品摘要"], "Display a short description about a product.": ["显示有关产品的简短描述。"], "The following error was returned": ["系统返回了以下错误"], "The following error was returned from the API": ["API 返回以下错误"], "Filters": ["过滤器"], "Search results updated.": ["搜索结果已更新。"], "%d item selected": ["已选择 %d项"], "Search for items": ["搜索项"], "No results for %s": ["无 %s的相关结果"], "No items found.": ["未找到项。"], "Clear all selected items": ["清除所有选定项"], "Clear all": ["全部清除"], "Remove %s": ["移除 %s"], "All selected attributes": ["所有选定属性"], "Any selected attributes": ["任何选定属性"], "Pick at least two attributes to use this setting.": ["选择至少两个属性以使用此设置。"], "Product attribute search results updated.": ["已更新产品属性搜索结果。"], "%d attribute selected": ["已选择 %d个属性"], "Search for product attributes": ["搜索产品属性"], "Your store doesn't have any product attributes.": ["您的商店没有任何产品属性。"], "Clear all product attributes": ["清除所有产品属性"], "Top Rated Products": ["评分最高的产品"], "Product search results updated.": ["产品搜索结果已更新。"], "Search for a product to display": ["搜索要显示的产品"], "Your store doesn't have any products.": ["您的商店没有任何产品。"], "Layout": ["布局"], "Columns": ["列"], "On Sale Products": ["促销产品"], "Best selling products": ["最畅销产品"], "Display products matching": ["显示产品匹配"], "Product Catalog": ["产品目录"], "Product Attributes": ["产品属性"], "Query type": ["查询类型"], "Random": ["随机"], "Top rated products": ["好评的产品"], "Featured products": ["推荐产品"], "%d product": ["%d 个产品"], "Custom": ["自定义"], "Width": ["宽度"], "Upsells": ["交叉销售"], "Products": ["产品"], "Categories": ["分类"], "Tags": ["标签"], "Cancel": ["取消"], "Continue": ["继续"], "Featured": ["精选"], "Attributes": ["属性"], "Settings": ["设置"], "Default": ["默认"], "Order by": ["排序方式"]}}, "comment": {"reference": "assets/client/blocks/product-collection.js"}}