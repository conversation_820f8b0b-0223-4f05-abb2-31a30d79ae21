{"translation-revision-date": "2024-10-02 16:14:23+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "Enable customers to filter the product collection by selecting one or more %s attributes.": ["Enable customers to filter the product collection by selecting one or more %s attributes."], "Clear button": ["Clear button"], "Empty filter options": ["Empty filter options"], "Product counts": ["Product counts"], "Logic": ["Logic"], "Determine the order of filter options.": ["Determine the order of filter options."], "Sort order": ["Sort order"], "Choose the attribute to show in this filter.": ["Choose the attribute to show in this filter."], "Least results first": ["Least results first"], "Most results first": ["Most results first"], "Name, Z to A": ["Name, Z to A"], "Name, A to Z": ["Name, A to Z"], "Red": ["Red"], "Green": ["Green"], "There are no products with the selected attributes.": ["There are no products with the selected attributes."], "Please select an attribute to use this filter!": ["Please select an attribute to use this filter!"], "Select an option": ["Select an option"], "Yellow": ["Yellow"], "Blue": ["Blue"], "Attributes are needed for filtering your products. You haven't created any attributes yet.": ["Attributes are needed for filtering your products. You haven't created any attributes yet."], "Attribute": ["Attribute"], "Display": ["Display"], "Header": ["Header"], "Any": ["Any"], "All": ["All"], "Settings": ["Settings"]}}, "comment": {"reference": "assets/client/blocks/product-filter-attribute.js"}}