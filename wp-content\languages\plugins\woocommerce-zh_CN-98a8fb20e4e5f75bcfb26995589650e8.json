{"translation-revision-date": "2025-07-21 17:33:10+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "zh_CN"}, "Products in cart": ["购物车中的产品"], "(%d item)": ["（%d 件商品）"], "Flat rate shipping": ["统一运费"], "Remove %s from cart": ["从购物车中删除 %s"], "Increase quantity of %s": ["增加 %s 的数量"], "Reduce quantity of %s": ["减少 %s 的数量"], "T-Shirt": ["T 恤"], "Hoodie with Pocket": ["带口袋连帽衫"], "Hoodie with Logo": ["带徽标连帽衫"], "Hoodie with Zipper": ["带拉链连帽衫"], "Long Sleeve Tee": ["长袖 T 恤"], "Polo": ["马球衫"], "Start shopping": ["开始购物"], "Filled Mini-Cart": ["已加到迷你购物车"], "Empty Mini-Cart": ["空的迷你购物车"], "Mini-Cart Contents": ["迷你购物车内容"], "Go to checkout": ["前往结账"], "View my cart": ["查看我的购物车"], "Shipping, taxes, and discounts calculated at checkout.": ["结账时计算运费、税费以及折扣。"], "Your cart": ["您的购物车"], "%s has been removed from your cart.": ["已将 %s 从您的购物车中移除。"], "Display a Mini-Cart widget.": ["显示一个迷你购物车小工具。"], "Price between %1$s and %2$s": ["价格在 %1$s 和 %2$s 之间"], "%s (optional)": ["%s （可选）"], "Remove item": ["删除项目"], "Details": ["详细资料"], "Orange": ["橙色"], "Lightweight baseball cap": ["轻便棒球帽"], "Yellow": ["黄色"], "Warm hat for winter": ["冬季保暖帽"], "Beanie": ["无檐小便帽"], "example product in Cart Block\u0004Beanie": ["无檐小便帽"], "example product in Cart Block\u0004Beanie with Logo": ["无檐小便帽（带标志）"], "Quantity increased to %s.": ["数量增加到 %s。"], "Quantity reduced to %s.": ["数量减少至 %s。"], "Quantity of %s in your cart.": ["购物车中的 %s 数量。"], "Cap": ["帽子"], "%d left in stock": ["%d 库存剩余"], "Discounted price:": ["折扣价格："], "Previous price:": ["先前价格："], "Sales tax": ["销售税"], "Color": ["颜色"], "Small": ["小"], "Size": ["尺寸"], "Free shipping": ["免费配送"], "Subtotal": ["小计"], "Dimensions": ["尺寸"], "Shipping": ["配送"], "Save %s": ["节省 %s"], "Total": ["合计"], "Fee": ["费用"], "Product": ["产品"], "Available on backorder": ["可超售"], "Local pickup": ["本地自提"], "WooCommerce": ["WooCommerce"]}}, "comment": {"reference": "assets/client/blocks/mini-cart-contents.js"}}