{"translation-revision-date": "2024-10-02 16:14:23+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "Actual products will vary depending on the product being viewed.": ["Actual products will vary depending on the product being viewed."], "What products do you want to show?": ["What products do you want to show?"], "create your own": ["create your own"], "or": ["or"], "Actual products will vary depending on the page being viewed.": ["Actual products will vary depending on the page being viewed."], "Missing": ["Missing"], "Choose collection": ["Choose collection"], "MAX": ["MAX"], "MIN": ["MIN"], "PRICE RANGE": ["PRICE RANGE"], "Auto": ["Auto"], "Showcase your featured products.": ["Showcase your featured products."], "On sale products": ["On sale products"], "Highlight products that are currently on sale.": ["Highlight products that are currently on sale."], "Recommend your best-selling products.": ["Recommend your best-selling products."], "Recommend products with the highest review ratings.": ["Recommend products with the highest review ratings."], "New arrivals": ["New arrivals"], "Recommend your newest products.": ["Recommend your newest products."], "New Arrivals": ["New arrivals"], "An error has prevented the block from being updated.": ["An error has prevented the block from being updated."], "Stock Status": ["Stock Status"], "%1$s, has %2$d variation": ["%1$s, has %2$d variation", "%1$s, has %2$d variations"], "%1$d variations": ["%1$d variations"], "%d term": ["%d term", "%d terms"], "%1$s, has %2$d term": ["%1$s, has %2$d term", "%1$s, has %2$d terms"], "%1$s, has %2$d product": ["%1$s, has %2$d product", "%1$s, has %2$d products"], "Give us your feedback.": ["Give us your feedback."], "Feedback?": ["Feedback?"], "Loading…": ["Loading…"], "Select product": ["Select product"], "Created": ["Created"], "Best Sellers": ["Best Sellers"], "On Sale": ["On Sale"], "Price Range": ["Price Range"], "Product Summary": ["Product summary"], "Display the title of a product.": ["Display the title of a product."], "Display a short description about a product.": ["Display a short description about a product."], "The following error was returned": ["The following error was returned"], "The following error was returned from the API": ["The following error was returned from the API"], "Search results updated.": ["Search results updated."], "Filters": ["Filters"], "%d item selected": ["%d item selected", "%d items selected"], "Search for items": ["Search for items"], "No results for %s": ["No results for %s"], "No items found.": ["No items found."], "Clear all selected items": ["Clear all selected items"], "Clear all": ["Clear all"], "Remove %s": ["Remove %s"], "%d attribute selected": ["%d attribute selected", "%d attributes selected"], "All selected attributes": ["All selected attributes"], "Any selected attributes": ["Any selected attributes"], "Clear all product attributes": ["Clear all product attributes"], "Columns": ["Columns"], "Layout": ["Layout"], "Pick at least two attributes to use this setting.": ["Pick at least two attributes to use this setting."], "Product attribute search results updated.": ["Product attribute search results updated."], "Product search results updated.": ["Product search results updated."], "Search for a product to display": ["Search for a product to display"], "Search for product attributes": ["Search for product attributes"], "Your store doesn't have any product attributes.": ["Your store doesn't have any product attributes."], "Your store doesn't have any products.": ["Your store doesn't have any products."], "On Sale Products": ["On Sale Products"], "Top Rated Products": ["Top Rated Products"], "Best selling products": ["Best selling products"], "Display products matching": ["Display products matching"], "Product Catalog": ["Product Catalogue"], "Product Title": ["Product Title"], "Product Attributes": ["Product Attributes"], "Query type": ["Query type"], "Random": ["Random"], "Top rated products": ["Top rated products"], "Featured products": ["Featured products"], "%d product": ["%d product", "%d products"], "Custom": ["Custom"], "Dimensions": ["Dimensions"], "Width": ["<PERSON><PERSON><PERSON>"], "Upsells": ["Upsells"], "Products": ["Products"], "Categories": ["Categories"], "Tags": ["Tags"], "Cancel": ["Cancel"], "Continue": ["Continue"], "Featured": ["Featured"], "Attributes": ["Attributes"], "Order by": ["Order by"], "Settings": ["Settings"], "Default": ["Standard"]}}, "comment": {"reference": "assets/client/blocks/product-collection.js"}}