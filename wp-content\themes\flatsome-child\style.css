/*
Theme Name:   Flatsome Child
Description:  Child theme of Flatsome
Author:       Your Name
Template:     flatsome
Version:      1.0.0
*/

/* Import parent theme styles */
@import url("../flatsome/style.css");

/* Custom CSS goes here */

/* Performance optimizations */
.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

.lazy.loaded {
    opacity: 1;
}

/* Image optimization */
img {
    height: auto;
    max-width: 100%;
}

/* Reduce layout shift */
.woocommerce-placeholder {
    background-color: #f8f8f8;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}