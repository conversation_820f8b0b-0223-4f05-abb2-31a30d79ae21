{"translation-revision-date": "2025-07-21 17:33:10+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "zh_CN"}, "The quantity of \"%1$s\" was changed to %2$d.": ["“%1$s”的数量已更改为 %2$d。"], "\"%s\" was removed from your cart.": ["已将“%s”从您的购物车中移除。"], "Flat rate shipping": ["统一运费"], "T-Shirt": ["T 恤"], "Hoodie with Pocket": ["带口袋连帽衫"], "Hoodie with Logo": ["带徽标连帽衫"], "Hoodie with Zipper": ["带拉链连帽衫"], "Long Sleeve Tee": ["长袖 T 恤"], "Polo": ["马球衫"], "%s (optional)": ["%s （可选）"], "There was an error registering the payment method with id '%s': ": ["注册 ID 为“%s”的付款方式时出错："], "Orange": ["橙色"], "Lightweight baseball cap": ["轻便棒球帽"], "Yellow": ["黄色"], "Warm hat for winter": ["冬季保暖帽"], "Beanie": ["无檐小便帽"], "example product in Cart Block\u0004Beanie": ["无檐小便帽"], "example product in Cart Block\u0004Beanie with Logo": ["无檐小便帽（带标志）"], "Cap": ["帽子"], "Something went wrong. Please contact us to get assistance.": ["出错了。 请联系我们以获取帮助。"], "The response is not a valid JSON response.": ["该响应不是有效的 JSON 响应。"], "Unable to get cart data from the API.": ["无法从 API 获取购物车数据。"], "Sales tax": ["销售税"], "Color": ["颜色"], "Small": ["小"], "Size": ["尺寸"], "Free shipping": ["免费配送"], "Shipping": ["配送"], "Fee": ["费用"], "Local pickup": ["本地自提"]}}, "comment": {"reference": "assets/client/blocks/wc-blocks-data.js"}}