{"translation-revision-date": "2025-07-21 17:33:10+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "zh_CN"}, "Add your full product description here. Describe your product in detail.": ["在此处添加您的完整产品描述。 详细描述您的产品。"], "Keep editing": ["继续编辑"], "Good work! Now you can publish your product to your store by hitting the “Publish” button or keep editing it.": ["做得好！ 现在，您可以点击“发布”按钮以在商店中发布产品，也可以继续编辑。"], "Publish your product 🎉": ["发布您的产品 🎉"], "Add your product categories here. Assign categories to your products to make them easier to browse through and find in your store.": ["在此处添加您的分类。 为您的产品指定分类，以便在商店中轻松浏览并找到这些产品。"], "Add your product categories": ["添加您的产品分类"], "Add your product tags here. Tags are a method of labeling your products to make them easier for customers to find. For example, if you sell clothing, and you have a lot of cat prints, you could make a tag for “cat.”": ["在此处添加您的产品标签。 标签是一种标记产品的方法，使客户可以更容易找到产品。 例如，假设您卖衣服，并且有很多猫的印花，那么您可以设置一个“猫”标签。"], "Add your product tags": ["添加您的产品标签"], "Upload an image to your product here. Ideally a JPEG or PNG about 600 px wide or bigger. This image will be shown in your store’s catalog.": ["在此处为您的产品上传图像。 理想的图像格式为 JPEG 或 PNG，大约 600 像素宽或更大。 这张图像将展示在您商店的目录中。"], "Add your product image": ["添加您的产品图像"], "Type a quick summary for your product here. This will appear on the product page right under the product name.": ["在此处输入产品的简要介绍。 这将显示在产品名称正下方的产品页面上。"], "Add your short product description": ["添加您的产品简述"], "Use the tabs to switch between sections and insert product details. Start by adding your product price.": ["使用选项卡在各部分之间进行切换，并插入产品详细信息。 首先添加您的产品价格。"], "Add your product data": ["添加您的产品数据"], "Add your product description": ["添加您的产品描述"], "Start typing your new product name here. This will be what your customers will see in your store.": ["在此处开始输入您的新产品名称。 这将是客户在您的商店中看到的内容。"], "Product name": ["产品名称"]}}, "comment": {"reference": "assets/client/admin/wp-admin-scripts/product-tour.js"}}